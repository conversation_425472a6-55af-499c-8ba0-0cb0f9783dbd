"use client";

import { useRef } from "react";
import Image from "next/image";
import { Upload, X, Loader2, ImageIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

export interface MediaUploadProps {
  previewUrl: string | null;
  isUploading: boolean;
  uploadError: string | null;
  onFileSelect: (_file: File | null) => void;
  onClearImage: () => void;
  disabled?: boolean;
  className?: string;
}

export default function MediaUpload({
  previewUrl,
  isUploading,
  uploadError,
  onFileSelect,
  onClearImage,
  disabled = false,
  className,
}: MediaUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null;
    onFileSelect(file);
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (disabled || isUploading) return;

    const droppedFile = event.dataTransfer.files?.[0] || null;
    if (droppedFile && droppedFile.type.startsWith("image/")) {
      onFileSelect(droppedFile);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const openFileDialog = () => {
    if (disabled || isUploading) return;
    fileInputRef.current?.click();
  };

  const handleRemoveImage = () => {
    onClearImage();
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className={cn("space-y-2", className)}>
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/webp,image/gif,.jpg,.jpeg,.png,.webp,.gif"
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled || isUploading}
      />

      {/* Upload area or preview */}
      {previewUrl ? (
        // Image preview
        <Card className="relative overflow-hidden">
          <CardContent className="p-0">
            <div className="relative w-full max-w-md mx-auto">
              <Image
                src={previewUrl}
                alt="Post image preview"
                width={400}
                height={300}
                className="object-contain rounded-lg w-full h-auto"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />

              {/* Remove button */}
              <Button
                type="button"
                variant="destructive"
                size="icon"
                className="absolute top-2 right-2 h-8 w-8"
                onClick={handleRemoveImage}
                disabled={disabled || isUploading}
              >
                <X className="h-4 w-4" />
              </Button>

              {/* Upload overlay */}
              {isUploading && (
                <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-lg">
                  <div className="text-white text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                    <p className="text-sm">Uploading...</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        // Upload area
        <Card
          className={cn(
            "border-2 border-dashed transition-colors cursor-pointer",
            "hover:border-primary/50 hover:bg-muted/50",
            disabled || isUploading ? "opacity-50 cursor-not-allowed" : "",
            uploadError ? "border-destructive" : "border-muted-foreground/25"
          )}
          onClick={openFileDialog}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          <CardContent className="flex flex-col items-center justify-center p-8 text-center">
            {isUploading ? (
              <>
                <Loader2 className="h-12 w-12 animate-spin text-muted-foreground mb-4" />
                <p className="text-sm text-muted-foreground">Processing image...</p>
              </>
            ) : (
              <>
                <div className="rounded-full bg-muted p-4 mb-4">
                  {uploadError ? (
                    <ImageIcon className="h-8 w-8 text-destructive" />
                  ) : (
                    <Upload className="h-8 w-8 text-muted-foreground" />
                  )}
                </div>

                <div className="space-y-2">
                  <p className="text-sm font-medium">
                    {uploadError ? "Upload failed" : "Upload an image"}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Drag and drop or click to select
                  </p>
                  <p className="text-xs text-muted-foreground">
                    JPEG, PNG, WebP, GIF • Max 15MB
                  </p>
                  <p className="text-xs text-muted-foreground/70">
                    Recommended: Square images for best display
                  </p>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      )}

      {/* Error message */}
      {uploadError && (
        <p className="text-sm text-destructive">{uploadError}</p>
      )}

      {/* Replace image button when preview exists */}
      {previewUrl && !isUploading && (
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={openFileDialog}
          disabled={disabled}
          className="w-full"
        >
          <Upload className="h-4 w-4 mr-2" />
          Replace Image
        </Button>
      )}
    </div>
  );
}
