"use client";

import React, { useRef } from "react";
import { motion, useInView } from "framer-motion";
import { LucideIcon } from "lucide-react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { getCategories } from "@/lib/config/categories";
import Link from "next/link";

interface CategoryCarouselProps {
  maxCategories?: number;
  className?: string;
  onCategorySelect?: (_category: string) => void;
}

export default function CategoryCarousel({
  maxCategories = 15,
  onCategorySelect
}: CategoryCarouselProps) {
  const carouselRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(carouselRef, { once: false, amount: 0.2 });

  // Get all categories from the shared config
  const categories = getCategories(maxCategories);

  const handleCategoryClick = (category: string) => {
    if (onCategorySelect) {
      onCategorySelect(category);
    }
  };

  return (
    <motion.div
      ref={carouselRef}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className="w-full relative py-6 bg-neutral-50/50 dark:bg-neutral-900/20 border-y border-neutral-200/50 dark:border-neutral-800/50"
    >
      <div className="container mx-auto px-4 mb-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
          <div className="mb-2 md:mb-0">
            <motion.h2
              className="text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-1"
              initial={{ opacity: 0, y: 10 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.5 }}
            >
              Popular Categories
            </motion.h2>
            <motion.p
              className="text-sm text-neutral-600 dark:text-neutral-400"
              initial={{ opacity: 0, y: 10 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Explore businesses and products/services by category
            </motion.p>
          </div>
          <motion.div
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : {}}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="flex items-center gap-2"
          >
            <div className="hidden md:flex items-center gap-1.5">
              <div className="w-8 h-1 rounded-full bg-neutral-300 dark:bg-neutral-700"></div>
              <div className="w-3 h-1 rounded-full bg-neutral-200 dark:bg-neutral-800"></div>
              <div className="w-2 h-1 rounded-full bg-neutral-200 dark:bg-neutral-800"></div>
            </div>
            <p className="text-sm text-neutral-500 dark:text-neutral-400 flex items-center gap-1.5">
              <span className="hidden md:inline">Drag to scroll</span>
              <span className="md:hidden">Swipe to explore</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="animate-pulse">
                <path d="M5 12h14"></path>
                <path d="m12 5 7 7-7 7"></path>
              </svg>
            </p>
          </motion.div>
        </div>
      </div>

      <Carousel
        opts={{
          align: "start",
          loop: true,
          dragFree: true,
        }}
        className="w-full"
      >
        <CarouselContent className="-ml-2 md:-ml-4">
          {categories.map((category, index) => (
            <CarouselItem
              key={index}
              className="pl-2 md:pl-4 basis-1/3 sm:basis-1/4 md:basis-1/5 lg:basis-1/6 py-2"
            >
              <CategoryCard
                name={category.name}
                icon={category.icon}
                index={index}
                onClick={() => handleCategoryClick(category.name)}
              />
            </CarouselItem>
          ))}
        </CarouselContent>

        <div className="hidden sm:block">
          <CarouselPrevious className="left-2 bg-white/90 dark:bg-neutral-900/90 border-neutral-200 dark:border-neutral-800 shadow-md hover:bg-white dark:hover:bg-neutral-800" />
          <CarouselNext className="right-2 bg-white/90 dark:bg-neutral-900/90 border-neutral-200 dark:border-neutral-800 shadow-md hover:bg-white dark:hover:bg-neutral-800" />
        </div>
      </Carousel>
    </motion.div>
  );
}

interface CategoryCardProps {
  name: string;
  icon: LucideIcon;
  index: number;
  onClick: () => void;
}

function CategoryCard({ name, icon: Icon, index, onClick }: CategoryCardProps) {
  // Find the slug for the category to create the link
  const slug = name.toLowerCase().replace(/\s+/g, '-').replace(/&/g, 'and');

  return (
    <motion.div
      className="group cursor-pointer p-1.5"
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.97 }}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
    >
      <Link href={`/categories/${slug}`} onClick={(_e) => {
        // Allow the onClick handler to work alongside the link
        onClick();
        // Don't prevent default - let the link work
      }}>
        <div className="flex flex-col items-center gap-2 p-3 rounded-xl bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 transition-all duration-300 hover:border-[var(--brand-gold)]/30 dark:hover:border-[var(--brand-gold)]/30 shadow-sm hover:shadow-md h-full">
          <div className="relative">
            {/* Background glow effect */}
            <div className="absolute inset-0 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-full blur-md opacity-30 group-hover:opacity-80 transition-opacity duration-300 scale-150"></div>

            {/* Icon container */}
            <div className="relative z-10 p-2.5 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-full">
              <Icon className="h-5 w-5 text-[var(--brand-gold)]" />
            </div>
          </div>

          {/* Category name */}
          <span className="text-xs font-medium text-neutral-700 dark:text-neutral-300 text-center">
            {name}
          </span>
        </div>
      </Link>
    </motion.div>
  );
}
