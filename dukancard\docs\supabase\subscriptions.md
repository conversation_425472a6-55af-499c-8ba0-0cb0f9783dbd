# Subscriptions Table Documentation

## Table Overview

The `subscriptions` table in the Dukancard application tracks user subscriptions to business profiles. It records when a user subscribes to or follows a business, enabling notification features and follower metrics for businesses.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key for the subscription record |
| user_id | uuid | NO | | Foreign key to users.id, the user who subscribed to the business |
| business_profile_id | uuid | NO | | Foreign key to business_profiles.id, the business that was subscribed to |
| created_at | timestamptz | NO | timezone('utc'::text, now()) | Timestamp when the subscription was created |

## Constraints

### Primary Key
- `subscriptions_pkey` - Primary key constraint on the `id` column

### Foreign Keys
- `subscriptions_user_id_fkey` - Foreign key constraint linking `user_id` to `users.id`
- `subscriptions_business_profile_id_fkey` - Foreign key constraint linking `business_profile_id` to `business_profiles.id`

### Unique Constraints
- `subscriptions_user_business_profile_unique` - Ensures a user can only subscribe to a specific business once
  - Columns: `user_id`, `business_profile_id`

### Check Constraints
Multiple check constraints ensure that required fields are not null.

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| subscriptions_pkey | UNIQUE | id | Primary key index |
| subscriptions_user_business_profile_unique | UNIQUE | user_id, business_profile_id | Ensures unique combination of user and business |

## Triggers

### trigger_add_subscription_activity
- **Event**: INSERT
- **Function**: add_subscription_activity()
- **Description**: Creates a record in the business_activities table when a new subscription is added

### handle_new_subscription
- **Event**: INSERT
- **Function**: update_total_subscriptions()
- **Description**: Updates the total_subscriptions count in the business_profiles table when a new subscription is added

### handle_deleted_subscription
- **Event**: DELETE
- **Function**: update_total_subscriptions()
- **Description**: Updates the total_subscriptions count in the business_profiles table when a subscription is removed

### Trigger Function Definitions

#### add_subscription_activity()

```sql
CREATE OR REPLACE FUNCTION public.add_subscription_activity()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
BEGIN
  -- Insert a new activity record
  INSERT INTO business_activities (
    business_profile_id,
    user_id,
    activity_type,
    created_at
  ) VALUES (
    NEW.business_profile_id,
    NEW.user_id,
    'subscribe',
    NEW.created_at
  );
  
  RETURN NEW;
END;
$function$
```

#### update_total_subscriptions()

```sql
CREATE OR REPLACE FUNCTION public.update_total_subscriptions()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  total_count integer;
  profile_id uuid;
BEGIN
  -- Determine the business_profile_id based on the operation type
  IF (TG_OP = 'DELETE') THEN
    profile_id := OLD.business_profile_id;
  ELSE
    profile_id := NEW.business_profile_id;
  END IF;

  -- Count the total subscriptions for the specific business profile
  SELECT COUNT(*) INTO total_count
  FROM public.subscriptions
  WHERE business_profile_id = profile_id;

  -- Update the business_profiles table
  UPDATE public.business_profiles
  SET total_subscriptions = total_count
  WHERE id = profile_id;

  RETURN NULL; -- Result is ignored since this is an AFTER trigger
END;
$function$
```

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| Allow public read access for subscriptions | SELECT | true | |
| Allow authenticated users to insert their own subscription | INSERT | | |
| Allow users to delete their own subscription | DELETE | (auth.role() = 'authenticated' AND user_id = auth.uid()) | |

These policies ensure that:
1. Anyone can view subscriptions (public read access)
2. Authenticated users can add subscriptions
3. Users can only delete their own subscriptions

## Related Tables

### users
The `users` table is referenced by the `user_id` foreign key and contains information about the user who subscribed to the business.

### business_profiles
The `business_profiles` table is referenced by the `business_profile_id` foreign key and contains information about the business that was subscribed to.

### business_activities
The `business_activities` table receives new records via the `add_subscription_activity` trigger when a subscription is created, tracking this as a business activity.

## Usage Notes

1. **Subscription Functionality**:
   - The table implements a "follow" or "subscribe" feature for businesses
   - Each record represents one user subscribing to one business
   - The unique constraint ensures a user can only subscribe to a business once

2. **Business Metrics**:
   - The `update_total_subscriptions` trigger maintains an accurate count of subscriptions in the `business_profiles.total_subscriptions` field
   - This enables efficient display of follower counts without needing to query the subscriptions table

3. **Activity Tracking**:
   - The `add_subscription_activity` trigger creates a record in the `business_activities` table
   - This allows business owners to see notifications about new subscribers

4. **Security**:
   - RLS policies ensure that while anyone can view subscriptions, only authenticated users can create them
   - Users can only delete their own subscriptions, not subscriptions created by others

5. **Performance Considerations**:
   - The composite index on `user_id` and `business_profile_id` supports efficient lookups
   - The triggers maintain denormalized counts for performance optimization

6. **User Experience Flow**:
   - When a user subscribes to a business, a new record is created
   - The business owner receives a notification via the business_activities table
   - The business profile's subscription count is automatically updated
   - If a user tries to subscribe to the same business again, the unique constraint prevents duplicate subscriptions

7. **Data Relationships**:
   - Each subscription is associated with exactly one user and one business profile
   - The relationship between users and business profiles through subscriptions is many-to-many

8. **Difference from Likes**:
   - While similar to likes, subscriptions typically imply an ongoing relationship where the user wants to receive updates
   - In the Dukancard application, subscriptions may be used to notify users of new products, offers, or updates from businesses they follow
