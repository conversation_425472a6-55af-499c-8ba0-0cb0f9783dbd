import React, { useEffect, useCallback } from 'react';
import { Alert, Platform } from 'react-native';
import Jai<PERSON><PERSON>onkey from 'jail-monkey';
import RNExitApp from 'react-native-exit-app';

interface DeviceSecurityCheckProps {
  children: React.ReactNode;
}

// Extend global types for security checks
declare global {
  interface Window {
    chrome?: {
      runtime?: any;
    };
  }
}

const DeviceSecurityCheck: React.FC<DeviceSecurityCheckProps> = ({ children }) => {
  const checkDeviceSecurity = useCallback(async () => {
    try {
      // Skip security checks in development mode
      if (__DEV__) {
        return;
      }

      // Check for debugging
      const isDebuggedMode = await JailMonkey.isDebuggedMode();
      
      // Check for jailbreak/root
      const isJailBroken = 
        JailMonkey.isOnExternalStorage() ||
        JailMonkey.isJailBroken() ||
        JailMonkey.trustFall() ||
        isDebuggedMode ||
        JailMonkey.canMockLocation();

      // Check for hook frameworks (Android)
      const hookDetected = Platform.OS === 'android' && JailMonkey.isOnExternalStorage();

      // Additional runtime checks
      const runtimeChecks = performRuntimeChecks();

      if (isJailBroken || hookDetected || runtimeChecks.suspicious) {
        showSecurityAlert();
      }
    } catch (error) {
      console.warn('Security check failed:', error);
      // In production, you might want to exit on security check failure
      if (!__DEV__) {
        showSecurityAlert();
      }
    }
  }, []);

  useEffect(() => {
    checkDeviceSecurity();
  }, [checkDeviceSecurity]);

  const performRuntimeChecks = () => {
    let suspicious = false;

    // Check for debugging tools
    if (typeof atob !== 'undefined' && !__DEV__) {
      suspicious = true;
    }

    // Check for common debugging variables
    if (typeof window !== 'undefined' && window.chrome && window.chrome.runtime) {
      suspicious = true;
    }

    // Check for Flipper (React Native debugger)
    if (typeof global !== 'undefined' && (global as any).__FLIPPER__) {
      suspicious = true;
    }

    // Check for Metro bundler in production
    if (!__DEV__ && typeof global !== 'undefined' && (global as any).__METRO__) {
      suspicious = true;
    }

    return { suspicious };
  };

  const showSecurityAlert = () => {
    Alert.alert(
      'Security Warning',
      'This device appears to be compromised or running in an unsafe environment. The app will now close for security reasons.',
      [
        {
          text: 'OK',
          onPress: () => {
            // Exit the app
            RNExitApp.exitApp();
          }
        }
      ],
      { cancelable: false }
    );
  };

  return <>{children}</>;
};

export default DeviceSecurityCheck;
