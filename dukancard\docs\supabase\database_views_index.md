# Database Views Index

## Overview

This document provides an index of all database views in the Dukancard application, their purposes, and links to detailed documentation.

## Views Summary

| View Name | Purpose | Source Tables | Documentation |
|-----------|---------|---------------|---------------|
| `unified_posts_view` | Combines business and customer posts for unified feeds | `business_posts`, `customer_posts` | [unified_posts_view.md](./unified_posts_view.md) |
| `ad_targets_view` | Shows ads with their targeting information and expiry status | `custom_ads`, `custom_ad_targets` | [ad_targets_view.md](./ad_targets_view.md) |
| `expired_ads_view` | Filters and shows only expired ads for cleanup operations | `custom_ads` | [expired_ads_view.md](./expired_ads_view.md) |

## View Categories

### 1. Content Management Views

#### `unified_posts_view`
- **Purpose**: Enables unified feeds with perfect pagination across business and customer posts
- **Key Features**: Chronological ordering, mixed content types, inherited RLS
- **Use Cases**: Feed generation, content discovery, social features
- **Performance**: Optimized for millions of posts, scalable to search engines

### 2. Advertising Management Views

#### `ad_targets_view`
- **Purpose**: Combines ad content with targeting rules for efficient ad serving
- **Key Features**: Location targeting, expiry status, global/local ads
- **Use Cases**: Ad serving, targeting management, analytics
- **Performance**: Optimized for real-time ad delivery

#### `expired_ads_view`
- **Purpose**: Identifies and manages expired advertisements
- **Key Features**: Automatic filtering, expiry duration calculation, cleanup support
- **Use Cases**: Administrative cleanup, lifecycle management, reporting
- **Performance**: Optimized for batch operations and maintenance tasks

## View Design Principles

### 1. Performance First
- All views leverage existing table indexes
- Pre-filtering reduces result sets
- Computed fields minimize application logic

### 2. Security Best Practices
- Views follow Supabase security guidelines (no SECURITY DEFINER)
- Views inherit RLS policies from source tables
- User-based permissions (not view creator permissions)
- No additional security configuration needed
- Automatic permission enforcement

### 3. Scalability Ready
- Views provide migration path to search engines
- API interfaces remain consistent during scaling
- Database-optimized for current scale

### 4. Maintainability
- Views automatically stay in sync with source tables
- Clear naming conventions and documentation
- Separation of concerns between different domains

## Common Query Patterns

### Pagination
```sql
-- Standard pagination pattern for all views
SELECT * FROM view_name 
WHERE [filters]
ORDER BY created_at DESC 
LIMIT 10 OFFSET 20;
```

### Location Filtering
```sql
-- Location-based filtering (available in most views)
SELECT * FROM view_name 
WHERE city_slug = 'rourkela' 
   OR locality_slug = 'sector-1'
   OR pincode = '769001';
```

### Status Filtering
```sql
-- Status-based filtering with computed fields
SELECT * FROM view_name 
WHERE status IN ('Active', 'Expiring Soon')
   OR expiry_status = 'Active';
```

## Performance Monitoring

### Key Metrics to Monitor
1. **Query execution times** for each view
2. **Index usage** on source tables
3. **Result set sizes** and pagination efficiency
4. **Memory usage** during complex queries

### Optimization Strategies
1. **Index maintenance** on source tables
2. **Query plan analysis** for slow queries
3. **Materialized views** for heavy analytical workloads (future)
4. **Search engine migration** for massive scale (future)

## Migration and Evolution

### Current Architecture (Phase 1)
- Database views for unified querying
- Inherited security from source tables
- Optimized for current scale (millions of records)

### Future Architecture (Phase 2)
- Search engine integration (Elasticsearch/OpenSearch)
- Real-time indexing and updates
- Advanced search and filtering capabilities
- Massive scale support (billions of records)

### Migration Strategy
- **API compatibility**: Same interfaces during migration
- **Gradual rollout**: A/B testing between old and new systems
- **Fallback support**: Views remain as backup during transition

## Best Practices

### For Developers
1. **Use views for complex queries**: Leverage pre-optimized view logic
2. **Respect view contracts**: Don't bypass views for direct table access
3. **Monitor performance**: Track query execution times and optimize accordingly
4. **Handle errors gracefully**: Implement proper error handling for view queries

### For Database Administrators
1. **Monitor index usage**: Ensure source table indexes are optimized
2. **Regular maintenance**: Keep table statistics updated with ANALYZE
3. **Performance tuning**: Monitor and optimize slow view queries
4. **Backup considerations**: Views are recreated from definition, no data backup needed

### For Application Architects
1. **Design for scale**: Consider future migration to search engines
2. **Consistent interfaces**: Maintain API compatibility across scaling phases
3. **Security by design**: Leverage inherited RLS policies
4. **Documentation**: Keep view documentation updated with schema changes

## Related Documentation

### Table Documentation
- [business_posts.md](./business_posts.md) - Source table for unified_posts_view
- [customer_posts.md](./customer_posts.md) - Source table for unified_posts_view
- [custom_ads.md](./custom_ads.md) - Source table for ad views
- [custom_ad_targets.md](./custom_ad_targets.md) - Source table for ad_targets_view

### Architecture Documentation
- Database schema overview
- Performance optimization guide
- Security and RLS documentation
- Scaling and migration strategies

This index provides a comprehensive overview of all database views and their role in the Dukancard application architecture.
