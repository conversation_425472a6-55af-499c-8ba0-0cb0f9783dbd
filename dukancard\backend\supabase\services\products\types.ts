// Export types used across multiple files
export interface ProductFilters {
  searchTerm?: string;
  filterAvailable?: boolean;
  hasVariants?: boolean;
  productType?: 'physical' | 'service';
  priceRange?: {
    min?: number;
    max?: number;
  };
}

export type ProductSortBy =
  | "created_asc"
  | "created_desc"
  | "updated_asc"
  | "updated_desc"
  | "price_asc"
  | "price_desc"
  | "name_asc"
  | "name_desc"
  | "variant_count_asc"
  | "variant_count_desc";

// Re-export the ProductServiceData type from schemas.ts
export type { ProductServiceData } from './schemas';