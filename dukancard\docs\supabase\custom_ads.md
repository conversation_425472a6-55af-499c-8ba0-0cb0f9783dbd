# Custom Ads Table Documentation

## Table Overview

The `custom_ads` table in the Dukancard application stores information about advertisements that can be displayed to users throughout the platform. It manages ad content, targeting, and lifecycle, enabling a location-based advertising system for the application.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description | 
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key for the ad record |
| ad_image_url | text | NO | | URL to the advertisement image |
| ad_link_url | text | YES | | URL that the ad links to when clicked |
| is_active | boolean | NO | true | Flag indicating whether the ad is currently active |
| created_at | timestamptz | NO | now() | Timestamp when the record was created |
| expiry_date | timestamptz | YES | | Date when the ad expires and should no longer be shown |

## Constraints

### Primary Key
- `custom_ads_pkey` - Primary key constraint on the `id` column

### Not Null Constraints
Multiple check constraints ensure that required fields are not null.

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| custom_ads_pkey | UNIQUE | id | Primary key index |
| idx_custom_ads_active_created | BTREE | is_active, created_at DESC | Index for filtering active ads and sorting by creation date |

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| Allow public read access to active ads | SELECT | (is_active = true) | |
| Authenticated users can manage all ads | ALL | true | |
| Public can read ads | SELECT | true | |

These policies ensure that:
1. Public users can view all ads (with a specific policy for active ads)
2. Authenticated users (likely administrators) have full control over all ads

## Related Tables

### custom_ad_targets
The `custom_ad_targets` table references the `custom_ads` table through the `ad_id` foreign key and contains information about the geographic targeting of ads.

## Usage Notes

1. **Ad Content Management**:
   - The table stores basic ad information including the image URL and optional link URL
   - Ads can be activated or deactivated using the `is_active` flag
   - Ads can have an optional expiry date after which they should no longer be displayed

2. **Ad Lifecycle**:
   - Newly created ads are active by default (`is_active = true`)
   - Ads can be deactivated by setting `is_active = false`
   - The `expiry_date` provides an automatic way to time-limit ads
   - The application should check both `is_active` and `expiry_date` when determining whether to display an ad

3. **Ad Targeting**:
   - Geographic targeting is handled through the related `custom_ad_targets` table
   - This allows ads to be shown only to users in specific locations (pincodes)
   - The `idx_custom_ads_active_created` index supports efficient queries for retrieving active ads sorted by creation date

4. **Security**:
   - RLS policies allow public read access to all ads, with a specific policy for active ads
   - Only authenticated users (likely administrators) can create, update, or delete ads
   - This separation ensures that ad management is restricted to authorized personnel

5. **Performance Considerations**:
   - The `idx_custom_ads_active_created` index optimizes the common query pattern of retrieving active ads sorted by creation date
   - This supports efficient pagination and "latest first" display of ads

6. **Ad Display Logic**:
   - When displaying ads, the application should:
     - Filter for `is_active = true`
     - Check that current date is before `expiry_date` (if set)
     - Join with `custom_ad_targets` to filter by the user's location
     - Sort by `created_at DESC` to show newest ads first

7. **Ad Metrics**:
   - The table does not directly store metrics like impressions or clicks
   - These would likely be tracked in a separate analytics table or system

8. **Data Relationships**:
   - Each ad can have multiple targeting rules in the `custom_ad_targets` table
   - This allows for complex geographic targeting strategies
