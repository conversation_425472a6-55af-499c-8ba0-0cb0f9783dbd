"use client";

import { useSearchParams } from "next/navigation";
import { motion } from "framer-motion";
import { MapPin, Globe, Building, Search } from "lucide-react";
import { useCategoryContext } from "../context/CategoryContext";

import { PINCODE_PARAM, CITY_PARAM, LOCALITY_PARAM, BUSINESS_NAME_PARAM, PRODUCT_NAME_PARAM } from "@/app/(main)/discover/constants/urlParamConstants";

export default function LocationIndicator() {
  const searchParams = useSearchParams();
  const { searchResult, viewType, category, locationInfo } = useCategoryContext();

  // Get location from URL params or context
  const pincode = searchParams.get(PINCODE_PARAM) || locationInfo?.pincode;
  const city = searchParams.get(CITY_PARAM) || locationInfo?.city;
  const locality = searchParams.get(LOCALITY_PARAM) || locationInfo?.locality;
  const state = locationInfo?.state;
  const businessName = searchParams.get(BUSINESS_NAME_PARAM);
  const productName = searchParams.get(PRODUCT_NAME_PARAM);

  // Determine the location text and icon based on search parameters
  let locationText = `All ${category.name} in India`;
  let LocationIcon = Globe;
  let detailText = `Showing ${category.name} businesses and products from across the country`;
  let highlightText = "";

  // If we have state information from context, use it
  if (state && !city && !pincode) {
    locationText = `${category.name} in ${state}`;
    LocationIcon = Building;
    detailText = `Showing ${category.name} ${viewType === "cards" ? "businesses" : "products"} in ${state}`;
    highlightText = state;
  }

  // If we have city information from context, prioritize it over state
  if (state && city && !pincode) {
    locationText = `${category.name} in ${city}`;
    LocationIcon = Building;
    detailText = `Showing ${category.name} ${viewType === "cards" ? "businesses" : "products"} in ${city}, ${state}`;
    highlightText = city;
  }

  if (businessName || productName) {
    // Search by name
    const searchTerm = businessName || productName || "";
    locationText = `Search results for "${searchTerm}"`;
    LocationIcon = Search;
    detailText = `Showing ${category.name} ${viewType === "cards" ? "businesses" : "products"} matching your search`;
    highlightText = searchTerm;
  } else if (pincode) {
    // Search by pincode
    locationText = `${category.name} in ${pincode}`;
    LocationIcon = MapPin;
    detailText = `Showing ${category.name} ${viewType === "cards" ? "businesses" : "products"} in pincode ${pincode}`;
    highlightText = pincode;

    // Add locality if available
    if (locality) {
      locationText += ` - ${locality}`;
      detailText += `, ${locality}`;
    }

    // Add city and state if available
    if (city) {
      detailText += `, ${city}`;
      if (state) {
        detailText += `, ${state}`;
      }
    } else if (state) {
      detailText += `, ${state}`;
    }
  } else if (city) {
    // Search by city
    locationText = `${category.name} in ${city}`;
    LocationIcon = Building;
    detailText = `Showing ${category.name} ${viewType === "cards" ? "businesses" : "products"} in ${city}`;
    highlightText = city;

    // Add locality if available
    if (locality) {
      locationText += ` - ${locality}`;
      detailText += `, ${locality}`;
    }

    // Add state if available
    if (state) {
      detailText += `, ${state}`;
    }
  }

  // Get location from search result if available
  if (searchResult?.location) {
    const { city: resultCity, state: resultState, pincode: resultPincode, locality: resultLocality } = searchResult.location;

    // Only use search result location if we don't have a city from locationInfo
    // This prevents the search result from overriding the city name from the URL
    if (!locationInfo?.city) {
      if (resultLocality && resultPincode) {
        locationText = `${category.name} in ${resultLocality}, ${resultPincode}`;
        detailText = `Showing ${category.name} ${viewType === "cards" ? "businesses" : "products"} in ${resultLocality}, ${resultPincode}`;
        if (resultCity) detailText += `, ${resultCity}`;
        if (resultState) detailText += `, ${resultState}`;
        highlightText = resultLocality;
        LocationIcon = MapPin;
      } else if (resultPincode) {
        locationText = `${category.name} in ${resultPincode}`;
        detailText = `Showing ${category.name} ${viewType === "cards" ? "businesses" : "products"} in pincode ${resultPincode}`;
        if (resultCity) detailText += `, ${resultCity}`;
        if (resultState) detailText += `, ${resultState}`;
        highlightText = resultPincode;
        LocationIcon = MapPin;
      } else if (resultCity) {
        locationText = `${category.name} in ${resultCity}`;
        detailText = `Showing ${category.name} ${viewType === "cards" ? "businesses" : "products"} in ${resultCity}`;
        if (resultState) detailText += `, ${resultState}`;
        highlightText = resultCity;
        LocationIcon = Building;
      } else if (resultState) {
        locationText = `${category.name} in ${resultState}`;
        detailText = `Showing ${category.name} ${viewType === "cards" ? "businesses" : "products"} in ${resultState}`;
        highlightText = resultState;
        LocationIcon = Building;
      }
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="container mx-auto px-4 pt-0 pb-3"
    >
      <div className="bg-gradient-to-r from-neutral-50 to-neutral-100 dark:from-neutral-900 dark:to-neutral-950 rounded-lg p-3 border border-neutral-200 dark:border-neutral-800 shadow-sm">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="flex items-center mb-1 md:mb-0">
            <LocationIcon className="h-5 w-5 mr-2 text-[var(--brand-gold)]" />
            <div>
              <h2 className="text-lg font-semibold text-neutral-900 dark:text-white mb-0.5">
                {locationText}
              </h2>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                {detailText}
                {highlightText && (
                  <span className="font-medium text-[var(--brand-gold)]">
                    {" "}
                    &quot;{highlightText}&quot;
                  </span>
                )}
              </p>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
