"use client";

import { motion } from "framer-motion";
import {
  Building2,
  User,
  Phone,
  Mail,
  MapPin,
  Globe,
  Clock,
  Truck,
  MessageCircle,
  Calendar,
  Navigation
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import WhatsAppIcon from "@/app/components/icons/WhatsAppIcon";
import FacebookIcon from "@/app/components/icons/FacebookIcon";
import InstagramIcon from "@/app/components/icons/InstagramIcon";

interface BusinessHours {
  [key: string]: {
    isOpen: boolean;
    openTime: string;
    closeTime: string;
  };
}

interface ProfessionalBusinessTableProps {
  businessProfile: {
    business_name?: string;
    member_name?: string;
    title?: string;
    business_category?: string;
    phone?: string;
    contact_email?: string;
    address_line?: string;
    locality?: string;
    city?: string;
    state?: string;
    pincode?: string;
    delivery_info?: string;
    business_hours?: BusinessHours;
    instagram_url?: string;
    facebook_url?: string;
    whatsapp_number?: string;
    established_year?: number | null;
    google_maps_url?: string;
  };
  isAuthenticated: boolean;
}

// Helper function to get current day and business status
const getCurrentDay = (): string => {
  const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
  return days[new Date().getDay()];
};

const getBusinessStatus = (businessHours?: BusinessHours): { isOpen: boolean; status: string; statusColor: string } => {
  if (!businessHours || typeof businessHours !== 'object') {
    return { isOpen: false, status: 'Hours not available', statusColor: 'text-neutral-500' };
  }

  const currentDay = getCurrentDay();
  const currentTime = new Date();
  const currentHour = currentTime.getHours();
  const currentMinute = currentTime.getMinutes();
  const currentTimeInMinutes = currentHour * 60 + currentMinute;

  const todayHours = businessHours[currentDay];

  if (!todayHours || !todayHours.isOpen) {
    return { isOpen: false, status: 'Closed today', statusColor: 'text-red-600 dark:text-red-400' };
  }

  const openTime = todayHours.openTime?.split(':');
  const closeTime = todayHours.closeTime?.split(':');

  if (!openTime || !closeTime) {
    return { isOpen: false, status: 'Hours not available', statusColor: 'text-neutral-500' };
  }

  const openTimeInMinutes = parseInt(openTime[0]) * 60 + parseInt(openTime[1]);
  const closeTimeInMinutes = parseInt(closeTime[0]) * 60 + parseInt(closeTime[1]);

  if (currentTimeInMinutes >= openTimeInMinutes && currentTimeInMinutes <= closeTimeInMinutes) {
    const minutesUntilClose = closeTimeInMinutes - currentTimeInMinutes;
    if (minutesUntilClose <= 60) {
      return {
        isOpen: true,
        status: `Closes in ${minutesUntilClose} minute${minutesUntilClose !== 1 ? 's' : ''}`,
        statusColor: 'text-yellow-600 dark:text-yellow-400'
      };
    } else {
      const closeHour = parseInt(closeTime[0]);
      const closeMinute = parseInt(closeTime[1]);
      const closeTimeFormatted = `${closeHour > 12 ? closeHour - 12 : closeHour}:${closeMinute.toString().padStart(2, '0')} ${closeHour >= 12 ? 'PM' : 'AM'}`;
      return {
        isOpen: true,
        status: `Open until ${closeTimeFormatted}`,
        statusColor: 'text-green-600 dark:text-green-400'
      };
    }
  } else if (currentTimeInMinutes < openTimeInMinutes) {
    const minutesUntilOpen = openTimeInMinutes - currentTimeInMinutes;
    if (minutesUntilOpen <= 60) {
      return {
        isOpen: false,
        status: `Opens in ${minutesUntilOpen} minute${minutesUntilOpen !== 1 ? 's' : ''}`,
        statusColor: 'text-blue-600 dark:text-blue-400'
      };
    } else {
      const openHour = parseInt(openTime[0]);
      const openMinute = parseInt(openTime[1]);
      const openTimeFormatted = `${openHour > 12 ? openHour - 12 : openHour}:${openMinute.toString().padStart(2, '0')} ${openHour >= 12 ? 'PM' : 'AM'}`;
      return {
        isOpen: false,
        status: `Opens at ${openTimeFormatted}`,
        statusColor: 'text-orange-600 dark:text-orange-400'
      };
    }
  } else {
    return { isOpen: false, status: 'Closed for today', statusColor: 'text-red-600 dark:text-red-400' };
  }
};

const formatBusinessHoursForDisplay = (businessHours?: BusinessHours): Array<{day: string, hours: string}> => {
  if (!businessHours || typeof businessHours !== 'object') {
    return [];
  }

  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  return days.map((day, index) => {
    const dayData = businessHours[day];
    const dayName = dayNames[index];

    if (!dayData || !dayData.isOpen) {
      return { day: dayName, hours: 'Closed' };
    }

    const openTime = formatTime(dayData.openTime);
    const closeTime = formatTime(dayData.closeTime);
    return { day: dayName, hours: `${openTime} - ${closeTime}` };
  });
};

const formatTime = (time: string): string => {
  const [hour, minute] = time.split(':');
  const hourNum = parseInt(hour);
  const period = hourNum >= 12 ? 'PM' : 'AM';
  const displayHour = hourNum > 12 ? hourNum - 12 : hourNum === 0 ? 12 : hourNum;
  return `${displayHour}:${minute} ${period}`;
};



export default function ProfessionalBusinessTable({
  businessProfile,
  isAuthenticated: _isAuthenticated,
}: ProfessionalBusinessTableProps) {
  const businessStatus = getBusinessStatus(businessProfile.business_hours);

  // Format address
  const fullAddress = [
    businessProfile.address_line,
    businessProfile.locality,
    businessProfile.city,
    businessProfile.state,
    businessProfile.pincode
  ].filter(Boolean).join(", ");

  // Get business hours for display
  const businessHoursData = formatBusinessHoursForDisplay(businessProfile.business_hours);

  // Helper functions for contact actions
  const handlePhoneClick = () => {
    if (businessProfile.phone) {
      const formattedNumber = businessProfile.phone.replace(/\D/g, "");
      window.open(`tel:+91${formattedNumber}`, "_self");
    }
  };

  const handleEmailClick = () => {
    if (businessProfile.contact_email) {
      window.open(`mailto:${businessProfile.contact_email}`, "_self");
    }
  };

  const handleWhatsAppClick = () => {
    if (businessProfile.whatsapp_number) {
      const formattedNumber = businessProfile.whatsapp_number.replace(/\D/g, "");
      const whatsappNumber = formattedNumber.startsWith("91") ? formattedNumber : `91${formattedNumber}`;
      const message = encodeURIComponent(`Hi ${businessProfile.business_name}, I found your business on Dukancard and would like to know more about your services.`);
      window.open(`https://wa.me/${whatsappNumber}?text=${message}`, "_blank");
    }
  };

  const handleFacebookClick = () => {
    if (businessProfile.facebook_url) {
      window.open(businessProfile.facebook_url, "_blank");
    }
  };

  const handleInstagramClick = () => {
    if (businessProfile.instagram_url) {
      window.open(businessProfile.instagram_url, "_blank");
    }
  };

  const tableData = [
    // Business Identity
    { icon: Building2, label: "Business Name", value: businessProfile.business_name || "Not specified", category: "identity" },
    { icon: User, label: "Owner Name", value: businessProfile.member_name || "Not specified", category: "identity" },
    { icon: Globe, label: "Business Category", value: businessProfile.business_category || "Not specified", category: "identity" },
    { icon: Calendar, label: "Established Year", value: businessProfile.established_year ? `${businessProfile.established_year}` : "Not specified", category: "identity" },
    { icon: Clock, label: "Business Status", value: businessStatus.status, category: "identity", statusColor: businessStatus.statusColor },

    // Contact Information - Always show as clickable buttons
    ...(businessProfile.phone ? [
      { icon: Phone, label: "Phone Number", value: "Call Now", category: "contact", isContactButton: true, contactType: "phone", onClick: handlePhoneClick }
    ] : []),
    ...(businessProfile.contact_email ? [
      { icon: Mail, label: "Email Address", value: "Send Email", category: "contact", isContactButton: true, contactType: "email", onClick: handleEmailClick }
    ] : []),
    { icon: MapPin, label: "Full Address", value: fullAddress || "Not provided", category: "contact" },
    { icon: Truck, label: "Delivery Info", value: businessProfile.delivery_info || "Not specified", category: "contact" },
    ...(businessProfile.google_maps_url ? [
      { icon: Navigation, label: "Get Directions", value: "Click to open in Google Maps", category: "contact", isButton: true, url: businessProfile.google_maps_url }
    ] : []),

    // Business Hours & Operations - separate row for each day
    ...businessHoursData.map(({ day, hours }) => ({
      icon: Clock,
      label: day,
      value: hours,
      category: "operations" as const
    })),

    // Social Media - Show as clickable buttons
    ...(businessProfile.whatsapp_number ? [
      { icon: MessageCircle, label: "WhatsApp", value: "Chat on WhatsApp", category: "social", isContactButton: true, contactType: "whatsapp", onClick: handleWhatsAppClick }
    ] : []),
    ...(businessProfile.facebook_url ? [
      { icon: Globe, label: "Facebook", value: "Visit Facebook Page", category: "social", isContactButton: true, contactType: "facebook", onClick: handleFacebookClick }
    ] : []),
    ...(businessProfile.instagram_url ? [
      { icon: Globe, label: "Instagram", value: "Visit Instagram Profile", category: "social", isContactButton: true, contactType: "instagram", onClick: handleInstagramClick }
    ] : []),
  ];

  const categories = [
    { id: "identity", name: "Business Information", color: "bg-white dark:bg-black" },
    { id: "contact", name: "Contact Details", color: "bg-white dark:bg-black" },
    { id: "operations", name: "Weekly Schedule", color: "bg-white dark:bg-black" },
    { id: "social", name: "Social Media", color: "bg-white dark:bg-black" },
  ];

  return (
    <div className="p-6">
      <div className="space-y-4">
        {categories.map((category, categoryIndex) => {
          const categoryData = tableData.filter(item => item.category === category.id);

          return (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: categoryIndex * 0.1, duration: 0.5 }}
              className={`rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden ${category.color}`}
            >
              <div className="p-4 border-b border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-900">
                <h4 className="font-semibold text-neutral-900 dark:text-neutral-100">
                  {category.name}
                </h4>
              </div>

              <Table>
                <TableBody>
                  {categoryData.map((row, index) => {
                    const IconComponent = row.icon;
                    return (
                      <TableRow key={`${category.id}-${index}`} className="hover:bg-white/50 dark:hover:bg-neutral-800/50">
                        <TableCell className="font-medium w-1/3">
                          <div className="flex items-center gap-3">
                            <IconComponent className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
                            <span className="text-sm">{row.label}</span>
                          </div>
                        </TableCell>
                        <TableCell className="w-2/3">
                          <div className="flex items-center gap-2">
                            {(row as { isContactButton?: boolean; contactType?: string; onClick?: () => void }).isContactButton ? (
                              <Button
                                onClick={(row as { onClick: () => void }).onClick}
                                size="sm"
                                className={`font-medium text-xs py-1.5 px-3 h-7 shadow-sm hover:shadow-md transition-all duration-200 ${
                                  (row as { contactType: string }).contactType === "phone"
                                    ? "bg-blue-600 hover:bg-blue-700 text-white"
                                    : (row as { contactType: string }).contactType === "email"
                                    ? "bg-purple-600 hover:bg-purple-700 text-white"
                                    : (row as { contactType: string }).contactType === "whatsapp"
                                    ? "bg-green-600 hover:bg-green-700 text-white"
                                    : (row as { contactType: string }).contactType === "facebook"
                                    ? "bg-blue-800 hover:bg-blue-900 text-white"
                                    : (row as { contactType: string }).contactType === "instagram"
                                    ? "bg-pink-600 hover:bg-pink-700 text-white"
                                    : "bg-gray-600 hover:bg-gray-700 text-white"
                                }`}
                              >
                                {(row as { contactType: string }).contactType === "phone" && <Phone className="mr-2 h-3 w-3" />}
                                {(row as { contactType: string }).contactType === "email" && <Mail className="mr-2 h-3 w-3" />}
                                {(row as { contactType: string }).contactType === "whatsapp" && <WhatsAppIcon className="mr-2 h-3 w-3" />}
                                {(row as { contactType: string }).contactType === "facebook" && <FacebookIcon className="mr-2 h-3 w-3" />}
                                {(row as { contactType: string }).contactType === "instagram" && <InstagramIcon className="mr-2 h-3 w-3" />}
                                {row.value}
                              </Button>
                            ) : (row as { isButton?: boolean; url?: string }).isButton ? (
                              <Button
                                onClick={() => window.open((row as { url: string }).url, "_blank")}
                                size="sm"
                                className="bg-blue-600 hover:bg-blue-700 text-white font-medium text-xs py-1.5 px-3 h-7 shadow-sm hover:shadow-md transition-all duration-200"
                              >
                                <Navigation className="mr-2 h-3 w-3" />
                                Get Directions
                              </Button>
                            ) : row.statusColor ? (
                              <span className={`text-sm font-medium ${row.statusColor}`}>
                                {row.value}
                              </span>
                            ) : row.label.includes("Status") && row.value === "online" ? (
                              <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                {row.value}
                              </Badge>
                            ) : row.label.includes("Status") && row.value === "offline" ? (
                              <Badge variant="secondary" className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                {row.value}
                              </Badge>
                            ) : row.label.includes("Subscription") ? (
                              <Badge variant={row.value === "Yes" ? "default" : "secondary"}>
                                {row.value}
                              </Badge>
                            ) : (
                              <span className="text-sm text-neutral-900 dark:text-neutral-100">
                                {row.value}
                              </span>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
}
