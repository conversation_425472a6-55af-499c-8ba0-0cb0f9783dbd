import { RazorpayWebhookData } from "../../types";
import { SupabaseClient } from "@supabase/supabase-js";

/**
 * Handle refund.created event
 *
 * This event is triggered when a refund is created.
 * Since the refunds table has been removed, this handler only logs the event
 * and returns success. Idempotency is handled by the main webhook handler
 * using the processed_webhook_events table.
 *
 * @param payload The webhook payload
 * @param _supabase The Supabase client (unused)
 * @returns The result of handling the event
 */
export async function handleRefundCreated(
  payload: RazorpayWebhookData,
  _supabase: SupabaseClient,
  _razorpayEventId?: string
): Promise<{ success: boolean; message: string }> {
  try {
    const refund = payload.payload.refund;
    if (!refund) {
      console.error("[RAZORPAY_WEBHOOK] Refund data not found in payload");
      return { success: false, message: "Refund data not found in payload" };
    }

    const refundId = refund.entity.id;
    const paymentId = refund.entity.payment_id;
    const amount = refund.entity.amount / 100; // Convert from paise to rupees
    const currency = refund.entity.currency;
    const _status = refund.entity.status;

    console.log(`[RAZORPAY_WEBHOOK] Refund created: ${refundId} for payment ${paymentId} - ${amount} ${currency}`);

    // Log additional refund details for audit purposes
    if (refund.entity.speed_requested) {

    }
    if (refund.entity.notes && Object.keys(refund.entity.notes).length > 0) {

    }

    // Note: Refunds table has been removed, so we only log the event
    // Idempotency is handled by the main webhook handler using processed_webhook_events table
    return { success: true, message: `Refund created event processed for refund ${refundId}` };
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Error handling refund created:", error);
    return {
      success: false,
      message: `Error handling refund created: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Handle refund.processed event
 *
 * This event is triggered when a refund is processed.
 * Since the refunds table has been removed, this handler only logs the event
 * and returns success. Idempotency is handled by the main webhook handler
 * using the processed_webhook_events table.
 *
 * @param payload The webhook payload
 * @param _supabase The Supabase client (unused)
 * @returns The result of handling the event
 */
export async function handleRefundProcessed(
  payload: RazorpayWebhookData,
  _supabase: SupabaseClient,
  _razorpayEventId?: string
): Promise<{ success: boolean; message: string }> {
  try {
    const refund = payload.payload.refund;
    if (!refund) {
      console.error("[RAZORPAY_WEBHOOK] Refund data not found in payload");
      return { success: false, message: "Refund data not found in payload" };
    }

    const refundId = refund.entity.id;
    const status = refund.entity.status;
    const speedProcessed = refund.entity.speed_processed;

    console.log(`[RAZORPAY_WEBHOOK] Refund processed: ${refundId} - status: ${status}`);

    if (speedProcessed) {

    }

    // Note: Refunds table has been removed, so we only log the event
    // Idempotency is handled by the main webhook handler using processed_webhook_events table
    return { success: true, message: `Refund processed event handled for refund ${refundId}` };
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Error handling refund processed:", error);
    return {
      success: false,
      message: `Error handling refund processed: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Handle refund.failed event
 *
 * This event is triggered when a refund fails.
 * Since the refunds table has been removed, this handler only logs the event
 * and returns success. Idempotency is handled by the main webhook handler
 * using the processed_webhook_events table.
 *
 * @param payload The webhook payload
 * @param _supabase The Supabase client (unused)
 * @returns The result of handling the event
 */
export async function handleRefundFailed(
  payload: RazorpayWebhookData,
  _supabase: SupabaseClient,
  _razorpayEventId?: string
): Promise<{ success: boolean; message: string }> {
  try {
    const refund = payload.payload.refund;
    if (!refund) {
      console.error("[RAZORPAY_WEBHOOK] Refund data not found in payload");
      return { success: false, message: "Refund data not found in payload" };
    }

    const refundId = refund.entity.id;
    const status = refund.entity.status;
    const paymentId = refund.entity.payment_id;

    console.log(`[RAZORPAY_WEBHOOK] Refund failed: ${refundId} for payment ${paymentId} - status: ${status}`);

    // Log additional failure details if available
    if (refund.entity.notes && Object.keys(refund.entity.notes).length > 0) {

    }

    // Note: Refunds table has been removed, so we only log the event
    // Idempotency is handled by the main webhook handler using processed_webhook_events table
    return { success: true, message: `Refund failed event handled for refund ${refundId}` };
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Error handling refund failed:", error);
    return {
      success: false,
      message: `Error handling refund failed: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}
