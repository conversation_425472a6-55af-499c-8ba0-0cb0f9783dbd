/**
 * Business Interactions Service for React Native
 * Handles like, subscribe, and review functionality for businesses
 */

import { supabase } from '@/lib/supabase';

export interface BusinessInteractionResponse {
  success: boolean;
  message: string;
  error?: string;
}

export interface BusinessInteractionStatus {
  isLiked: boolean;
  isSubscribed: boolean;
  userRating: number | null;
  userReview: string | null;
  likeCount: number;
  subscriptionCount: number;
  averageRating: number;
  reviewCount: number;
}

/**
 * Get current user's interaction status with a business
 */
export async function getBusinessInteractionStatus(
  businessId: string
): Promise<{ success: boolean; data?: BusinessInteractionStatus; error?: string }> {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      // Return default status for unauthenticated users
      const [likesResult, subscriptionsResult, reviewsResult] = await Promise.all([
        supabase
          .from('likes')
          .select('id', { count: 'exact' })
          .eq('business_profile_id', businessId),
        supabase
          .from('subscriptions')
          .select('id', { count: 'exact' })
          .eq('business_profile_id', businessId),
        supabase
          .from('ratings_reviews')
          .select('rating', { count: 'exact' })
          .eq('business_profile_id', businessId)
      ]);

      const averageRating = reviewsResult.data?.length
        ? reviewsResult.data.reduce((sum: any, review: any) => sum + (review.rating || 0), 0) / reviewsResult.data.length
        : 0;

      return {
        success: true,
        data: {
          isLiked: false,
          isSubscribed: false,
          userRating: null,
          userReview: null,
          likeCount: likesResult.count || 0,
          subscriptionCount: subscriptionsResult.count || 0,
          averageRating,
          reviewCount: reviewsResult.count || 0,
        }
      };
    }

    // Get user's interactions and business stats
    const [likeResult, subscriptionResult, reviewResult, likesCountResult, subscriptionsCountResult, reviewsStatsResult] = await Promise.all([
      supabase
        .from('likes')
        .select('id')
        .eq('business_profile_id', businessId)
        .eq('user_id', user.id)
        .single(),
      supabase
        .from('subscriptions')
        .select('id')
        .eq('business_profile_id', businessId)
        .eq('user_id', user.id)
        .single(),
      supabase
        .from('ratings_reviews')
        .select('rating, review_text')
        .eq('business_profile_id', businessId)
        .eq('user_id', user.id)
        .single(),
      supabase
        .from('likes')
        .select('id', { count: 'exact' })
        .eq('business_profile_id', businessId),
      supabase
        .from('subscriptions')
        .select('id', { count: 'exact' })
        .eq('business_profile_id', businessId),
      supabase
        .from('ratings_reviews')
        .select('rating')
        .eq('business_profile_id', businessId)
    ]);

    const averageRating = reviewsStatsResult.data?.length
      ? reviewsStatsResult.data.reduce((sum: any, review: any) => sum + (review.rating || 0), 0) / reviewsStatsResult.data.length
      : 0;

    return {
      success: true,
      data: {
        isLiked: !likeResult.error,
        isSubscribed: !subscriptionResult.error,
        userRating: reviewResult.data?.rating || null,
        userReview: reviewResult.data?.review_text || null,
        likeCount: likesCountResult.count || 0,
        subscriptionCount: subscriptionsCountResult.count || 0,
        averageRating,
        reviewCount: reviewsStatsResult.data?.length || 0,
      }
    };
  } catch (error) {
    console.error('Error getting business interaction status:', error);
    return {
      success: false,
      error: 'Failed to get interaction status'
    };
  }
}

/**
 * Toggle like status for a business
 */
export async function toggleBusinessLike(
  businessId: string
): Promise<BusinessInteractionResponse> {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return { 
        success: false, 
        message: 'Authentication required',
        error: 'User not authenticated' 
      };
    }

    // Check if already liked
    const { data: existingLike, error: checkError } = await supabase
      .from('likes')
      .select('id')
      .eq('business_profile_id', businessId)
      .eq('user_id', user.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError;
    }

    if (existingLike) {
      // Unlike
      const { error: deleteError } = await supabase
        .from('likes')
        .delete()
        .eq('id', existingLike.id);

      if (deleteError) throw deleteError;

      return {
        success: true,
        message: 'Business unliked successfully'
      };
    } else {
      // Like
      const { error: insertError } = await supabase
        .from('likes')
        .insert({
          business_profile_id: businessId,
          user_id: user.id
        });

      if (insertError) throw insertError;

      return {
        success: true,
        message: 'Business liked successfully'
      };
    }
  } catch (error) {
    console.error('Error toggling business like:', error);
    return {
      success: false,
      message: 'Failed to update like status',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Toggle subscription status for a business
 */
export async function toggleBusinessSubscription(
  businessId: string
): Promise<BusinessInteractionResponse> {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return { 
        success: false, 
        message: 'Authentication required',
        error: 'User not authenticated' 
      };
    }

    // Check if already subscribed
    const { data: existingSubscription, error: checkError } = await supabase
      .from('subscriptions')
      .select('id')
      .eq('business_profile_id', businessId)
      .eq('user_id', user.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError;
    }

    if (existingSubscription) {
      // Unsubscribe
      const { error: deleteError } = await supabase
        .from('subscriptions')
        .delete()
        .eq('id', existingSubscription.id);

      if (deleteError) throw deleteError;

      return {
        success: true,
        message: 'Unsubscribed successfully'
      };
    } else {
      // Subscribe
      const { error: insertError } = await supabase
        .from('subscriptions')
        .insert({
          business_profile_id: businessId,
          user_id: user.id
        });

      if (insertError) throw insertError;

      return {
        success: true,
        message: 'Subscribed successfully'
      };
    }
  } catch (error) {
    console.error('Error toggling business subscription:', error);
    return {
      success: false,
      message: 'Failed to update subscription status',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Check if user is the owner of the business
 */
export async function isBusinessOwner(businessId: string): Promise<boolean> {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return false;
    }

    const { data: business, error } = await supabase
      .from('business_profiles')
      .select('id')
      .eq('id', businessId)
      .eq('user_id', user.id)
      .single();

    return !error && !!business;
  } catch (error) {
    console.error('Error checking business ownership:', error);
    return false;
  }
}

/**
 * Submit a review for a business
 */
export async function submitBusinessReview(
  businessId: string,
  rating: number,
  reviewText?: string
): Promise<BusinessInteractionResponse> {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return { 
        success: false, 
        message: 'Authentication required',
        error: 'User not authenticated' 
      };
    }

    // Check if user already reviewed this business
    const { data: existingReview, error: checkError } = await supabase
      .from('ratings_reviews')
      .select('id')
      .eq('business_profile_id', businessId)
      .eq('user_id', user.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError;
    }

    if (existingReview) {
      // Update existing review
      const { error: updateError } = await supabase
        .from('ratings_reviews')
        .update({
          rating,
          review_text: reviewText || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingReview.id);

      if (updateError) throw updateError;

      return {
        success: true,
        message: 'Review updated successfully'
      };
    } else {
      // Create new review
      const { error: insertError } = await supabase
        .from('ratings_reviews')
        .insert({
          business_profile_id: businessId,
          user_id: user.id,
          rating,
          review_text: reviewText || null
        });

      if (insertError) throw insertError;

      return {
        success: true,
        message: 'Review submitted successfully'
      };
    }
  } catch (error) {
    console.error('Error submitting business review:', error);
    return {
      success: false,
      message: 'Failed to submit review',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
