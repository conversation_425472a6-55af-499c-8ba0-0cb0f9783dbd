"use server";

import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { revalidatePath } from "next/cache";
// getSecureBusinessProfileBySlug is imported but not used in this file
// import { getSecureBusinessProfileBySlug } from './secureBusinessProfiles';
// import { cookies } from 'next/headers'; // Removed unused import

export async function subscribeToBusiness(
  businessProfileId: string
): Promise<{ success: boolean; error?: string }> {
  // const cookieStore = cookies(); // No longer needed here
  const supabase = await createClient(); // Await the async function

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  // Prevent a business from subscribing to their own business card
  if (user.id === businessProfileId) {
    return {
      success: false,
      error: "You cannot subscribe to your own business card.",
    };
  }

  // Check if the current user is a business (has a business profile)
  const { data: userBusinessProfile } = await supabase
    .from("business_profiles")
    .select("id")
    .eq("id", user.id)
    .maybeSingle();

  try {
    // 1. Insert subscription - Use admin client to bypass RLS
    const supabaseAdminForSubscribe = createAdminClient();
    const { error: insertError } = await supabaseAdminForSubscribe
      .from("subscriptions")
      .insert({ user_id: user.id, business_profile_id: businessProfileId });

    if (insertError) {
      // Handle potential unique constraint violation (already subscribed) gracefully
      if (insertError.code === "23505") {
        // unique_violation
        console.log(
          `User ${user.id} already subscribed to business ${businessProfileId}.`
        );
        // Optionally return success true if already subscribed is acceptable
        return { success: true };
      }
      console.error("Error inserting subscription:", insertError);
      throw new Error(insertError.message);
    }

    // Note: We don't need to manually update the subscription count
    // The database trigger 'update_total_subscriptions' will handle this automatically

    // 3. Revalidate paths
    // Revalidate the specific card page and potentially the user's dashboard
    // Use admin client to bypass RLS
    const supabaseAdmin = createAdminClient();
    const { data: cardData } = await supabaseAdmin
      .from("business_profiles")
      .select("business_slug")
      .eq("id", businessProfileId)
      .single();

    if (cardData?.business_slug) {
      revalidatePath(`/${cardData.business_slug}`);
    }
    revalidatePath("/dashboard/customer"); // Revalidate customer dashboard

    // Check if the current user is a business and revalidate business dashboard
    if (userBusinessProfile) {
      revalidatePath("/dashboard/business"); // Revalidate business dashboard
      revalidatePath("/dashboard/business/subscriptions"); // Revalidate business subscriptions page
    }

    // Revalidate the activities page for the business
    revalidatePath(`/dashboard/business/activities`);

    return { success: true };
  } catch (error) {
    console.error("Unexpected error in subscribeToBusiness:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred.";
    return { success: false, error: errorMessage };
  }
}

// --- Implementation for other actions ---

export async function unsubscribeFromBusiness(
  businessProfileId: string
): Promise<{ success: boolean; error?: string }> {
  // const cookieStore = cookies();
  const supabase = await createClient(); // Await the async function

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  // Prevent a business from unsubscribing from their own business card
  if (user.id === businessProfileId) {
    return {
      success: false,
      error: "You cannot unsubscribe from your own business card.",
    };
  }

  // Check if the current user is a business (has a business profile)
  const { data: userBusinessProfile } = await supabase
    .from("business_profiles")
    .select("id")
    .eq("id", user.id)
    .maybeSingle();

  try {
    // 1. Delete subscription - Use admin client to bypass RLS
    const supabaseAdminForUnsubscribe = createAdminClient();
    const { error: deleteError } = await supabaseAdminForUnsubscribe
      .from("subscriptions")
      .delete()
      .match({ user_id: user.id, business_profile_id: businessProfileId });

    if (deleteError) {
      console.error("Error deleting subscription:", deleteError);
      throw new Error(deleteError.message);
    }

    // Note: We don't need to manually update the subscription count
    // The database trigger 'update_total_subscriptions' will handle this automatically

    // 3. Revalidate paths
    // Use admin client to bypass RLS
    const supabaseAdmin = createAdminClient();
    const { data: cardData } = await supabaseAdmin
      .from("business_profiles")
      .select("business_slug")
      .eq("id", businessProfileId)
      .single();

    if (cardData?.business_slug) {
      revalidatePath(`/${cardData.business_slug}`);
    }
    revalidatePath("/dashboard/customer");

    // Check if the current user is a business and revalidate business dashboard
    if (userBusinessProfile) {
      revalidatePath("/dashboard/business"); // Revalidate business dashboard
      revalidatePath("/dashboard/business/subscriptions"); // Revalidate business subscriptions page
    }

    // Revalidate the activities page for the business
    revalidatePath(`/dashboard/business/activities`);

    return { success: true };
  } catch (error) {
    console.error("Unexpected error in unsubscribeFromBusiness:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred.";
    return { success: false, error: errorMessage };
  }
}

export async function submitReview(
  businessProfileId: string,
  rating: number,
  reviewText?: string | null // Allow null for review text
): Promise<{ success: boolean; error?: string }> {
  // const cookieStore = cookies();
  const supabase = await createClient(); // Await the async function

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  // Prevent a business from reviewing their own business card
  if (user.id === businessProfileId) {
    return { success: false, error: "You cannot review your own business card." };
  }

  if (rating < 1 || rating > 5) {
    return { success: false, error: "Rating must be between 1 and 5." };
  }

  try {
    // Upsert the review: insert if not exists, update if exists - Use admin client to bypass RLS
    const supabaseAdminForReview = createAdminClient();
    const { error: upsertError } = await supabaseAdminForReview
      .from("ratings_reviews")
      .upsert(
        {
          user_id: user.id,
          business_profile_id: businessProfileId,
          rating: rating,
          review_text: reviewText, // Pass reviewText directly
          updated_at: new Date().toISOString(), // Explicitly set updated_at on upsert
        },
        {
          onConflict: "user_id, business_profile_id", // Specify conflict target
        }
      );

    if (upsertError) {
      console.error("Error submitting review:", upsertError);
      throw new Error(upsertError.message);
    }

    // Average rating is handled by the database trigger

    // Revalidate paths
    // Use admin client to bypass RLS
    const supabaseAdmin = createAdminClient();
    const { data: cardData } = await supabaseAdmin
      .from("business_profiles")
      .select("business_slug")
      .eq("id", businessProfileId)
      .single();

    if (cardData?.business_slug) {
      revalidatePath(`/${cardData.business_slug}`);
    }
    revalidatePath("/dashboard/customer"); // Revalidate customer dashboard where reviews might be shown

    // Revalidate the activities page for the business
    revalidatePath(`/dashboard/business/activities`);

    return { success: true };
  } catch (error) {
    console.error("Unexpected error in submitReview:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred.";
    return { success: false, error: errorMessage };
  }
}

export async function deleteReview(
  businessProfileId: string
): Promise<{ success: boolean; error?: string }> {
  // const cookieStore = cookies();
  const supabase = await createClient(); // Await the async function

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  try {
    // Use admin client to bypass RLS
    const supabaseAdminForDeleteReview = createAdminClient();
    const { error: deleteError } = await supabaseAdminForDeleteReview
      .from("ratings_reviews")
      .delete()
      .match({ user_id: user.id, business_profile_id: businessProfileId });

    if (deleteError) {
      console.error("Error deleting review:", deleteError);
      throw new Error(deleteError.message);
    }

    // Average rating is handled by the database trigger

    // Revalidate paths
    // Use admin client to bypass RLS
    const supabaseAdmin = createAdminClient();
    const { data: cardData } = await supabaseAdmin
      .from("business_profiles")
      .select("business_slug")
      .eq("id", businessProfileId)
      .single();

    if (cardData?.business_slug) {
      revalidatePath(`/${cardData.business_slug}`);
    }
    revalidatePath("/dashboard/customer");

    // Revalidate the activities page for the business
    revalidatePath(`/dashboard/business/activities`);

    return { success: true };
  } catch (error) {
    console.error("Unexpected error in deleteReview:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred.";
    return { success: false, error: errorMessage };
  }
}

export async function likeBusiness(
  businessProfileId: string
): Promise<{ success: boolean; error?: string }> {
  // const cookieStore = cookies();
  const supabase = await createClient(); // Await the async function

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  // Prevent a business from liking their own business card
  if (user.id === businessProfileId) {
    return { success: false, error: "You cannot like your own business card." };
  }

  try {
    // 1. Insert like - Use admin client to bypass RLS
    const supabaseAdminForLike = createAdminClient();
    const { error: insertError } = await supabaseAdminForLike
      .from("likes")
      .insert({ user_id: user.id, business_profile_id: businessProfileId });

    if (insertError) {
      // Handle potential unique constraint violation (already liked) gracefully
      if (insertError.code === "23505") {
        // unique_violation
        console.log(
          `User ${user.id} already liked business ${businessProfileId}.`
        );
        return { success: true }; // Consider it success if already liked
      }
      console.error("Error inserting like:", insertError);
      throw new Error(insertError.message);
    }

    // Note: We don't need to manually update the like count
    // The database trigger 'update_total_likes' will handle this automatically

    // 3. Revalidate paths
    // Use admin client to bypass RLS
    const supabaseAdminForSlug = createAdminClient();
    const { data: cardData } = await supabaseAdminForSlug
      .from("business_profiles")
      .select("business_slug")
      .eq("id", businessProfileId)
      .single();

    if (cardData?.business_slug) {
      revalidatePath(`/${cardData.business_slug}`);
    }

    // Check if the current user is a business and revalidate business dashboard
    const { data: userBusinessProfile } = await supabase
      .from("business_profiles")
      .select("id")
      .eq("id", user.id)
      .maybeSingle();

    if (userBusinessProfile) {
      revalidatePath("/dashboard/business"); // Revalidate business dashboard
      revalidatePath("/dashboard/business/likes"); // Revalidate business likes page
    }

    // Revalidate the activities page for the business
    revalidatePath(`/dashboard/business/activities`);

    return { success: true };
  } catch (error) {
    console.error("Unexpected error in likeBusiness:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred.";
    return { success: false, error: errorMessage };
  }
}

export async function unlikeBusiness(
  businessProfileId: string
): Promise<{ success: boolean; error?: string }> {
  // const cookieStore = cookies();
  const supabase = await createClient(); // Await the async function

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  // Prevent a business from unliking their own business card
  if (user.id === businessProfileId) {
    return {
      success: false,
      error: "You cannot unlike your own business card.",
    };
  }

  try {
    // 1. Delete like - Use admin client to bypass RLS
    const supabaseAdminForUnlike = createAdminClient();
    const { error: deleteError } = await supabaseAdminForUnlike
      .from("likes")
      .delete()
      .match({ user_id: user.id, business_profile_id: businessProfileId });

    if (deleteError) {
      console.error("Error deleting like:", deleteError);
      throw new Error(deleteError.message);
    }

    // Note: We don't need to manually update the like count
    // The database trigger 'update_total_likes' will handle this automatically

    // 3. Revalidate paths
    // Use admin client to bypass RLS
    const supabaseAdminForCardData = createAdminClient();
    const { data: cardData } = await supabaseAdminForCardData
      .from("business_profiles")
      .select("business_slug")
      .eq("id", businessProfileId)
      .single();

    if (cardData?.business_slug) {
      revalidatePath(`/${cardData.business_slug}`);
    }

    // Check if the current user is a business and revalidate business dashboard
    const { data: userBusinessProfile } = await supabase
      .from("business_profiles")
      .select("id")
      .eq("id", user.id)
      .maybeSingle();

    if (userBusinessProfile) {
      revalidatePath("/dashboard/business"); // Revalidate business dashboard
      revalidatePath("/dashboard/business/likes"); // Revalidate business likes page
    }

    // Revalidate the activities page for the business
    revalidatePath(`/dashboard/business/activities`);

    return { success: true };
  } catch (error) {
    console.error("Unexpected error in unlikeBusiness:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred.";
    return { success: false, error: errorMessage };
  }
}

export async function getInteractionStatus(businessProfileId: string): Promise<{
  isSubscribed: boolean;
  hasLiked: boolean;
  userRating: number | null;
  userReview: string | null;
  error?: string;
}> {
  // const cookieStore = cookies();
  const supabase = await createClient(); // Await the async function
  let userId: string | null = null;

  // Try to get authenticated user, but proceed even if not logged in
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (user) {
    userId = user.id;
  }

  // Default status for anonymous users
  const defaultStatus = {
    isSubscribed: false,
    hasLiked: false,
    userRating: null,
    userReview: null,
  };

  if (!userId) {
    return defaultStatus; // Return default if no user is logged in
  }

  try {
    // Use admin client to bypass RLS
    const supabaseAdminForStatus = createAdminClient();

    // Fetch all statuses in parallel
    const [subscriptionRes, likeRes, reviewRes] = await Promise.all([
      supabaseAdminForStatus
        .from("subscriptions")
        .select("id", { count: "exact", head: true }) // Just check existence
        .match({ user_id: userId, business_profile_id: businessProfileId }),
      supabaseAdminForStatus
        .from("likes")
        .select("id", { count: "exact", head: true }) // Just check existence
        .match({ user_id: userId, business_profile_id: businessProfileId }),
      supabaseAdminForStatus
        .from("ratings_reviews")
        .select("rating, review_text")
        .match({ user_id: userId, business_profile_id: businessProfileId })
        .maybeSingle(), // Use maybeSingle as user might not have reviewed
    ]);

    // Check for errors in parallel fetches
    if (subscriptionRes.error)
      throw new Error(
        `Subscription fetch error: ${subscriptionRes.error.message}`
      );
    if (likeRes.error)
      throw new Error(`Like fetch error: ${likeRes.error.message}`);
    if (reviewRes.error)
      throw new Error(`Review fetch error: ${reviewRes.error.message}`);

    const reviewData = reviewRes.data;

    return {
      isSubscribed: (subscriptionRes.count ?? 0) > 0,
      hasLiked: (likeRes.count ?? 0) > 0,
      userRating: reviewData?.rating ?? null,
      userReview: reviewData?.review_text ?? null,
    };
  } catch (error) {
    console.error("Error fetching interaction status:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred.";
    // Return default status but include the error message
    return { ...defaultStatus, error: errorMessage };
  }
}
