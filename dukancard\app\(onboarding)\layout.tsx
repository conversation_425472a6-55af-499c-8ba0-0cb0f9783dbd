import React from "react";
import <PERSON><PERSON><PERSON>eader from "@/app/components/MinimalHeader";
import { ThemeProvider } from "next-themes";
import { ThemeToggle } from "@/app/components/ThemeToggle"; // Import ThemeToggle
import BottomNav from "@/app/components/BottomNav"; // Import BottomNav
import { createClient } from "@/utils/supabase/server"; // Import server client

// Layout for onboarding pages

export default async function OnboardingLayout({ // Make component async
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient(); // Await the client creation
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Extract user name from metadata
  const userName = user?.user_metadata?.full_name ?? user?.user_metadata?.name ?? null;

  return (
    // Allow theme switching, remove forcedTheme
    <ThemeProvider attribute="class">
      {/* Use semantic background */}
      <div className="min-h-screen flex flex-col bg-background">
        {/* Pass userName and ThemeToggle with dashboard variant for icon-only */}
        <MinimalHeader userName={userName}>
          {/* MinimalHeader expects children in specific order: [She<PERSON>, <PERSON><PERSON>, ThemeToggle] */}
          {/* We only need ThemeToggle here, pass nulls for others if needed by MinimalHeader's logic */}
          {null} {/* Placeholder for mobileSheetTrigger */}
          {null} {/* Placeholder for desktopToggleButton */}
          <ThemeToggle variant="dashboard" />
        </MinimalHeader>
        <main className="flex-grow">
          {children}
        </main>
        <BottomNav />
      </div>
    </ThemeProvider>
  );
}