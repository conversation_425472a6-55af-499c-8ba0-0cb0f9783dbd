"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { AnimatePresence } from "framer-motion";
import ErrorDialog from "./components/landing/ErrorDialog";
import FeaturesSection from "./components/landing/FeaturesSection";
import PricingSection from "./components/landing/PricingSection";
import TestimonialsSection from "./components/landing/TestimonialsSection";
import CTASection from "./components/landing/CTASection";
import NewArrivalsSection from "./components/landing/NewArrivalsSection";
import PopularBusinessesSection from "./components/landing/PopularBusinessesSection";
import StickyHeroSectionClient from "./components/landing/StickyHeroSectionClient";
import SectionDivider from "./components/landing/SectionDivider";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import { createClient } from "@/utils/supabase/client";

// Helper function to determine user plan (same as public card page)
const getUserPlan = (
  profile: { subscription_status: string | null; plan_id: string | null }
): "free" | "basic" | "growth" | "pro" | "enterprise" | undefined => {
  // Simply return the plan_id from the subscription data
  switch (profile.plan_id) {
    case "free":
      return "free";
    case "growth":
      return "growth";
    case "pro":
      return "pro";
    case "enterprise":
      return "enterprise";
    case "basic":
      return "basic";
    default:
      return "free"; // Default to free if no plan_id specified
  }
};

export default function LandingPageClient() {
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isMounted, setIsMounted] = useState(false);

  // Authentication state
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userType, setUserType] = useState<"business" | "customer" | null>(null);
  const [businessCardData, setBusinessCardData] = useState<BusinessCardData | undefined>();
  const [userPlan, setUserPlan] = useState<"free" | "basic" | "growth" | "pro" | "enterprise" | "trial" | undefined>();
  const [authLoading, setAuthLoading] = useState(true);

  const searchParams = useSearchParams();
  const supabase = createClient();

  // Handle client-side mounting to prevent hydration errors
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Handle authentication client-side
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { user }, error: authError } = await supabase.auth.getUser();

        if (authError || !user) {
          setIsAuthenticated(false);
          setUserType(null);
          setAuthLoading(false);
          return;
        }

        setIsAuthenticated(true);

        // Check user type by looking at both profile tables
        const [customerRes, businessRes] = await Promise.all([
          supabase
            .from("customer_profiles")
            .select("id")
            .eq("id", user.id)
            .maybeSingle(),
          supabase
            .from("business_profiles")
            .select(`
              id, business_name, contact_email, has_active_subscription,
              trial_end_date, created_at, updated_at, logo_url, member_name, title,
              address_line, city, state, pincode, locality, phone, instagram_url,
              facebook_url, whatsapp_number, about_bio, status, business_slug,
              total_likes, total_subscriptions, average_rating, theme_color,
              delivery_info, business_hours, business_category, total_visits, established_year, google_maps_url,
              custom_branding, custom_ads
            `)
            .eq("id", user.id)
            .maybeSingle(),
        ]);

        if (customerRes.error || businessRes.error) {
          console.error("Error checking user type:", customerRes.error || businessRes.error);
          setUserType(null);
        } else if (customerRes.data) {
          setUserType("customer");
        } else if (businessRes.data) {
          setUserType("business");

          // Fetch subscription data separately and add to business profile data
          const { data: subscription } = await supabase
            .from("payment_subscriptions")
            .select("plan_id, subscription_status")
            .eq("business_profile_id", businessRes.data.id)
            .order("created_at", { ascending: false })
            .limit(1)
            .maybeSingle();

          // Add subscription data and transform data exactly like public card page
          const businessDataWithSubscription = {
            ...businessRes.data,
            subscription_status: subscription?.subscription_status || null,
            plan_id: subscription?.plan_id || null,
            // Add missing fields for schema compatibility (same as public card page)
            total_reviews: 0, // We don't fetch reviews count on homepage
            primary_phone: businessRes.data.phone,
            business_category: businessRes.data.business_category || "",
            // Ensure required fields are properly typed
            status: businessRes.data.status as "online" | "offline",
            title: businessRes.data.title || "",
            member_name: businessRes.data.member_name || "",
            business_name: businessRes.data.business_name || "",
            contact_email: businessRes.data.contact_email || "",
            // Convert date strings to Date objects
            created_at: businessRes.data.created_at
              ? new Date(businessRes.data.created_at)
              : undefined,
            updated_at: businessRes.data.updated_at
              ? new Date(businessRes.data.updated_at)
              : undefined,
            trial_end_date: businessRes.data.trial_end_date
              ? businessRes.data.trial_end_date // Keep as string
              : null,
          };

          setBusinessCardData(businessDataWithSubscription as BusinessCardData);

          // Use the same getUserPlan logic as public card page
          const plan = getUserPlan(businessDataWithSubscription);
          setUserPlan(plan);
        }
      } catch (error) {
        console.error("Unexpected error checking auth:", error);
        setIsAuthenticated(false);
        setUserType(null);
      } finally {
        setAuthLoading(false);
      }
    };

    checkAuth();
  }, [supabase]);

  // Check URL parameters for errors
  useEffect(() => {
    const error = searchParams.get("error");
    const errorCode = searchParams.get("error_code");
    const errorDescription = searchParams.get("error_description");

    if (error && errorDescription) {
      let message = decodeURIComponent(errorDescription);
      switch (errorCode) {
        case "otp_expired":
          message =
            "The email verification link has expired. Please try registering again.";
          break;
        case "access_denied":
          message = "Access was denied. " + message;
          break;
        default:
          message = "An error occurred: " + message;
      }
      setErrorMessage(message);
      setShowError(true);
    }
  }, [searchParams]);



  return (
    <div className="min-h-screen bg-white dark:bg-black relative">
      {/* Error Dialog */}
      <AnimatePresence>
        {showError && (
          <ErrorDialog
            open={showError}
            onOpenChange={setShowError}
            errorMessage={errorMessage}
          />
        )}
      </AnimatePresence>

      {/* Main content */}
      <div>
          {/* Hero Section with side-by-side layout - only render after client-side mounting and auth check */}
          {isMounted && !authLoading ? (
            <StickyHeroSectionClient
              isAuthenticated={isAuthenticated}
              userType={userType}
              businessCardData={businessCardData}
              userPlan={userPlan}
            />
          ) : (
            <div className="w-full h-[80vh] flex items-center justify-center">
              <div className="animate-pulse w-16 h-16 rounded-full bg-[var(--brand-gold)]/20"></div>
            </div>
          )}

        {/* Section divider */}
        <SectionDivider variant="gold" />

        {/* New Arrivals Section */}
        <NewArrivalsSection />

        {/* Section divider */}
        <SectionDivider variant="neutral" />

        {/* Popular Businesses Section */}
        <PopularBusinessesSection />

        {/* Section divider */}
        <SectionDivider variant="blue" />

        {/* Pricing Section */}
        <PricingSection />

        {/* Section divider */}
        <SectionDivider variant="gold" />

        {/* Features Section */}
        <FeaturesSection />

        {/* Section divider */}
        <SectionDivider variant="purple" />

        {/* Testimonials Section */}
        <TestimonialsSection />

        {/* Section divider */}
        <SectionDivider variant="blue" />

        {/* CTA Section */}
        <CTASection />
      </div>
    </div>
  );
}
