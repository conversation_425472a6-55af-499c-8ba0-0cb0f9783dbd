import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import { ProductServiceData } from "@/backend/supabase/services/products/types";
import { BusinessSortBy } from "@/lib/actions/businessProfiles";

// Define the structure for products found nearby
export type NearbyProduct = ProductServiceData & {
  business_slug: string | null;
  slug?: string;
};

// Define the overall return structure for the action
export type DiscoverSearchResult = {
  location?: { city: string; state: string } | null; // Optional for name search
  businesses?: BusinessCardData[]; // Now full data, optional
  products?: NearbyProduct[]; // Optional
  isAuthenticated: boolean; // Add authentication status
  totalCount: number; // Total count for pagination
  hasMore: boolean; // Whether there are more items to load
  nextPage: number | null; // Next page number or null if no more pages
};

// Helper function to get the column name for sorting
export function getSortingColumn(
  sortBy: BusinessSortBy | string,
  isProductView: boolean = false
): string {
  // For product view, we need to handle sorting differently
  if (isProductView) {
    switch (sortBy) {
      case "name_asc":
      case "name_desc":
        return "name";
      case "price_asc":
      case "price_desc":
        // We'll handle price sorting with a custom approach in the query
        return "price";
      case "newest":
        // Handle 'newest' as a special case - sort by created_at descending
        return "created_at";
      case "created_asc":
      case "created_desc":
      default:
        return "created_at";
    }
  } else {
    // For business view
    switch (sortBy) {
      case "name_asc":
      case "name_desc":
        return "name";
      case "price_asc":
      case "price_desc":
        return "base_price";
      case "likes_desc":
        return "likes_count";
      case "subscriptions_desc":
        return "subscriptions_count";
      case "rating_desc":
        return "average_rating";
      case "created_asc":
      case "created_desc":
      default:
        return "created_at";
    }
  }
}

// Helper function to determine sort direction
export function getSortingDirection(sortBy: BusinessSortBy | string): boolean {
  switch (sortBy) {
    case "name_asc":
    case "price_asc":
    case "created_asc":
      return true; // ascending
    case "name_desc":
    case "price_desc":
    case "likes_desc":
    case "subscriptions_desc":
    case "rating_desc":
    case "created_desc":
    case "newest": // 'newest' is equivalent to 'created_desc'
    default:
      return false; // descending
  }
}
