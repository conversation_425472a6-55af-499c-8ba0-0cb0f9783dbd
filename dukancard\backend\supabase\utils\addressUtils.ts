import { createClient } from '@/utils/supabase/client';

export interface PostAddress {
  locality: string;
  city: string;
  state: string;
  pincode: string;
}

/**
 * Fetch real address data from pincodes table using post slugs
 */
export async function fetchPostAddress(
  locality_slug?: string | null,
  city_slug?: string | null,
  state_slug?: string | null,
  pincode?: string | null
): Promise<PostAddress | null> {
  if (!pincode) {
    return null;
  }

  const supabase = createClient();

  try {
    // Build query conditions
    let query = supabase
      .from('pincodes')
      .select('OfficeName, DivisionName, StateName, Pincode')
      .eq('Pincode', pincode);

    // Add additional filters if available
    if (city_slug) {
      query = query.eq('city_slug', city_slug);
    }
    if (state_slug) {
      query = query.eq('state_slug', state_slug);
    }
    if (locality_slug) {
      query = query.eq('locality_slug', locality_slug);
    }

    const { data, error } = await query.limit(1);

    if (error) {
      console.error('Error fetching address data:', error);
      return null;
    }

    if (!data || data.length === 0) {
      // Fallback: try with just pincode
      const { data: fallbackData, error: fallbackError } = await supabase
        .from('pincodes')
        .select('OfficeName, DivisionName, StateName, Pincode')
        .eq('Pincode', pincode)
        .limit(1);

      if (fallbackError || !fallbackData || fallbackData.length === 0) {
        return null;
      }

      const record = fallbackData[0];
      return {
        locality: record.OfficeName || '',
        city: record.DivisionName || '',
        state: record.StateName || '',
        pincode: record.Pincode || '',
      };
    }

    const record = data[0];
    return {
      locality: record.OfficeName || '',
      city: record.DivisionName || '',
      state: record.StateName || '',
      pincode: record.Pincode || '',
    };
  } catch (error) {
    console.error('Error in fetchPostAddress:', error);
    return null;
  }
}

/**
 * Format address parts into a readable string
 */
export function formatAddressString(address: PostAddress): string {
  const parts = [];
  
  if (address.locality) parts.push(address.locality);
  if (address.city) parts.push(address.city);
  if (address.state) parts.push(address.state);
  if (address.pincode) parts.push(address.pincode);
  
  return parts.join(', ');
}

/**
 * Fallback function to format address from slugs (for when API fails)
 */
export function formatAddressFromSlugs(
  locality_slug?: string | null,
  city_slug?: string | null,
  state_slug?: string | null,
  pincode?: string | null
): string {
  const addressParts = [];
  
  if (locality_slug) {
    addressParts.push(locality_slug.replace(/-/g, ' '));
  }
  if (city_slug) {
    addressParts.push(city_slug.replace(/-/g, ' '));
  }
  if (state_slug) {
    addressParts.push(state_slug.replace(/-/g, ' '));
  }
  if (pincode) {
    addressParts.push(pincode);
  }
  
  return addressParts.join(', ');
}
