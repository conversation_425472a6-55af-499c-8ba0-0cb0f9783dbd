"use client";

import { Suspense, useState, useEffect } from "react";
import { Loader2 } from "lucide-react";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import {
  import { ProductServiceData, ProductSortBy } from "@/backend/supabase/services/products/types";
} from "@/app/(dashboard)/dashboard/business/products/actions";

import { GalleryImage } from "@/lib/actions/gallery";
import { AdData } from "@/types/ad";
import EnhancedPublicCardPageWrapper from "./components/EnhancedPublicCardPageWrapper";

// Define the BusinessProfile type
type BusinessProfile = BusinessCardData & {
  total_reviews?: number;
  subscription_status?: string;
  has_active_subscription?: boolean;
  trial_end_date?: Date | string | null;
};

interface PublicCardPageClientProps {
  businessProfile: BusinessProfile;
  initialProducts: ProductServiceData[];
  totalProductCount: number;
  defaultSortPreference: ProductSortBy;
  isAuthenticated: boolean;
  currentUserId: string | null;
  userPlan: "free" | "basic" | "growth" | "pro" | "enterprise" | "trial" | undefined;
  topAdData: AdData;

  galleryImages?: GalleryImage[];
  galleryTotalCount?: number;
}

export default function PublicCardPageClient({
  businessProfile,
  initialProducts,
  totalProductCount,
  defaultSortPreference,
  isAuthenticated,
  currentUserId,
  userPlan,
  topAdData,

  galleryImages = [],
  galleryTotalCount = 0,
}: PublicCardPageClientProps) {
  const [isLoading, setIsLoading] = useState(true);

  // Set loading to false after component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 300); // Short delay to ensure the loader is visible

    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      {/* Always show loader initially, then fade it out */}
      <div
        className={`fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50 transition-opacity duration-300 ${isLoading ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
      >
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-[var(--brand-gold)]" />
          <p className="text-sm text-muted-foreground">Loading business card...</p>
        </div>
      </div>

      <Suspense fallback={
        <div className="fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50">
          <div className="flex flex-col items-center gap-2">
            <Loader2 className="h-8 w-8 animate-spin text-[var(--brand-gold)]" />
            <p className="text-sm text-muted-foreground">Loading business card...</p>
          </div>
        </div>
      }>
        <EnhancedPublicCardPageWrapper
          businessProfile={businessProfile}
          initialProducts={initialProducts}
          totalProductCount={totalProductCount}
          defaultSortPreference={defaultSortPreference}
          isAuthenticated={isAuthenticated}
          currentUserId={currentUserId}
          userPlan={userPlan}
          topAdData={topAdData}

          galleryImages={galleryImages}
          galleryTotalCount={galleryTotalCount}
        />
      </Suspense>
    </>
  );
}
