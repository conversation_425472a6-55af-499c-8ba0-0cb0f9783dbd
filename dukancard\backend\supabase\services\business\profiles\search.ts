"use server";

import { createClient } from "@/utils/supabase/server";
import { BusinessProfilePublicData, BusinessSortBy } from "./types";
import { applySorting, createSubscriptionMap, getCurrentISOTimestamp } from "./utils";
import { createAdminClient } from "@/utils/supabase/admin";

/**
 * Securely fetch all business profiles or search by name and/or location
 */
export async function getSecureBusinessProfiles(
  searchTerm?: string | null,
  pincode?: string | null,
  locality?: string | null,
  page: number = 1,
  limit: number = 20,
  sortBy: BusinessSortBy = "created_desc"
): Promise<{
  data?: BusinessProfilePublicData[];
  count?: number;
  error?: string;
}> {
  try {
    // Use regular client with public read RLS policies
    const supabase = await createClient();
    const supabaseAdmin = await createAdminClient()
    const _nowISO = getCurrentISOTimestamp();
    const offset = (page - 1) * limit;

    // Define fields to select
    const businessFields = `
      id, business_name, logo_url, member_name, title,
      address_line, city, state, pincode, locality, phone, instagram_url,
      facebook_url, whatsapp_number, about_bio, status, business_slug, theme_color,
      delivery_info, total_likes, total_subscriptions, average_rating, business_hours,
      business_category, trial_end_date, created_at, updated_at, contact_email, established_year, google_maps_url,
      custom_branding, custom_ads
    `;

    // Build the base query - only check for online status for businesses
    let countQuery = supabase
      .from("business_profiles")
      .select("id", { count: "exact" })
      .eq("status", "online");

    let businessQuery = supabase
      .from("business_profiles")
      .select(businessFields)
      .eq("status", "online");

    // Add search term filter if provided
    if (searchTerm) {
      const formattedSearchTerm = searchTerm.trim();
      if (formattedSearchTerm) {
        // Use ilike for case-insensitive search
        countQuery = countQuery.ilike(
          "business_name",
          `%${formattedSearchTerm}%`
        );
        businessQuery = businessQuery.ilike(
          "business_name",
          `%${formattedSearchTerm}%`
        );
      }
    }

    // Add pincode filter if provided
    if (pincode) {
      countQuery = countQuery.eq("pincode", pincode);
      businessQuery = businessQuery.eq("pincode", pincode);
    }

    // Add locality filter if provided
    if (locality) {
      countQuery = countQuery.eq("locality", locality);
      businessQuery = businessQuery.eq("locality", locality);
    }

    // Apply sorting
    businessQuery = applySorting(businessQuery, sortBy);

    // Apply pagination
    businessQuery = businessQuery.range(offset, offset + limit - 1);

    // Execute the queries
    const [countResult, dataResult] = await Promise.all([
      countQuery,
      businessQuery,
    ]);

    const { count, error: countError } = countResult;
    const { data, error: dataError } = dataResult;

    if (countError) {
      console.error("Count Error:", countError);
      return { error: "Database error counting profiles." };
    }

    if (dataError) {
      console.error("Data Error:", dataError);
      return { error: "Database error fetching profiles." };
    }

    // If there are no profiles, return empty array
    if (!count || count === 0 || !data || data.length === 0) {
      return { data: [], count: 0 };
    }

    // Get subscription data for all profiles
    const profileIds = data.map(profile => profile.id);

    // Fetch subscription data for all profiles
    const { data: subscriptionsData, error: subscriptionsError } = await supabaseAdmin
      .from("payment_subscriptions")
      .select("business_profile_id, subscription_status, plan_id")
      .in("business_profile_id", profileIds)
      .order("created_at", { ascending: false });

    if (subscriptionsError) {
      console.error("Error fetching subscription data:", subscriptionsError);
    }

    // Create a map of business_profile_id to subscription data
    const subscriptionMap = createSubscriptionMap(subscriptionsData);

    // Filter out sensitive data before returning
    const safeData: BusinessProfilePublicData[] = data.map((profile) => {
      // Get subscription data for this profile
      const subData = subscriptionMap.get(profile.id) || {
        subscription_status: null,
        plan_id: null
      };

      return {
        id: profile.id,
        business_name: profile.business_name,
        logo_url: profile.logo_url,
        member_name: profile.member_name,
        title: profile.title,
        address_line: profile.address_line,
        city: profile.city,
        state: profile.state,
        pincode: profile.pincode,
        locality: profile.locality,
        phone: profile.phone,
        instagram_url: profile.instagram_url,
        facebook_url: profile.facebook_url,
        whatsapp_number: profile.whatsapp_number,
        about_bio: profile.about_bio,
        status: profile.status,
        business_slug: profile.business_slug,
        theme_color: profile.theme_color,
        delivery_info: profile.delivery_info,
        business_hours: profile.business_hours,
        business_category: profile.business_category,
        google_maps_url: profile.google_maps_url,
        established_year: profile.established_year,
        total_likes: profile.total_likes,
        total_subscriptions: profile.total_subscriptions,
        average_rating: profile.average_rating,
        custom_branding: profile.custom_branding,
        custom_ads: profile.custom_ads,
        created_at: profile.created_at,
        updated_at: profile.updated_at,
        trial_end_date: profile.trial_end_date,
        contact_email: profile.contact_email,
        subscription_status: subData.subscription_status,
        plan_id: subData.plan_id
      };
    });

    return { data: safeData, count };
  } catch (e) {
    console.error("Exception in getSecureBusinessProfiles:", e);
    return { error: "An unexpected error occurred." };
  }
}
