import { Skeleton } from "@/components/ui/skeleton";
import { Card } from "@/components/ui/card";

export default function BlogListingSkeleton() {
  return (
    <div className="bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header Skeleton */}
        <div className="text-center mb-12">
          <Skeleton className="h-12 w-48 mx-auto mb-4" />
          <Skeleton className="h-6 w-96 mx-auto" />
        </div>

        {/* Search and Filter Skeleton */}
        <div className="mb-8 space-y-4">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            {/* Search Input Skeleton */}
            <Skeleton className="h-10 w-full md:w-96" />
            
            {/* Sort Filter Skeleton */}
            <Skeleton className="h-10 w-48" />
          </div>
        </div>

        {/* Blog Grid Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {Array.from({ length: 12 }).map((_, index) => (
            <Card key={index} className="overflow-hidden">
              {/* Featured Image Skeleton */}
              <Skeleton className="h-48 w-full" />
              
              <div className="p-6">
                {/* Categories Skeleton */}
                <div className="flex gap-2 mb-3">
                  <Skeleton className="h-5 w-16" />
                  <Skeleton className="h-5 w-20" />
                </div>
                
                {/* Title Skeleton */}
                <Skeleton className="h-6 w-full mb-3" />
                <Skeleton className="h-6 w-3/4 mb-4" />
                
                {/* Excerpt Skeleton */}
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3 mb-4" />
                
                {/* Meta Info Skeleton */}
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-4">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                  <Skeleton className="h-4 w-24" />
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Pagination Skeleton */}
        <div className="flex justify-center">
          <div className="flex items-center gap-2">
            <Skeleton className="h-10 w-20" />
            <Skeleton className="h-10 w-10" />
            <Skeleton className="h-10 w-10" />
            <Skeleton className="h-10 w-10" />
            <Skeleton className="h-10 w-16" />
          </div>
        </div>
      </div>
    </div>
  );
}

export function BlogCardSkeleton() {
  return (
    <Card className="overflow-hidden">
      {/* Featured Image Skeleton */}
      <Skeleton className="h-48 w-full" />
      
      <div className="p-6">
        {/* Categories Skeleton */}
        <div className="flex gap-2 mb-3">
          <Skeleton className="h-5 w-16" />
          <Skeleton className="h-5 w-20" />
        </div>
        
        {/* Title Skeleton */}
        <Skeleton className="h-6 w-full mb-3" />
        <Skeleton className="h-6 w-3/4 mb-4" />
        
        {/* Excerpt Skeleton */}
        <Skeleton className="h-4 w-full mb-2" />
        <Skeleton className="h-4 w-full mb-2" />
        <Skeleton className="h-4 w-2/3 mb-4" />
        
        {/* Meta Info Skeleton */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-4">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-16" />
          </div>
          <Skeleton className="h-4 w-24" />
        </div>
      </div>
    </Card>
  );
}
