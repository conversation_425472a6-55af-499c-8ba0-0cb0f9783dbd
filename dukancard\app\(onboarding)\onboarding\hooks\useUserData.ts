"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import { toast } from "sonner";
import { User } from "../types/onboarding";

export function useUserData() {
  const router = useRouter();
  const supabase = createClient();
  const [user, setUser] = useState<User | null>(null);

  // Fetch user on mount
  useEffect(() => {
    const getUser = async () => {
      const sessionResponse = await supabase.auth.getSession();
      const session = sessionResponse?.data?.session;
      const sessionError = sessionResponse?.error;

      if (sessionError || !session?.user) {
        toast.error("Authentication error. Redirecting to login.");
        router.push("/login");
        return;
      }
      setUser(session.user);
    };
    getUser();
  }, [router, supabase.auth]);

  return { user };
}
