"use client";

import { <PERSON>, X, <PERSON><PERSON><PERSON><PERSON><PERSON>, Filter } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useProducts } from "../../context/ProductsContext";
import { ProductSortBy } from "@/backend/supabase/services/products/types";

export default function ProductFilters() {
  const {
    searchTerm,
    setSearchTerm,
    filterAvailable,
    setFilterAvailable,
    sortBy,
    setSortBy,
    isLoading,
    isPending
  } = useProducts();

  return (
    <div className="flex flex-col gap-3 sm:gap-4">
      {/* Top row: Search on left, Sort and Availability on right */}
      <div className="flex flex-col lg:flex-row gap-3 sm:gap-4">
        {/* Left side - Search */}
        <div className="w-full lg:w-1/2 space-y-1 sm:space-y-2">
          <Label
            htmlFor="search-products"
            className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5"
          >
            <Search className="h-3.5 w-3.5 text-primary" />
            Search Products
          </Label>

          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400 dark:text-neutral-500 pointer-events-none" />

            <Input
              id="search-products"
              type="text"
              placeholder="Search by name or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-2 sm:py-3 md:py-4 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
              disabled={isLoading}
            />

            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 text-neutral-400 dark:text-neutral-500 hover:text-neutral-600 dark:hover:text-neutral-300"
                onClick={() => setSearchTerm("")}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        {/* Right side - Sort and Availability */}
        <div className="w-full lg:w-1/2 flex flex-col sm:flex-row gap-3 sm:gap-4">
          {/* Filter by Availability */}
          <div className="w-full sm:w-1/2 flex items-end">
            <div className="w-full space-y-1 sm:space-y-2">
              <Label
                htmlFor="filter-available"
                className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5"
              >
                <Filter className="h-3.5 w-3.5 text-primary" />
                Availability
              </Label>
              <div className="flex items-center space-x-3">
                <Switch
                  id="filter-available"
                  checked={filterAvailable ?? false}
                  onCheckedChange={(checked) =>
                    setFilterAvailable(checked ? true : undefined)
                  }
                  disabled={isLoading}
                  className="data-[state=checked]:bg-primary"
                />
                <Label htmlFor="filter-available" className="text-xs sm:text-sm text-neutral-600 dark:text-neutral-400 cursor-pointer">
                  Show Available Only
                </Label>
              </div>
            </div>
          </div>

          {/* Sort By */}
          <div className="w-full sm:w-1/2">
            <Label
              htmlFor="sort-by"
              className="text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5 mb-1 sm:mb-2"
            >
              <ArrowUpDown className="h-3.5 w-3.5 text-primary" />
              Sort By
            </Label>
            <Select
              value={sortBy}
              onValueChange={(value) => setSortBy(value as ProductSortBy)}
              disabled={isLoading || isPending}
            >
              <SelectTrigger
                id="sort-by"
                className="w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-2 sm:py-3 md:py-4 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200"
              >
                <SelectValue placeholder="Sort by..." />
              </SelectTrigger>
              <SelectContent className="border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg bg-white dark:bg-neutral-900">
                <SelectItem value="created_desc" className="text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20">Date Added (Newest)</SelectItem>
                <SelectItem value="created_asc" className="text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20">Date Added (Oldest)</SelectItem>
                <SelectItem value="updated_desc" className="text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20">Last Updated</SelectItem>
                <SelectItem value="price_asc" className="text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20">Price (Low to High)</SelectItem>
                <SelectItem value="price_desc" className="text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20">Price (High to Low)</SelectItem>
                <SelectItem value="name_asc" className="text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20">Name (A-Z)</SelectItem>
                <SelectItem value="name_desc" className="text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20">Name (Z-A)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Bottom row: Reset Filters */}
      <div className="flex justify-end">
        <Button
          variant="outline"
          size="default"
          onClick={() => {
            setSearchTerm("");
            setFilterAvailable(undefined);
            setSortBy("created_desc");
          }}
          disabled={!(searchTerm || filterAvailable !== undefined || sortBy !== "created_desc")}
          className="text-xs sm:text-sm rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-2 sm:py-2.5 px-3 sm:px-4 text-neutral-600 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-all duration-200"
        >
          <X className="mr-1.5 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4" />
          Reset Filters
        </Button>
      </div>
    </div>
  );
}
