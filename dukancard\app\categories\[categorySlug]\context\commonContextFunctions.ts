"use client";

import { useSearchParams } from "next/navigation";
import { useTransition } from "react";
import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { searchCategoryCombined } from "../actions";
import { BusinessProfilePublicData } from "@/lib/actions/businessProfiles/types";
import { NearbyProduct } from "@/app/(main)/discover/actions/types";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import {
  CATEGORY_BUSINESSES_PER_PAGE,
  CATEGORY_PRODUCTS_PER_PAGE,
} from "../constants/paginationConstants";
import {
  VIEW_TYPE_PARAM,
  BUSINESS_NAME_PARAM,
  PRODUCT_NAME_PARAM,
  PINCODE_PARAM,
  CITY_PARAM,
  LOCALITY_PARAM,
} from "@/app/(main)/discover/constants/urlParamConstants";
import {
  CombinedSearchFormData,
  CategorySearchResult,
  ProductFilterOption,
  ProductSortOption,
  ViewType,
  SerializableCategory,
} from "./types";

// Common context functions
export function useCommonContextFunctions(
  category: SerializableCategory,
  viewType: ViewType,
  setViewType: (_value: ViewType) => void,
  setIsSearching: (_value: boolean) => void,
  setSearchResult: (_value: CategorySearchResult | null) => void,
  setIsAuthenticated: (_value: boolean) => void,
  setBusinesses: (_value: (BusinessProfilePublicData | BusinessCardData)[]) => void,
  setProducts: (_value: NearbyProduct[]) => void,
  setHasMore: (_value: boolean) => void,
  setTotalCount: (_value: number) => void,
  setCurrentPage: (_value: number) => void,
  setSearchError: (_value: string | null) => void,
  businessSort: BusinessSortBy,
  productSort: ProductSortOption,
  productFilterBy: ProductFilterOption,
  locationInfo?: { state?: string | null; city?: string | null; pincode?: string | null; locality?: string | null }
) {
  const searchParams = useSearchParams();
  const [isPending, startSearchTransition] = useTransition();

  // Handle view type change
  const handleViewChange = (view: ViewType) => {
    if (view === viewType) return;

    // Update URL params
    const url = new URL(window.location.href);
    url.searchParams.set(VIEW_TYPE_PARAM, view);
    window.history.pushState({}, "", url.toString());

    // Update state
    setViewType(view);

    // Get current search params
    const businessName = searchParams.get(BUSINESS_NAME_PARAM);
    const productName = searchParams.get(PRODUCT_NAME_PARAM);
    const pincode = searchParams.get(PINCODE_PARAM);
    const city = searchParams.get(CITY_PARAM);
    const locality = searchParams.get(LOCALITY_PARAM);

    // Set loading state
    setIsSearching(true);

    startSearchTransition(async () => {
      try {
        // Add a small delay to prevent race conditions
        await new Promise((resolve) => setTimeout(resolve, 100));

        // If switching to products view, clear businesses
        if (view === "products") {
          setBusinesses([]);
        }
        // If switching to businesses view, clear products
        else {
          setProducts([]);
        }

        // Use values from locationInfo if they're not in the URL params
        const effectivePincode = pincode || locationInfo?.pincode || null;
        const effectiveCity = city || locationInfo?.city || null;
        const effectiveLocality = locality || locationInfo?.locality || null;

        // Extract state and city slugs from the URL
        const urlParts = window.location.pathname.split('/').filter(part => part.trim() !== '');

        // Find the index of the category slug
        const categoryIndex = urlParts.findIndex(part => part === category.slug);

        // Get stateSlug, citySlug, and localitySlug from URL if available
        let stateSlugFromUrl = null;
        let citySlugFromUrl = null;
        let localitySlugFromUrl = null;

        if (categoryIndex >= 0 && categoryIndex + 1 < urlParts.length) {
            stateSlugFromUrl = urlParts[categoryIndex + 1];
        } else if (locationInfo?.state) {
            // If state slug is not in URL but we have state name, convert it to slug
            stateSlugFromUrl = locationInfo.state.toLowerCase().replace(/\s+/g, '-');
        }

        if (categoryIndex >= 0 && categoryIndex + 2 < urlParts.length) {
            citySlugFromUrl = urlParts[categoryIndex + 2];
        } else if (locationInfo?.city) {
            // If city slug is not in URL but we have city name, convert it to slug
            citySlugFromUrl = locationInfo.city.toLowerCase().replace(/\s+/g, '-');
        }

        // Check for pincode (needed to extract locality slug)
        if (categoryIndex >= 0 && categoryIndex + 3 < urlParts.length) {
            // pincodeFromUrl = urlParts[categoryIndex + 3]; // Currently not used
        }

        // Check for locality slug
        if (categoryIndex >= 0 && categoryIndex + 4 < urlParts.length) {
            localitySlugFromUrl = urlParts[categoryIndex + 4];
        }



        const result = await searchCategoryCombined({
          categoryName: category.name,
          businessName: view === "cards" ? businessName : null,
          productName: view === "products" ? productName : null,
          state: locationInfo?.state || null, // Include state from locationInfo
          pincode: effectivePincode, // Use the effective pincode
          city: effectiveCity, // Use the effective city
          locality: effectiveLocality, // Use the effective locality
          stateSlug: stateSlugFromUrl, // Pass state slug from URL
          citySlug: citySlugFromUrl, // Pass city slug from URL
          localitySlug: localitySlugFromUrl, // Pass locality slug from URL
          viewType: view,
          page: 1,
          limit:
            view === "products"
              ? CATEGORY_PRODUCTS_PER_PAGE
              : CATEGORY_BUSINESSES_PER_PAGE,
          businessSort,
          productSort,
          productType:
            view === "products" && productFilterBy !== "all"
              ? productFilterBy
              : null,
        });

        if (result.data) {
          setSearchResult(result.data);
          setIsAuthenticated(result.data.isAuthenticated);

          if (view === "cards") {
            setBusinesses(result.data.businesses || []);
          } else {
            setProducts(result.data.products || []);
          }

          setHasMore(result.data.hasMore);
          setTotalCount(result.data.totalCount);
          setCurrentPage(result.data.nextPage ? 2 : 1);
        } else if (result.error) {
          setSearchError(result.error);
        }
      } catch (error) {
        setSearchError("An error occurred while changing view.");
        console.error("Error changing view:", error);
      } finally {
        setIsSearching(false);
      }
    });
  };

  // Perform search with combined parameters
  const performSearch = async (data: CombinedSearchFormData) => {
    const { businessName, pincode, city, locality } = data;

    // Update URL params
    const url = new URL(window.location.href);
    if (businessName) url.searchParams.set(BUSINESS_NAME_PARAM, businessName);
    else url.searchParams.delete(BUSINESS_NAME_PARAM);

    if (pincode) url.searchParams.set(PINCODE_PARAM, pincode);
    else url.searchParams.delete(PINCODE_PARAM);

    if (city) url.searchParams.set(CITY_PARAM, city);
    else url.searchParams.delete(CITY_PARAM);

    if (locality) url.searchParams.set(LOCALITY_PARAM, locality);
    else url.searchParams.delete(LOCALITY_PARAM);

    window.history.pushState({}, "", url.toString());

    // Set loading state
    setIsSearching(true);

    startSearchTransition(async () => {
      try {
        // Add a small delay to prevent race conditions
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Use values from locationInfo if they're not in the URL params
        const effectivePincode = pincode || locationInfo?.pincode || null;
        const effectiveCity = city || locationInfo?.city || null;
        const effectiveLocality = locality === "_any" ? "" : (locality || locationInfo?.locality || null);

        // Extract state and city slugs from the URL
        const urlParts = window.location.pathname.split('/').filter(part => part.trim() !== '');

        // Find the index of the category slug
        const categoryIndex = urlParts.findIndex(part => part === category.slug);

        // Get stateSlug, citySlug, and localitySlug from URL if available
        let stateSlugFromUrl = null;
        let citySlugFromUrl = null;
        let localitySlugFromUrl = null;

        if (categoryIndex >= 0 && categoryIndex + 1 < urlParts.length) {
            stateSlugFromUrl = urlParts[categoryIndex + 1];
        }

        if (categoryIndex >= 0 && categoryIndex + 2 < urlParts.length) {
            citySlugFromUrl = urlParts[categoryIndex + 2];
        }

        // Check for pincode (needed to extract locality slug)
        if (categoryIndex >= 0 && categoryIndex + 3 < urlParts.length) {
            // pincodeFromUrl = urlParts[categoryIndex + 3]; // Currently not used
        }

        // Check for locality slug
        if (categoryIndex >= 0 && categoryIndex + 4 < urlParts.length) {
            localitySlugFromUrl = urlParts[categoryIndex + 4];
        }



        const result = await searchCategoryCombined({
          categoryName: category.name,
          businessName: viewType === "cards" ? businessName : null,
          productName: viewType === "products" ? businessName : null, // Use businessName for product search too
          state: locationInfo?.state || null, // Include state from locationInfo
          pincode: effectivePincode, // Use the effective pincode
          city: effectiveCity, // Use the effective city
          locality: effectiveLocality, // Use the effective locality
          stateSlug: stateSlugFromUrl, // Pass state slug from URL
          citySlug: citySlugFromUrl, // Pass city slug from URL
          localitySlug: localitySlugFromUrl, // Pass locality slug from URL
          viewType,
          page: 1,
          limit:
            viewType === "products"
              ? CATEGORY_PRODUCTS_PER_PAGE
              : CATEGORY_BUSINESSES_PER_PAGE,
          businessSort,
          productSort,
          productType:
            viewType === "products" && productFilterBy !== "all"
              ? productFilterBy
              : null,
        });

        if (result.data) {
          setSearchResult(result.data);
          setIsAuthenticated(result.data.isAuthenticated);

          if (viewType === "cards") {
            setBusinesses(result.data.businesses || []);
          } else {
            setProducts(result.data.products || []);
          }

          setHasMore(result.data.hasMore);
          setTotalCount(result.data.totalCount);
          setCurrentPage(result.data.nextPage ? 2 : 1);
        } else if (result.error) {
          setSearchError(result.error);
        }
      } catch (error) {
        setSearchError("An error occurred while searching.");
        console.error("Error searching:", error);
      } finally {
        setIsSearching(false);
      }
    });
  };

  // Common load more function
  const loadMore = async (
    loadMoreBusinesses: () => Promise<void>,
    loadMoreProducts: () => Promise<void>,
    currentPage: number,
    isLoadingMore: boolean,
    setIsLoadingMore: (_value: boolean) => void
  ) => {
    if (isLoadingMore || !currentPage) return;

    setIsLoadingMore(true);

    try {
      if (viewType === "cards") {
        await loadMoreBusinesses();
      } else {
        await loadMoreProducts();
      }
    } catch (error) {
      console.error("Error loading more items:", error);
      setSearchError("Failed to load more items.");
    } finally {
      setIsLoadingMore(false);
    }
  };

  return {
    isPending,
    handleViewChange,
    performSearch,
    loadMore,
  };
}
