"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2, Trash2 } from "lucide-react";
import { toast } from "sonner";
import { deletePost } from "@/lib/actions/posts";
import { motion, AnimatePresence } from "framer-motion";

export interface PostDeleteDialogProps {
  isOpen: boolean;
  onOpenChange: (_open: boolean) => void;
  postId: string;
  postContent: string;
  onDeleteSuccess?: () => void;
}

export default function PostDeleteDialog({
  isOpen,
  onOpenChange,
  postId,
  postContent: _postContent,
  onDeleteSuccess,
}: PostDeleteDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    setIsDeleting(true);

    try {
      const result = await deletePost(postId);

      if (result.success) {
        toast.success("Post deleted successfully", {
          description: "Your post and associated media have been removed."
        });
        onDeleteSuccess?.();
        onOpenChange(false);
      } else {
        toast.error("Failed to delete post", {
          description: result.error || "Please try again."
        });
      }
    } catch (error) {
      console.error("Error deleting post:", error);
      toast.error("An unexpected error occurred", {
        description: "Please try again later."
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="text-center pb-2">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-50 dark:bg-red-950/20">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.1, type: "spring", stiffness: 200 }}
            >
              <Trash2 className="h-8 w-8 text-red-500" />
            </motion.div>
          </div>
          <DialogTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Delete this post?
          </DialogTitle>
          <DialogDescription className="text-gray-500 dark:text-gray-400 mt-2">
            This action cannot be undone.
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="flex flex-col-reverse sm:flex-row gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isDeleting}
            className="flex-1 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200"
          >
            Cancel
          </Button>

          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="flex-1"
          >
            <Button
              type="button"
              onClick={handleDelete}
              disabled={isDeleting}
              className={`
                w-full relative overflow-hidden
                bg-gradient-to-r from-red-500 to-red-600
                hover:from-red-600 hover:to-red-700
                text-white font-medium
                shadow-lg hover:shadow-xl
                transition-all duration-300
                before:absolute before:inset-0
                before:bg-gradient-to-r before:from-red-400 before:to-red-500
                before:opacity-0 hover:before:opacity-20
                before:transition-opacity before:duration-300
                ${isDeleting ? 'cursor-not-allowed opacity-80' : ''}
              `}
              style={{
                boxShadow: isDeleting
                  ? '0 4px 20px rgba(239, 68, 68, 0.3)'
                  : '0 4px 20px rgba(239, 68, 68, 0.4), 0 0 20px rgba(239, 68, 68, 0.2)'
              }}
            >
              <AnimatePresence mode="wait">
                {isDeleting ? (
                  <motion.div
                    key="deleting"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className="flex items-center justify-center"
                  >
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Deleting...
                  </motion.div>
                ) : (
                  <motion.div
                    key="delete"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className="flex items-center justify-center"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Post
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </motion.div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Trigger button component for easier usage
export interface PostDeleteButtonProps {
  postId: string;
  postContent: string;
  onDeleteSuccess?: () => void;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}

export function PostDeleteButton({
  postId,
  postContent,
  onDeleteSuccess,
  variant = "ghost",
  size = "sm",
  className = "",
}: PostDeleteButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={() => setIsDialogOpen(true)}
        className={className}
      >
        <Trash2 className="h-4 w-4" />
      </Button>

      <PostDeleteDialog
        isOpen={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        postId={postId}
        postContent={postContent}
        onDeleteSuccess={onDeleteSuccess}
      />
    </>
  );
}
