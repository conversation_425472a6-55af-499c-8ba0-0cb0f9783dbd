"use server";

import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { BusinessProfilePublicData, BusinessProfileWithProducts } from "./types";

/**
 * Securely fetch a business profile by slug using the service role key
 * This bypasses RLS and ensures sensitive data is not exposed to the client
 */
export async function getSecureBusinessProfileBySlug(slug: string): Promise<{
  data?: BusinessProfilePublicData;
  error?: string;
}> {
  if (!slug) {
    return { error: "Business slug is required." };
  }

  try {
    // Use regular client with public read RLS policies
    const supabase = await createClient();

    // Fetch the business profile with subscription data - don't filter by status to allow testing
    const { data: profileData, error: profileError } = await supabase
      .from("business_profiles")
      .select(`
        *,
        custom_branding,
        custom_ads,
        payment_subscriptions!business_profile_id (
          plan_id,
          subscription_status,
          subscription_start_date,
          subscription_expiry_time,
          created_at
        )
      `)
      .eq("business_slug", slug)
      .maybeSingle();

    if (profileError) {
      console.error("Secure Fetch Error:", profileError);
      return { error: `Failed to fetch business profile: ${profileError.message}` };
    }

    if (!profileData) {
      return { error: "Profile not found." };
    }

    // Fetch subscription data using admin client
    const supabaseAdmin = await createAdminClient();
    const { data: subscriptionData, error: subscriptionError } = await supabaseAdmin
      .from("payment_subscriptions")
      .select("subscription_status, plan_id")
      .eq("business_profile_id", profileData.id)
      .order("created_at", { ascending: false })
      .limit(1)
      .maybeSingle();

    if (subscriptionError) {
      console.error("Subscription Fetch Error:", subscriptionError);
      // Continue without subscription data
    }

    // Combine profile and subscription data
    const data = {
      ...profileData,
      subscription_status: subscriptionData?.subscription_status || null,
      plan_id: subscriptionData?.plan_id || null
    };

    // Error handling already done above

    // Filter out sensitive data before returning
    const safeData: BusinessProfilePublicData = {
      id: data.id,
      business_name: data.business_name,
      logo_url: data.logo_url,
      member_name: data.member_name,
      title: data.title,
      address_line: data.address_line,
      city: data.city,
      state: data.state,
      pincode: data.pincode,
      locality: data.locality,
      phone: data.phone,
      instagram_url: data.instagram_url,
      facebook_url: data.facebook_url,
      whatsapp_number: data.whatsapp_number,
      about_bio: data.about_bio,
      status: data.status,
      business_slug: data.business_slug,
      theme_color: data.theme_color,
      delivery_info: data.delivery_info,
      business_hours: data.business_hours,
      business_category: data.business_category,
      established_year: data.established_year,
      total_likes: data.total_likes,
      total_subscriptions: data.total_subscriptions,
      average_rating: data.average_rating,
      created_at: data.created_at,
      updated_at: data.updated_at,
      trial_end_date: data.trial_end_date,
      contact_email: data.contact_email,
      google_maps_url: data.google_maps_url,
      subscription_status: data.subscription_status,
      plan_id: data.plan_id,
      custom_branding: data.custom_branding,
      custom_ads: data.custom_ads
    };

    return { data: safeData };
  } catch (e) {
    console.error("Exception in getSecureBusinessProfileBySlug:", e);
    return { error: "An unexpected error occurred." };
  }
}

/**
 * Securely fetch a business profile with products by slug using the service role key
 */
export async function getSecureBusinessProfileWithProductsBySlug(
  slug: string
): Promise<{
  data?: BusinessProfileWithProducts;
  error?: string;
}> {
  if (!slug) {
    return { error: "Business slug is required." };
  }

  try {
    // Use the admin client with service role key to bypass RLS
    const supabaseAdmin = await createAdminClient();

    // Fetch the business profile with products - don't filter by status to allow testing
    const { data: profileData, error: profileError } = await supabaseAdmin
      .from("business_profiles")
      .select(
        `
        id, business_name, logo_url, member_name, title,
        address_line, city, state, pincode, locality, phone, instagram_url,
        facebook_url, whatsapp_number, about_bio, status, business_slug,
        theme_color, delivery_info, business_hours, business_category, total_likes, total_subscriptions,
        average_rating, created_at, updated_at, trial_end_date, contact_email, established_year, google_maps_url,
        custom_branding, custom_ads,
        products_services (
          id, name, description, base_price, discounted_price, is_available, image_url, created_at, updated_at, product_type
        )
      `
      )
      .eq("business_slug", slug)
      .maybeSingle();

    if (profileError) {
      console.error("Secure Fetch Error:", profileError);
      return { error: `Failed to fetch business profile: ${profileError.message}` };
    }

    if (!profileData) {
      return { error: "Profile not found." };
    }

    // Fetch subscription data
    const { data: subscriptionData, error: subscriptionError } = await supabaseAdmin
      .from("payment_subscriptions")
      .select("subscription_status, plan_id")
      .eq("business_profile_id", profileData.id)
      .order("created_at", { ascending: false })
      .limit(1)
      .maybeSingle();

    if (subscriptionError) {
      console.error("Subscription Fetch Error:", subscriptionError);
      // Continue without subscription data
    }

    // Combine profile and subscription data
    const data = {
      ...profileData,
      subscription_status: subscriptionData?.subscription_status || null,
      plan_id: subscriptionData?.plan_id || null
    };

    // Error handling already done above

    // Filter out sensitive data before returning
    const safeData: BusinessProfileWithProducts = {
      id: data.id,
      business_name: data.business_name,
      logo_url: data.logo_url,
      member_name: data.member_name,
      title: data.title,
      address_line: data.address_line,
      city: data.city,
      state: data.state,
      pincode: data.pincode,
      locality: data.locality,
      phone: data.phone,
      instagram_url: data.instagram_url,
      facebook_url: data.facebook_url,
      whatsapp_number: data.whatsapp_number,
      about_bio: data.about_bio,
      status: data.status,
      business_slug: data.business_slug,
      theme_color: data.theme_color,
      delivery_info: data.delivery_info,
      business_hours: data.business_hours,
      business_category: data.business_category,
      google_maps_url: data.google_maps_url,
      established_year: data.established_year,
      total_likes: data.total_likes,
      total_subscriptions: data.total_subscriptions,
      average_rating: data.average_rating,
      created_at: data.created_at,
      updated_at: data.updated_at,
      trial_end_date: data.trial_end_date,
      contact_email: data.contact_email,
      subscription_status: data.subscription_status,
      plan_id: data.plan_id,
      custom_branding: data.custom_branding,
      custom_ads: data.custom_ads,
      products_services: data.products_services,
    };

    return { data: safeData };
  } catch (e) {
    console.error("Exception in getSecureBusinessProfileWithProductsBySlug:", e);
    return { error: "An unexpected error occurred." };
  }
}
