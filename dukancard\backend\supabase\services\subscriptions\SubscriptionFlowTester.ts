/**
 * SUBSCRIPTION FLOW TESTER
 * 
 * Comprehensive testing utility for all subscription flow scenarios.
 * This module provides testing functions for each business scenario
 * described in the requirements.
 */

import { createAdminClient } from '@/utils/supabase/admin';
import { SubscriptionFlowManager } from './SubscriptionFlowManager';

import type { SubscriptionRequest, SubscriptionContext } from './types';

export interface TestScenario {
  id: string;
  name: string;
  description: string;
  setup: () => Promise<SubscriptionContext>;
  test: () => Promise<TestResult>;
  cleanup?: () => Promise<void>;
}

export interface TestResult {
  success: boolean;
  scenario: string;
  expectedFlow: string;
  actualFlow: string;
  message: string;
  details?: Record<string, unknown>;
  errors?: string[];
}

export class SubscriptionFlowTester {
  private adminClient = createAdminClient();
  private businessId: string;

  constructor(businessId: string) {
    // Use provided business ID - no defaults for security
    this.businessId = businessId;
    console.log(`🧪 SubscriptionFlowTester initialized with business ID: ${this.businessId}`);
  }

  /**
   * Test Scenario 1: Trial User Authorizes Payment for Post-Trial
   */
  async testTrialUserAuthorizesPayment(): Promise<TestResult> {
    const scenario = "Trial User Authorizes Payment for Post-Trial";
    
    try {
      // Create test context for trial user
      const context: SubscriptionContext = {
        userId: 'test-user-trial-auth',
        currentPlanId: 'free',
        currentPlanCycle: 'monthly',
        subscriptionStatus: 'trial',
        trialEndDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        razorpaySubscriptionId: null,
        lastPaymentMethod: null,
        hasActiveSubscription: false
      };

      const request: SubscriptionRequest = {
        planId: 'growth',
        planCycle: 'monthly',
        context
      };

      const decision = SubscriptionFlowManager.determineSubscriptionFlow(request);

      return {
        success: decision.flowType === 'FRESH_SUBSCRIPTION' && decision.paymentTiming === 'TRIAL_END',
        scenario,
        expectedFlow: 'FRESH_SUBSCRIPTION with TRIAL_END payment',
        actualFlow: `${decision.flowType} with ${decision.paymentTiming} payment`,
        message: decision.reason,
        details: { decision }
      };

    } catch (error) {
      return {
        success: false,
        scenario,
        expectedFlow: 'FRESH_SUBSCRIPTION with TRIAL_END payment',
        actualFlow: 'ERROR',
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        errors: [String(error)]
      };
    }
  }

  /**
   * Test Scenario 2: Post-Trial User Subscribes (Upfront Payment)
   */
  async testPostTrialUserSubscribes(): Promise<TestResult> {
    const scenario = "Post-Trial User Subscribes (Upfront Payment)";
    
    try {
      const context: SubscriptionContext = {
        userId: 'test-user-post-trial',
        currentPlanId: 'free',
        currentPlanCycle: 'monthly',
        subscriptionStatus: 'inactive',
        trialEndDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
        razorpaySubscriptionId: null,
        lastPaymentMethod: null,
        hasActiveSubscription: false
      };

      const request: SubscriptionRequest = {
        planId: 'basic',
        planCycle: 'monthly',
        context
      };

      const decision = SubscriptionFlowManager.determineSubscriptionFlow(request);

      return {
        success: decision.flowType === 'UPFRONT_PAYMENT' && decision.requiresUpfrontPayment,
        scenario,
        expectedFlow: 'UPFRONT_PAYMENT with immediate payment',
        actualFlow: `${decision.flowType} with ${decision.requiresUpfrontPayment ? 'immediate' : 'deferred'} payment`,
        message: decision.reason,
        details: { decision }
      };

    } catch (error) {
      return {
        success: false,
        scenario,
        expectedFlow: 'UPFRONT_PAYMENT with immediate payment',
        actualFlow: 'ERROR',
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        errors: [String(error)]
      };
    }
  }

  /**
   * Test Scenario 3: Active User Switches Plans (Card Payment)
   */
  async testActiveUserSwitchesPlansCard(): Promise<TestResult> {
    const scenario = "Active User Switches Plans (Card Payment)";
    
    try {
      const context: SubscriptionContext = {
        userId: 'test-user-active-card',
        currentPlanId: 'basic',
        currentPlanCycle: 'monthly',
        subscriptionStatus: 'active',
        trialEndDate: null,
        razorpaySubscriptionId: 'sub_test_card_payment',
        lastPaymentMethod: 'card',
        hasActiveSubscription: true
      };

      const request: SubscriptionRequest = {
        planId: 'growth',
        planCycle: 'monthly',
        context
      };

      const decision = SubscriptionFlowManager.determineSubscriptionFlow(request);

      return {
        success: decision.flowType === 'CREATE_AND_CANCEL' && decision.shouldCancelExisting,
        scenario,
        expectedFlow: 'CREATE_AND_CANCEL with existing subscription cancellation (simplified flow)',
        actualFlow: `${decision.flowType} with ${decision.shouldCancelExisting ? 'cancellation' : 'no cancellation'}`,
        message: decision.reason,
        details: { decision }
      };

    } catch (error) {
      return {
        success: false,
        scenario,
        expectedFlow: 'CREATE_AND_CANCEL with existing subscription cancellation (simplified flow)',
        actualFlow: 'ERROR',
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        errors: [String(error)]
      };
    }
  }

  /**
   * Test Scenario 4: Active User Switches Plans (UPI/E-Mandate)
   */
  async testActiveUserSwitchesPlansUPI(): Promise<TestResult> {
    const scenario = "Active User Switches Plans (UPI/E-Mandate)";
    
    try {
      const context: SubscriptionContext = {
        userId: 'test-user-active-upi',
        currentPlanId: 'basic',
        currentPlanCycle: 'monthly',
        subscriptionStatus: 'active',
        trialEndDate: null,
        razorpaySubscriptionId: 'sub_test_upi_payment',
        lastPaymentMethod: 'upi',
        hasActiveSubscription: true
      };

      const request: SubscriptionRequest = {
        planId: 'growth',
        planCycle: 'monthly',
        context
      };

      const decision = SubscriptionFlowManager.determineSubscriptionFlow(request);

      return {
        success: decision.flowType === 'CREATE_AND_CANCEL' && decision.shouldCancelExisting,
        scenario,
        expectedFlow: 'CREATE_AND_CANCEL with existing subscription cancellation',
        actualFlow: `${decision.flowType} with ${decision.shouldCancelExisting ? 'cancellation' : 'no cancellation'}`,
        message: decision.reason,
        details: { decision }
      };

    } catch (error) {
      return {
        success: false,
        scenario,
        expectedFlow: 'CREATE_AND_CANCEL with existing subscription cancellation',
        actualFlow: 'ERROR',
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        errors: [String(error)]
      };
    }
  }



  /**
   * Test Webhook Idempotency
   */
  async testWebhookIdempotency(): Promise<TestResult> {
    const scenario = "Webhook Idempotency Check";

    try {
      // Test that processed webhook events are properly tracked
      const testEventId = `test_event_${Date.now()}`;

      // Check if event exists (should not)
      const { data: existingEvent } = await this.adminClient
        .from('processed_webhook_events')
        .select('event_id')
        .eq('event_id', testEventId)
        .maybeSingle();

      const eventNotExists = !existingEvent;

      // Insert test event
      const { error: insertError } = await this.adminClient
        .from('processed_webhook_events')
        .insert({
          event_id: testEventId,
          event_type: 'test.event',
          entity_type: 'subscription',
          entity_id: 'test_sub_123',
          status: 'processed',
          notes: 'Test event for idempotency check'
        });

      const insertSuccessful = !insertError;

      // Try to insert same event again (should fail due to unique constraint)
      const { error: duplicateError } = await this.adminClient
        .from('processed_webhook_events')
        .insert({
          event_id: testEventId,
          event_type: 'test.event',
          entity_type: 'subscription',
          entity_id: 'test_sub_123',
          status: 'processed',
          notes: 'Duplicate test event'
        });

      const duplicateRejected = !!duplicateError;

      // Cleanup
      await this.adminClient
        .from('processed_webhook_events')
        .delete()
        .eq('event_id', testEventId);

      return {
        success: eventNotExists && insertSuccessful && duplicateRejected,
        scenario,
        expectedFlow: 'Event tracking with duplicate prevention',
        actualFlow: `Initial: ${eventNotExists ? 'not exists' : 'exists'}, Insert: ${insertSuccessful ? 'success' : 'failed'}, Duplicate: ${duplicateRejected ? 'rejected' : 'allowed'}`,
        message: 'Webhook idempotency test completed',
        details: { insertError, duplicateError }
      };

    } catch (error) {
      return {
        success: false,
        scenario,
        expectedFlow: 'Event tracking with duplicate prevention',
        actualFlow: 'ERROR',
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        errors: [String(error)]
      };
    }
  }

  /**
   * Test Subscription State Validation
   */
  async testSubscriptionStateValidation(): Promise<TestResult> {
    const scenario = "Subscription State Validation";

    try {
      // Test the subscription state validation function using the configured business ID
      const testBusinessId = this.businessId;

      // Store original state for restoration
      const { data: originalProfile } = await this.adminClient
        .from('business_profiles')
        .select('has_active_subscription, trial_end_date, business_name')
        .eq('id', testBusinessId)
        .single();

      const { data: originalSubscription } = await this.adminClient
        .from('payment_subscriptions')
        .select('subscription_status, plan_id, razorpay_subscription_id')
        .eq('business_profile_id', testBusinessId)
        .single();

      // Create intentionally inconsistent state for testing
      await this.adminClient
        .from('business_profiles')
        .update({
          has_active_subscription: true, // Intentionally inconsistent
          business_name: 'Test Business Validation'
        })
        .eq('id', testBusinessId);

      // Update subscription to inconsistent state
      await this.adminClient
        .from('payment_subscriptions')
        .update({
          subscription_status: 'cancelled', // Inconsistent with has_active_subscription: true
          plan_id: 'free',
          razorpay_subscription_id: 'test_sub_validation'
        })
        .eq('business_profile_id', testBusinessId);

      // Run validation function
      const { data: validationResult, error: validationError } = await this.adminClient
        .rpc('validate_and_fix_subscription_state', {
          p_business_profile_id: testBusinessId
        });

      const validationSuccessful = !validationError && validationResult?.success;
      const inconsistencyDetected = !validationResult?.is_consistent;
      const fixesApplied = validationResult?.fixes_applied?.length > 0;

      // Restore original state instead of deleting
      if (originalProfile) {
        await this.adminClient
          .from('business_profiles')
          .update({
            has_active_subscription: originalProfile.has_active_subscription,
            trial_end_date: originalProfile.trial_end_date,
            business_name: originalProfile.business_name
          })
          .eq('id', testBusinessId);
      }

      if (originalSubscription) {
        await this.adminClient
          .from('payment_subscriptions')
          .update({
            subscription_status: originalSubscription.subscription_status,
            plan_id: originalSubscription.plan_id,
            razorpay_subscription_id: originalSubscription.razorpay_subscription_id
          })
          .eq('business_profile_id', testBusinessId);
      }

      return {
        success: validationSuccessful && inconsistencyDetected && fixesApplied,
        scenario,
        expectedFlow: 'Detect and fix subscription state inconsistencies',
        actualFlow: `Validation: ${validationSuccessful ? 'success' : 'failed'}, Inconsistency: ${inconsistencyDetected ? 'detected' : 'not detected'}, Fixes: ${fixesApplied ? 'applied' : 'none'}`,
        message: 'Subscription state validation test completed',
        details: { validationResult, validationError }
      };

    } catch (error) {
      return {
        success: false,
        scenario,
        expectedFlow: 'Detect and fix subscription state inconsistencies',
        actualFlow: 'ERROR',
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        errors: [String(error)]
      };
    }
  }

  /**
   * Run all subscription flow tests
   */
  async runAllTests(): Promise<TestResult[]> {
    const tests = [
      this.testTrialUserAuthorizesPayment(),
      this.testPostTrialUserSubscribes(),
      this.testActiveUserSwitchesPlansCard(),
      this.testActiveUserSwitchesPlansUPI(),
      this.testWebhookIdempotency(),
      this.testSubscriptionStateValidation()
    ];

    return Promise.all(tests);
  }

  /**
   * Generate test report
   */
  async generateTestReport(): Promise<{
    totalTests: number;
    passedTests: number;
    failedTests: number;
    results: TestResult[];
    summary: string;
  }> {
    const results = await this.runAllTests();
    const passedTests = results.filter(r => r.success).length;
    const failedTests = results.length - passedTests;

    return {
      totalTests: results.length,
      passedTests,
      failedTests,
      results,
      summary: `${passedTests}/${results.length} tests passed`
    };
  }
}
