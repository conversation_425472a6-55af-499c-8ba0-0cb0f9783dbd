@import "tailwindcss";

/* Keep original Shadcn UI theme structure */
@custom-variant dark (&:is(.dark *));

@theme inline {
  /* Map CSS variables to theme properties - Keep original mappings */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-sans);
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

@layer base {
  :root {
    --radius: 0.625rem; /* Keep original radius */
    --font-sans: "Inter", system-ui, -apple-system, BlinkMacSystemFont,
      "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans",
      "Helvetica Neue", sans-serif; /* Define Inter font */

    /* Light Mode Base - Clean White/Black Theme */
    --background: oklch(1 0 0); /* Pure white */
    --foreground: oklch(0.09 0 0); /* Pure black */
    --card: oklch(1 0 0); /* Pure white */
    --card-foreground: oklch(0.09 0 0); /* Pure black */
    --popover: oklch(1 0 0); /* Pure white */
    --popover-foreground: oklch(0.09 0 0); /* Pure black */
    --secondary: oklch(0.96 0 0); /* Light gray */
    --secondary-foreground: oklch(0.09 0 0); /* Pure black */
    --muted: oklch(0.96 0 0); /* Light gray */
    --muted-foreground: oklch(0.45 0 0); /* Medium gray */
    --accent: oklch(0.96 0 0); /* Light gray */
    --accent-foreground: oklch(0.09 0 0); /* Pure black */
    --destructive: oklch(0.577 0.245 27.325); /* Keep original destructive */
    --border: oklch(0.9 0 0); /* Light gray border */
    --input: oklch(0.9 0 0); /* Light gray input */
    --ring: oklch(0.5 0 0 / 0.5); /* Neutral gray ring */

    /* Gold Theme - Keep for specific brand elements only */
    --brand-gold: oklch(0.769 0.11 85);
    --brand-gold-foreground: oklch(0.09 0 0); /* Pure black text on gold */
    --brand-gold-light: oklch(0.82 0.1 85); /* Lighter gold for hover/accent */
    --brand-gold-dark: oklch(0.7 0.12 85); /* Darker gold for active/border */

    /* Primary (using neutral colors instead of gold) */
    --primary: oklch(0.09 0 0); /* Pure black for primary */
    --primary-foreground: oklch(1 0 0); /* Pure white text on black */

    /* Chart Colors (Keep original or refine) */
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);

    /* Sidebar */
    --sidebar: oklch(1 0 0); /* Pure white background in light mode */
    --sidebar-foreground: oklch(0 0 0); /* Pure black text */
    --sidebar-primary: oklch(0 0 0); /* Pure black for primary */
    --sidebar-primary-foreground: oklch(1 0 0); /* Pure white text */
    --sidebar-accent: oklch(0.95 0 0); /* Light gray accent */
    --sidebar-accent-foreground: oklch(0 0 0); /* Pure black text */
    --sidebar-border: oklch(0.9 0 0); /* Light gray border */
    --sidebar-ring: oklch(0.5 0 0); /* Medium gray ring */
  }

  .dark {
    /* Dark Mode Base - Clean Black/White Theme */
    --background: oklch(0 0 0); /* Pure black */
    --foreground: oklch(1 0 0); /* Pure white */
    --card: oklch(0 0 0); /* Pure black */
    --card-foreground: oklch(1 0 0); /* Pure white */
    --popover: oklch(0.1 0 0); /* Very dark gray */
    --popover-foreground: oklch(1 0 0); /* Pure white */
    --secondary: oklch(0.15 0 0); /* Dark gray */
    --secondary-foreground: oklch(1 0 0); /* Pure white */
    --muted: oklch(0.15 0 0); /* Dark gray */
    --muted-foreground: oklch(0.6 0 0); /* Medium gray */
    --accent: oklch(0.15 0 0); /* Dark gray */
    --accent-foreground: oklch(1 0 0); /* Pure white */
    --destructive: oklch(0.704 0.191 22.216); /* Keep original destructive */
    --border: oklch(1 0 0 / 0.15); /* Subtle white border */
    --input: oklch(1 0 0 / 0.18); /* Subtle white input */
    --ring: oklch(1 0 0 / 0.5); /* White ring */

    /* Gold Theme - Keep for specific brand elements only */
    --brand-gold: oklch(0.769 0.11 85);
    --brand-gold-foreground: oklch(0 0 0); /* Pure black text on gold */
    --brand-gold-light: oklch(0.82 0.1 85); /* Lighter gold for hover/accent */
    --brand-gold-dark: oklch(0.7 0.12 85); /* Darker gold for active/border */

    /* Primary (using neutral colors instead of gold) */
    --primary: oklch(1 0 0); /* Pure white for primary in dark mode */
    --primary-foreground: oklch(0 0 0); /* Pure black text on white */

    /* Chart Colors (Keep original or refine) */
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);

    /* Sidebar */
    --sidebar: oklch(0 0 0); /* Pure black background in dark mode */
    --sidebar-foreground: oklch(1 0 0); /* Pure white text */
    --sidebar-primary: oklch(1 0 0); /* Pure white for primary */
    --sidebar-primary-foreground: oklch(0 0 0); /* Pure black text */
    --sidebar-accent: oklch(0.15 0 0); /* Dark gray accent */
    --sidebar-accent-foreground: oklch(1 0 0); /* Pure white text */
    --sidebar-border: oklch(1 0 0 / 10%); /* Subtle white border */
    --sidebar-ring: oklch(0.5 0 0); /* Medium gray ring */
  }

  /* Minimal base layer adjustments */
  body {
    /* Font smoothing */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Font family is applied via layout.tsx using --font-sans */
  }

  /* Keep original base layer for * if needed, but remove problematic applies */
  * {
    border-color: var(--border); /* Apply border color directly */
  }

  /* Add focus-visible styles for accessibility using CSS variables */
  *:focus-visible {
    outline: 2px solid transparent;
    outline-offset: 2px;
    /* Replicate ring and offset using box-shadow */
    box-shadow: 0 0 0 2px var(--background), 0 0 0 4px var(--ring);
  }

  /* Recharts Dark Mode Overrides */
  .dark .recharts-text,
  .dark .recharts-legend-item-text {
    fill: var(--foreground); /* Use main foreground for text */
  }

  .dark .recharts-tooltip-wrapper .recharts-default-tooltip {
    background-color: var(--popover) !important; /* Override default style */
    border-color: var(--border) !important;
    color: var(--popover-foreground) !important;
  }

  .dark .recharts-cartesian-axis-tick-value {
    fill: var(--muted-foreground); /* Use muted for axis labels */
  }

  /* Highlight.js theme customization for better integration */
  .hljs {
    background: hsl(var(--muted)) !important;
    color: hsl(var(--foreground)) !important;
    border-radius: 0.5rem;
  }

  .dark .hljs {
    background: hsl(var(--muted)) !important;
    color: hsl(var(--foreground)) !important;
  }

  /* Override specific highlight.js colors to match theme */
  .hljs-keyword,
  .hljs-selector-tag,
  .hljs-built_in {
    color: hsl(var(--primary)) !important;
  }

  .hljs-string,
  .hljs-attr {
    color: #22c55e !important; /* Green for strings */
  }

  .hljs-number,
  .hljs-literal {
    color: #f59e0b !important; /* Amber for numbers */
  }

  .hljs-comment {
    color: hsl(var(--muted-foreground)) !important;
    font-style: italic;
  }

  .dark .recharts-cartesian-grid-line line {
    stroke: var(--border); /* Use border color for grid lines */
  }

  /* Ensure Brush text is also themed */
  .dark .recharts-brush-texts {
    fill: var(--muted-foreground);
  }
}

:root {
  --sidebar: hsl(0 0% 100%); /* Pure white */
  --sidebar-foreground: hsl(0 0% 0%); /* Pure black */
  --sidebar-primary: hsl(0 0% 0%); /* Pure black */
  --sidebar-primary-foreground: hsl(0 0% 100%); /* Pure white */
  --sidebar-accent: hsl(0 0% 95%); /* Light gray */
  --sidebar-accent-foreground: hsl(0 0% 0%); /* Pure black */
  --sidebar-border: hsl(0 0% 90%); /* Light gray */
  --sidebar-ring: hsl(0 0% 50%); /* Medium gray */
}

.dark {
  --sidebar: hsl(0 0% 0%); /* Pure black */
  --sidebar-foreground: hsl(0 0% 100%); /* Pure white */
  --sidebar-primary: hsl(0 0% 100%); /* Pure white */
  --sidebar-primary-foreground: hsl(0 0% 0%); /* Pure black */
  --sidebar-accent: hsl(0 0% 15%); /* Dark gray */
  --sidebar-accent-foreground: hsl(0 0% 100%); /* Pure white */
  --sidebar-border: hsl(0 0% 15%); /* Dark gray */
  --sidebar-ring: hsl(0 0% 50%); /* Medium gray */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Add RGB variables for the brand gold color */
:root {
  --brand-gold-rgb: 194, 157, 91;
}

/* Hide scrollbar for horizontal product sliders */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Add animations */
@keyframes gradient-x {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-gradient-x {
  animation: gradient-x 15s ease infinite;
  background-size: 200% 200%;
}

/* Custom slider styles for crop dialog */
input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: rgb(59 130 246);
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.4);
  transition: all 0.2s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.6);
}

input[type="range"]::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: rgb(59 130 246);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.4);
  transition: all 0.2s ease;
}

input[type="range"]::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.6);
}

/* Custom prose styles for blog content */
@layer components {
  .prose {
    --tw-prose-body: hsl(var(--foreground) / 0.9);
    --tw-prose-headings: hsl(var(--foreground));
    --tw-prose-lead: hsl(var(--foreground) / 0.8);
    --tw-prose-links: hsl(var(--primary));
    --tw-prose-bold: hsl(var(--foreground));
    --tw-prose-counters: hsl(var(--muted-foreground));
    --tw-prose-bullets: hsl(var(--muted-foreground));
    --tw-prose-hr: hsl(var(--border));
    --tw-prose-quotes: hsl(var(--foreground) / 0.8);
    --tw-prose-quote-borders: hsl(var(--primary));
    --tw-prose-captions: hsl(var(--muted-foreground));
    --tw-prose-code: hsl(var(--foreground));
    --tw-prose-pre-code: hsl(var(--foreground));
    --tw-prose-pre-bg: hsl(var(--muted));
    --tw-prose-th-borders: hsl(var(--border));
    --tw-prose-td-borders: hsl(var(--border));
  }
}
