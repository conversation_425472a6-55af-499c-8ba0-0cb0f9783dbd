'use client';

import { useState, useEffect, useCallback } from 'react';
import { useInView } from 'react-intersection-observer';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2, AlertCircle } from 'lucide-react';
import { getUnifiedFeedPostsWithAuthors, UnifiedPost } from '@/lib/actions/posts/unifiedFeed';
import { FeedFilterType } from '@/lib/types/posts';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import UnifiedPostCard from './shared/UnifiedPostCard';
import PostCardSkeleton from './shared/PostCardSkeleton';
import ModernFeedHeader from './shared/ModernFeedHeader';
import ModernFeedContainer from './shared/ModernFeedContainer';
import ModernCustomerPostCreator from './shared/ModernCustomerPostCreator';

interface ModernCustomerFeedListProps {
  initialPosts: UnifiedPost[];
  initialTotalCount: number;
  initialHasMore: boolean;
  initialFilter?: FeedFilterType;
  citySlug?: string;
  stateSlug?: string;
  localitySlug?: string;
  pincode?: string;
  userName?: string;
}

export default function ModernCustomerFeedList({
  initialPosts,
  initialTotalCount,
  initialHasMore,
  initialFilter = 'smart',
  citySlug,
  stateSlug,
  localitySlug,
  pincode,
  userName = 'Valued Customer'
}: ModernCustomerFeedListProps) {
  // State for posts and pagination
  const [posts, setPosts] = useState<UnifiedPost[]>(initialPosts);
  const [_totalCount, setTotalCount] = useState<number>(initialTotalCount);
  const [hasMore, setHasMore] = useState<boolean>(initialHasMore);
  const [page, setPage] = useState<number>(1);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [filter, setFilter] = useState<FeedFilterType>(initialFilter);

  // Intersection observer for infinite scroll
  const { ref, inView } = useInView();

  // Load more posts
  const loadMorePosts = useCallback(async () => {
    if (!hasMore || isLoading) return;

    setIsLoading(true);

    try {
      const nextPage = page + 1;
      const result = await getUnifiedFeedPostsWithAuthors({
        filter,
        page: nextPage,
        city_slug: citySlug,
        state_slug: stateSlug,
        locality_slug: localitySlug,
        pincode: pincode,
      });

      if (result.success && result.data?.items) {
        setPosts(prev => {
          // Create a Set of existing post IDs for fast lookup
          const existingIds = new Set(prev.map(post => post.id));
          // Filter out any posts that already exist
          const newPosts = result.data!.items.filter(post => !existingIds.has(post.id));
          return [...prev, ...newPosts];
        });
        setHasMore(result.data!.hasMore || false);
        setTotalCount(result.data!.totalCount || 0);
        setPage(nextPage);
      }
    } catch (error) {
      console.error('Error loading more posts:', error);
    } finally {
      setIsLoading(false);
    }
  }, [hasMore, isLoading, page, filter, citySlug, stateSlug, localitySlug, pincode]);

  // Load more posts when the user scrolls to the bottom
  useEffect(() => {
    if (inView && hasMore && !isLoading) {
      loadMorePosts();
    }
  }, [inView, hasMore, isLoading, loadMorePosts]);

  // Handle filter change
  const handleFilterChange = async (newFilter: FeedFilterType) => {
    if (newFilter === filter) return;

    setIsLoading(true);
    setFilter(newFilter);

    // Clear existing posts immediately to show skeletons
    setPosts([]);

    try {
      const result = await getUnifiedFeedPostsWithAuthors({
        filter: newFilter,
        page: 1,
        city_slug: citySlug,
        state_slug: stateSlug,
        locality_slug: localitySlug,
        pincode: pincode,
      });

      if (result.success && result.data) {
        setPosts(result.data.items);
        setHasMore(result.data.hasMore);
        setTotalCount(result.data.totalCount);
        setPage(1);
      }
    } catch (error) {
      console.error('Error changing filter:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle post creation success
  const handlePostCreated = async () => {
    // Refresh the feed
    try {
      const result = await getUnifiedFeedPostsWithAuthors({
        filter,
        page: 1,
        city_slug: citySlug,
        state_slug: stateSlug,
        locality_slug: localitySlug,
        pincode: pincode,
      });

      if (result.success && result.data?.items) {
        // For refresh, we replace all posts so no deduplication needed
        setPosts(result.data.items);
        setHasMore(result.data.hasMore || false);
        setTotalCount(result.data.totalCount || 0);
        setPage(1);
      }
    } catch (error) {
      console.error('Error refreshing feed:', error);
    }
  };

  // Handle post update
  const handlePostUpdate = (postId: string, newContent: string) => {
    setPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? { ...post, content: newContent }
          : post
      )
    );
  };

  // Handle post deletion
  const handlePostDelete = (postId: string) => {
    setPosts(prevPosts => prevPosts.filter(post => post.id !== postId));
    setTotalCount(prevCount => Math.max(0, prevCount - 1));
  };

  // Handle product update
  const handleProductsUpdate = (postId: string, newProductIds: string[]) => {
    setPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? { ...post, product_ids: newProductIds }
          : post
      )
    );
  };

  // Get empty state message
  const getEmptyStateMessage = () => {
    switch (filter) {
      case 'smart':
        return 'No posts available in your smart feed. Try subscribing to businesses or check other filters.';
      case 'subscribed':
        return 'Subscribe to businesses to see their posts here.';
      case 'locality':
        return 'No posts from businesses in your locality yet.';
      case 'pincode':
        return 'No posts from businesses in your pincode yet.';
      case 'city':
        return 'No posts from businesses in your city yet.';
      case 'state':
        return 'No posts from businesses in your state yet.';
      default:
        return 'No posts available at the moment.';
    }
  };

  return (
    <ModernFeedContainer>
      <ModernFeedHeader
        activeFilter={filter}
        onFilterChange={handleFilterChange}
        isLoading={isLoading}
      />

      {/* Posts Content */}
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Customer Post Creation Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <ModernCustomerPostCreator
            customerName={userName}
            onPostCreated={handlePostCreated}
          />
        </motion.div>

        <AnimatePresence mode="wait">
          {posts.length === 0 && !isLoading ? (
            <motion.div
              key="empty-state"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Alert className="bg-white dark:bg-black border-neutral-200 dark:border-neutral-800">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>No posts found</AlertTitle>
                <AlertDescription>{getEmptyStateMessage()}</AlertDescription>
              </Alert>
            </motion.div>
          ) : (
            <motion.div
              key="posts-list"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="space-y-0"
            >
              {/* Loading skeletons during filter change */}
              {isLoading && posts.length === 0 && (
                <>
                  {Array.from({ length: 10 }).map((_, index) => (
                    <PostCardSkeleton
                      key={`skeleton-${index}`}
                      index={index}
                      showImage={Math.random() > 0.3}
                      showProducts={Math.random() > 0.7}
                    />
                  ))}
                </>
              )}

              {/* Posts */}
              {posts.map((post, index) => (
                <UnifiedPostCard
                  key={post.id}
                  post={post}
                  index={index}
                  onPostUpdate={handlePostUpdate}
                  onPostDelete={handlePostDelete}
                  onProductsUpdate={handleProductsUpdate}
                />
              ))}

              {/* Infinite scroll trigger */}
              {hasMore && (
                <div ref={ref} className="flex justify-center items-center py-8">
                  {isLoading && (
                    <div className="flex items-center gap-2 text-neutral-500">
                      <Loader2 className="h-5 w-5 animate-spin" />
                      <span className="text-sm">Loading more posts...</span>
                    </div>
                  )}
                </div>
              )}

              {/* Load more button (fallback) */}
              {hasMore && !isLoading && (
                <div className="flex justify-center mt-8 mb-4">
                  <Button
                    variant="outline"
                    onClick={loadMorePosts}
                    disabled={isLoading}
                    className="bg-white dark:bg-black border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-900"
                  >
                    Load More Posts
                  </Button>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </ModernFeedContainer>
  );
}
