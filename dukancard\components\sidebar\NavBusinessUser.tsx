"use client";

import React, { useState, useEffect } from "react";
import {
  ChevronsUpDown,
  LogOut,
  // Import other icons if needed for dropdown items
} from "lucide-react";
import { signOutUser } from "@/app/auth/actions"; // Assuming this action is correct
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button"; // Import Button for the form
import { cn } from "@/lib/utils";

// Helper to get initials (can be moved to utils if used elsewhere)
const getInitials = (name: string | null | undefined): string => {
  if (!name) return "?";
  const names = name.trim().split(/\s+/);
  if (names.length === 1 && names[0]) return names[0].charAt(0).toUpperCase();
  if (names.length > 1 && names[0] && names[names.length - 1]) {
    return (
      names[0].charAt(0).toUpperCase() +
      names[names.length - 1].charAt(0).toUpperCase()
    );
  }
  return "?";
};


interface NavBusinessUserProps {
  user: {
    name: string | null; // Member name
    avatar: string | null; // Business logo URL
  };
  businessName: string | null;
}

export function NavBusinessUser({ user, businessName }: NavBusinessUserProps) {
  const { isMobile } = useSidebar();
  const businessInitials = getInitials(businessName);
  const [isTablet, setIsTablet] = useState(false);

  useEffect(() => {
    // Check if device is a tablet (between 768px and 1024px)
    const checkTablet = () => {
      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);
    };

    // Initial check
    checkTablet();

    // Add event listener for resize
    window.addEventListener('resize', checkTablet);

    // Cleanup
    return () => window.removeEventListener('resize', checkTablet);
  }, []);

  // Use business logo/initials for the main trigger avatar
  const displayAvatar = user.avatar;
  const displayFallback = businessInitials;
  const displayName = businessName || "Business";
  const displaySubText = user.name || "Member";

  // Calculate bottom padding based on device type
  const bottomPadding = isMobile ? "pb-16" : isTablet ? "pb-14" : "";

  return (
    <SidebarMenu className={cn(bottomPadding)}>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg border border-[var(--brand-gold)]/30">
                {displayAvatar ? (
                  <AvatarImage src={displayAvatar} alt={displayName} />
                ) : null}
                <AvatarFallback className="rounded-lg bg-muted border border-[var(--brand-gold)]/30 text-xs">
                  {displayFallback}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{displayName}</span>
                <span className="truncate text-xs text-muted-foreground">{displaySubText}</span>
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                 <Avatar className="h-8 w-8 rounded-lg border border-[var(--brand-gold)]/30">
                    {displayAvatar ? (
                      <AvatarImage src={displayAvatar} alt={displayName} />
                    ) : null}
                    <AvatarFallback className="rounded-lg bg-muted border border-[var(--brand-gold)]/30 text-xs">
                      {displayFallback}
                    </AvatarFallback>
                 </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">{displayName}</span>
                   <span className="truncate text-xs text-muted-foreground">{displaySubText}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            {/* Add any relevant dropdown items here if needed */}
            {/* <DropdownMenuGroup>
              <DropdownMenuItem>
                <Settings /> Settings // Example
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator /> */}
            <form action={signOutUser} className="w-full px-2 py-1.5">
              <Button
                variant="ghost"
                type="submit"
                className="w-full justify-start p-0 h-auto font-normal cursor-pointer"
              >
                <LogOut className="mr-2 h-4 w-4" />
                Log out
              </Button>
            </form>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
