import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Container } from "@/app/components/ui/container";

export default function CategoryNotFound() {
  return (
    <Container className="flex flex-col items-center justify-center min-h-[60vh] py-12 text-center">
      <h1 className="text-4xl font-bold mb-4">Category Not Found</h1>
      <p className="text-muted-foreground mb-8 max-w-md">
        The category you&apos;re looking for doesn&apos;t exist or may have been removed.
      </p>
      <div className="flex flex-col sm:flex-row gap-4">
        <Link href="/categories">
          <Button>Browse All Categories</Button>
        </Link>
        <Link href="/?view=home">
          <Button variant="outline">Go to Homepage</Button>
        </Link>
      </div>
    </Container>
  );
}
