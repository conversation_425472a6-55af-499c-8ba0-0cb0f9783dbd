import { MetadataRoute } from "next";
// import { BUSINESS_CATEGORIES } from "@/lib/config/categories"; // Temporarily commented out

/**
 * Main categories sitemap index
 * This only includes references to other sitemaps, not direct category pages
 */
export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";

  // Always include the base domain URL to ensure the sitemap is never empty
  const baseEntries: MetadataRoute.Sitemap = [
    {
      url: siteUrl,
      lastModified: new Date(),
      changeFrequency: "daily",
      priority: 1.0,
    }
  ];

  // TODO: Uncomment the following code when server is upgraded and ready for crawler load
  // This will enable full categories sitemap generation

  /*
  try {
    // Get all categories with slugs
    const categoriesWithSlugs = BUSINESS_CATEGORIES.filter(c => c.slug);

    // Create entries for sitemap references only
    const sitemapEntries = [
      // Global categories sitemap (contains all category pages)
      {
        url: `${siteUrl}/categories/global/sitemap.xml`,
        lastModified: new Date(),
        changeFrequency: "daily" as const,
        priority: 0.9,
      },
      // Add references to category-specific sitemaps
      ...categoriesWithSlugs.map(category => ({
        url: `${siteUrl}/categories/${category.slug}/sitemap.xml`,
        lastModified: new Date(),
        changeFrequency: "weekly" as const,
        priority: 0.8,
      }))
    ];

    console.log(`Generated categories sitemap with ${sitemapEntries.length} entries`);
    return sitemapEntries;
  } catch (error) {
    console.error("Error generating categories sitemap:", error);

    // Return a basic sitemap in case of error
    return [
      {
        url: `${siteUrl}/categories/global/sitemap.xml`,
        lastModified: new Date(),
        changeFrequency: "daily" as const,
        priority: 0.9,
      }
    ];
  }
  */

  // Temporarily return only base entries to reduce server load
  return baseEntries;
}
