// Re-export common types from the shared types file
// This approach allows both server and client components to import the same types

import {
  PlanCycle,
  PlanId,
  SubscriptionAction,
  PaymentMethodType,
  PaymentType,
  PaymentManagementAction,
  PaymentMethod,
  PaymentOptions,
  PaymentManagementActionDetails,
  ActionDetails,
  ActionResponse
} from "@/lib/types/subscription";

// Re-export all types
export type {
  PlanCycle,
  PlanId,
  SubscriptionAction,
  PaymentMethodType,
  PaymentType,
  PaymentManagementAction,
  PaymentMethod,
  PaymentOptions,
  PaymentManagementActionDetails,
  ActionDetails,
  ActionResponse
};
