"use client";

import { useState, useEffect, useRef } from "react";
import { Search, X, Filter, ArrowUpDown } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { BusinessProfilePublicData } from "@/lib/actions/businessProfiles/types";
import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import AnimatedBusinessGrid from "@/app/(main)/discover/components/AnimatedBusinessGrid";
import AnimatedBusinessGridSkeleton from "@/app/(main)/discover/components/AnimatedBusinessGridSkeleton";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";

interface ModernBusinessFilterGridProps {
  businesses: (BusinessProfilePublicData | BusinessCardData)[];
  isAuthenticated: boolean;
  onSortChange: (_sortBy: BusinessSortBy) => void;
  onSearch: (_searchTerm: string) => void;
  currentSortBy: BusinessSortBy;
  isLoading: boolean;
  initialSearchTerm?: string | null;
}

export default function ModernBusinessFilterGrid({
  businesses,
  isAuthenticated,
  onSortChange,
  onSearch,
  currentSortBy,
  isLoading,
  initialSearchTerm,
}: ModernBusinessFilterGridProps) {
  const [searchQuery, setSearchQuery] = useState(initialSearchTerm || "");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(
    initialSearchTerm || ""
  );
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isFirstRender = useRef(true);
  const prevSearchQueryRef = useRef(searchQuery);
  const wasInitiallyPopulated = useRef(!!initialSearchTerm);

  // Update prevSearchQueryRef when component mounts or searchQuery changes
  useEffect(() => {
    // Skip the first update if initialSearchTerm was provided
    if (isFirstRender.current && initialSearchTerm) {
      isFirstRender.current = false;
      wasInitiallyPopulated.current = true;
      return;
    }

    prevSearchQueryRef.current = searchQuery;
  }, [searchQuery, initialSearchTerm]);

  // Prevent infinite loops by tracking if we've already processed the initial search term
  useEffect(() => {
    // This effect runs only once on mount
    if (initialSearchTerm) {
      // Mark that we've already processed the initial search term
      wasInitiallyPopulated.current = true;
      isFirstRender.current = false;
    }
  }, [initialSearchTerm]); // Include initialSearchTerm as a dependency

  // Update isFirstRender when data is loaded
  useEffect(() => {
    // If we have businesses or isLoading is false, we're no longer on first render
    if (businesses.length > 0 || !isLoading) {
      isFirstRender.current = false;
    }
  }, [businesses, isLoading]);

  // Handle input change with manual debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);

    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = null;
    }

    // Check if the user has manually cleared the search
    const wasManuallyCleared =
      prevSearchQueryRef.current.length > 0 && query === "";

    // If the search was manually cleared, trigger search immediately
    if (wasManuallyCleared) {
      setDebouncedSearchQuery("");
      // Add a small visual delay before triggering the search
      setTimeout(() => {
        onSearch("");
      }, 100);
      return;
    }

    // Only set a new timeout if the query is empty or at least 3 characters
    if (query === "" || query.length >= 3) {
      searchTimeoutRef.current = setTimeout(() => {
        setDebouncedSearchQuery(query);
        // Skip search on initial render if initialSearchTerm was provided
        if (!isFirstRender.current) {
          // Call the server-side search function
          onSearch(query);
        } else {
          isFirstRender.current = false;
        }
      }, 500);
    }
  };

  // Handle clear search when clicking the X button
  const handleClearSearch = () => {
    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = null;
    }

    setSearchQuery("");
    setDebouncedSearchQuery("");

    // Add a small visual delay before triggering the search
    setTimeout(() => {
      onSearch("");
    }, 100);
  };

  const toggleFilterExpanded = () => {
    setIsFilterExpanded(!isFilterExpanded);
  };

  return (
    <div className="space-y-6">
      {/* Search and Filter Controls */}
      <motion.div
        className="sticky top-[80px] z-30 container mx-auto px-4"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="relative overflow-hidden bg-white/80 dark:bg-black/80 p-5 rounded-2xl border border-neutral-200/80 dark:border-neutral-800/80 transition-all duration-300 backdrop-blur-md">
          {/* Decorative elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-70">
            <div className="absolute -top-24 -right-24 w-48 h-48 rounded-full bg-gradient-to-br from-[var(--brand-gold)]/5 to-amber-500/5 blur-2xl dark:from-[var(--brand-gold)]/10 dark:to-amber-500/10"></div>
            <div className="absolute -bottom-24 -left-24 w-48 h-48 rounded-full bg-gradient-to-tr from-purple-500/5 to-blue-500/5 blur-2xl dark:from-purple-500/10 dark:to-blue-500/10"></div>
          </div>

          <div className="relative z-10 flex flex-col sm:flex-row gap-3 sm:gap-5 items-start sm:items-center justify-between">
            {/* Search Input */}
            <div className="relative w-full sm:w-auto sm:flex-1 max-w-full sm:max-w-md">
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  // Only trigger search if query is empty or at least 3 characters
                  if (searchQuery === "" || searchQuery.length >= 3) {
                    // Clear any existing timeout
                    if (searchTimeoutRef.current) {
                      clearTimeout(searchTimeoutRef.current);
                      searchTimeoutRef.current = null;
                    }
                    setDebouncedSearchQuery(searchQuery);
                    onSearch(searchQuery);
                  }
                }}
              >
                <div className="relative group">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--brand-gold)] transition-all duration-200" />
                  <Input
                    placeholder="Search businesses..."
                    value={searchQuery}
                    onChange={handleSearchChange}
                    className="pl-10 h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm"
                  />
                  {searchQuery && (
                    <button
                      type="button"
                      onClick={handleClearSearch}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors duration-200"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                  {/* Hidden submit button for form submission */}
                  <button type="submit" className="hidden">
                    Search
                  </button>
                </div>
              </form>
            </div>

            <div className="grid grid-cols-2 sm:flex sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
              {/* Sort Dropdown */}
              <Select
                value={currentSortBy}
                onValueChange={(value) => onSortChange(value as BusinessSortBy)}
                disabled={isLoading}
              >
                <SelectTrigger className="w-full sm:w-[180px] h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm">
                  <div className="flex items-center overflow-hidden">
                    <ArrowUpDown className="flex-shrink-0 mr-1 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 text-[var(--brand-gold)]" />
                    <SelectValue
                      placeholder="Sort by"
                      className="text-xs sm:text-sm truncate"
                    />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5">
                      Date
                    </SelectLabel>
                    <SelectItem value="created_desc" className="relative pl-8">
                      Newest First
                    </SelectItem>
                    <SelectItem value="created_asc" className="relative pl-8">
                      Oldest First
                    </SelectItem>
                  </SelectGroup>
                  <SelectGroup>
                    <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1">
                      Name
                    </SelectLabel>
                    <SelectItem value="name_asc" className="relative pl-8">
                      Name: A to Z
                    </SelectItem>
                    <SelectItem value="name_desc" className="relative pl-8">
                      Name: Z to A
                    </SelectItem>
                  </SelectGroup>
                  <SelectGroup>
                    <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1">
                      Popularity
                    </SelectLabel>
                    <SelectItem value="likes_desc" className="relative pl-8">
                      Most Liked
                    </SelectItem>
                    <SelectItem value="subscriptions_desc" className="relative pl-8">
                      Most Subscribed
                    </SelectItem>
                    <SelectItem value="rating_desc" className="relative pl-8">
                      Highest Rated
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>

              {/* Filter Button */}
              <Button
                variant="outline"
                onClick={toggleFilterExpanded}
                className="w-full sm:w-[140px] h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm overflow-hidden"
              >
                <div className="flex items-center">
                  <Filter className="flex-shrink-0 mr-1 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 text-[var(--brand-gold)]" />
                  <span className="text-xs sm:text-sm truncate">Filters</span>
                </div>
              </Button>
            </div>
          </div>

          {/* Expanded Filter Options */}
          <AnimatePresence>
            {isFilterExpanded && (
              <motion.div
                className="mt-4 pt-4 border-t border-neutral-200/50 dark:border-neutral-800/50"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
              >
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  {/* Additional filter options could go here */}
                  <div className="text-sm text-neutral-500 dark:text-neutral-400">
                    Additional filters coming soon...
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>

      {/* Results Count */}
      <motion.div
        className="container mx-auto px-4 flex items-center justify-between"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <div className="text-xs sm:text-sm text-neutral-500 dark:text-neutral-400 truncate">
          {debouncedSearchQuery && (
            <span>
              Showing results for{" "}
              <span className="font-medium text-neutral-700 dark:text-neutral-300 max-w-[150px] sm:max-w-none inline-block truncate">
                &quot;{debouncedSearchQuery}&quot;
              </span>
            </span>
          )}
        </div>

        {debouncedSearchQuery && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearSearch}
            className="text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 h-8 px-2 py-1"
          >
            <X className="h-3.5 w-3.5 mr-1" />
            Clear Search
          </Button>
        )}
      </motion.div>

      {/* Business Grid */}
      <div className="container mx-auto px-4">
        {isLoading || (!isLoading && businesses.length === 0 && isFirstRender.current) ? (
          <AnimatedBusinessGridSkeleton />
        ) : businesses.length > 0 ? (
          <AnimatedBusinessGrid
            businesses={businesses as unknown as BusinessCardData[]}
            isAuthenticated={isAuthenticated}
          />
        ) : (
          <motion.div
            className="text-center py-12 px-4 bg-white dark:bg-neutral-900 rounded-xl border border-neutral-200 dark:border-neutral-800 shadow-sm"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
          >
            <div className="max-w-md mx-auto">
              <h3 className="text-xl font-semibold text-neutral-900 dark:text-white mb-2">
                No Businesses Found
              </h3>
              <p className="text-neutral-500 dark:text-neutral-400 mb-4">
                We couldn&apos;t find any businesses
                {debouncedSearchQuery
                  ? ` with "${debouncedSearchQuery}" in their name`
                  : " in this location"}
                . Try adjusting your search criteria.
              </p>
              {debouncedSearchQuery && (
                <Button
                  variant="outline"
                  className="mt-2 border-[var(--brand-gold)]/30 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10"
                  onClick={handleClearSearch}
                >
                  <X className="h-4 w-4 mr-2" />
                  Clear Search
                </Button>
              )}
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
