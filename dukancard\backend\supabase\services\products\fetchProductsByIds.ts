'use server';

import { createAdminClient } from '@/utils/supabase/admin';

export interface ProductData {
  id: string;
  name: string;
  base_price: number | null;
  discounted_price: number | null;
  image_url: string | null;
  slug: string | null;
}

/**
 * Fetch products by their IDs using admin client to bypass RLS
 * This is used for displaying linked products in feed posts
 */
export async function fetchProductsByIds(productIds: string[]): Promise<{
  success: boolean;
  data?: ProductData[];
  error?: string;
}> {
  if (!productIds || productIds.length === 0) {
    return {
      success: true,
      data: []
    };
  }

  try {
    // Use admin client to bypass RLS policies
    const supabaseAdmin = createAdminClient();

    const { data, error } = await supabaseAdmin
      .from('products_services')
      .select('id, name, base_price, discounted_price, image_url, slug')
      .in('id', productIds)
      .eq('is_available', true);

    if (error) {
      console.error('Error fetching products by IDs:', error);
      return {
        success: false,
        error: 'Failed to fetch products'
      };
    }

    return {
      success: true,
      data: data || []
    };
  } catch (error) {
    console.error('Error in fetchProductsByIds:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}
