# Custom Ads Quick Reference Guide

This guide provides quick reference for common custom ad management tasks in Dukancard.

## Adding Ads

### Add a Global Ad

```sql
SELECT add_custom_ad(
  'https://example.com/global-ad.jpg',  -- ad_image_url
  'https://example.com/landing',        -- ad_link_url
  TRUE,                                 -- is_active
  TRUE,                                 -- is_global
  NOW() + INTERVAL '1 year'             -- expiry_date
);
```

### Add a Pincode-Targeted Ad

```sql
SELECT add_custom_ad(
  'https://example.com/delhi-ad.jpg',   -- ad_image_url
  'https://example.com/delhi',          -- ad_link_url
  TRUE,                                 -- is_active
  FALSE,                                -- is_global
  NOW() + INTERVAL '6 months',          -- expiry_date
  ARRAY['110001', '110002', '110003']   -- pincodes
);
```

## Updating Ads

### Update Ad Content

```sql
SELECT update_custom_ad(
  'ad-uuid-here',                       -- ad_id
  'https://example.com/new-image.jpg',  -- ad_image_url
  'https://example.com/new-link',       -- ad_link_url
  NULL,                                 -- is_active (no change)
  NULL,                                 -- expiry_date (no change)
  NULL,                                 -- is_global (no change)
  NULL                                  -- pincodes (no change)
);
```

### Update Expiry Date

```sql
SELECT update_custom_ad(
  'ad-uuid-here',                       -- ad_id
  NULL,                                 -- ad_image_url (no change)
  NULL,                                 -- ad_link_url (no change)
  NULL,                                 -- is_active (no change)
  NOW() + INTERVAL '1 year',            -- expiry_date
  NULL,                                 -- is_global (no change)
  NULL                                  -- pincodes (no change)
);
```

### Change from Pincode-Specific to Global

```sql
SELECT update_custom_ad(
  'ad-uuid-here',                       -- ad_id
  NULL,                                 -- ad_image_url (no change)
  NULL,                                 -- ad_link_url (no change)
  NULL,                                 -- is_active (no change)
  NULL,                                 -- expiry_date (no change)
  TRUE,                                 -- is_global
  NULL                                  -- pincodes (ignored for global ads)
);
```

### Change from Global to Pincode-Specific

```sql
SELECT update_custom_ad(
  'ad-uuid-here',                       -- ad_id
  NULL,                                 -- ad_image_url (no change)
  NULL,                                 -- ad_link_url (no change)
  NULL,                                 -- is_active (no change)
  NULL,                                 -- expiry_date (no change)
  FALSE,                                -- is_global
  ARRAY['400001', '400002', '400003']   -- pincodes
);
```

### Deactivate an Ad

```sql
SELECT update_custom_ad(
  'ad-uuid-here',                       -- ad_id
  NULL,                                 -- ad_image_url (no change)
  NULL,                                 -- ad_link_url (no change)
  FALSE,                                -- is_active
  NULL,                                 -- expiry_date (no change)
  NULL,                                 -- is_global (no change)
  NULL                                  -- pincodes (no change)
);
```

## Deleting Ads

```sql
SELECT delete_custom_ad('ad-uuid-here');
```

## Querying Ads

### Get Ad for a Specific Pincode

```sql
SELECT * FROM get_ad_for_pincode('110001');
```

### Get All Ads for a Pincode

```sql
SELECT * FROM get_all_ads_for_pincode('110001');
```

### Get Ad Statistics

```sql
SELECT * FROM get_custom_ads_stats();
```

### Get Most Targeted Pincodes

```sql
SELECT * FROM get_most_targeted_pincodes(10);
```

### View All Ads and Their Targets

```sql
SELECT * FROM ad_targets_view;
```

### View Expired Ads

```sql
SELECT * FROM expired_ads_view;
```

## Manually Clean Up Expired Ads

```sql
SELECT cleanup_expired_ads();
```

## Finding Ads by Various Criteria

### Find Ads by Image URL

```sql
SELECT * FROM custom_ads WHERE ad_image_url LIKE '%example.com%';
```

### Find Ads by Link URL

```sql
SELECT * FROM custom_ads WHERE ad_link_url LIKE '%example.com%';
```

### Find Ads Expiring Soon

```sql
SELECT * FROM custom_ads 
WHERE expiry_date IS NOT NULL 
AND expiry_date > NOW() 
AND expiry_date <= NOW() + INTERVAL '7 days';
```

### Find Inactive Ads

```sql
SELECT * FROM custom_ads WHERE is_active = FALSE;
```

### Find Global Ads

```sql
SELECT c.* 
FROM custom_ads c
JOIN custom_ad_targets t ON c.id = t.ad_id
WHERE t.is_global = TRUE;
```

### Find Ads Targeting a Specific Pincode

```sql
SELECT c.* 
FROM custom_ads c
JOIN custom_ad_targets t ON c.id = t.ad_id
WHERE t.pincode = '110001' AND t.is_global = FALSE;
```
