import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { getInvoicesForSubscription } from "@/lib/razorpay/services/invoice";

/**
 * GET /api/subscriptions/:id/invoices
 *
 * Fetches all invoices for a subscription from Razorpay
 *
 * Example response:
 * ```json
 * {
 *   "success": true,
 *   "invoices": {
 *     "entity": "collection",
 *     "count": 2,
 *     "items": [
 *       {
 *         "id": "inv_00000000000003",
 *         "entity": "invoice",
 *         "receipt": null,
 *         "invoice_number": null,
 *         "customer_id": "cust_00000000000001",
 *         "customer_details": {
 *           "id": "cust_00000000000001",
 *           "name": null,
 *           "email": "<EMAIL>",
 *           "contact": "+919876543210",
 *           "gstin": null,
 *           "billing_address": null,
 *           "shipping_address": null,
 *           "customer_name": null,
 *           "customer_email": "<EMAIL>",
 *           "customer_contact": "+919876543210"
 *         },
 *         "order_id": "order_00000000000002",
 *         "subscription_id": "sub_00000000000001",
 *         "line_items": [
 *           {
 *             "id": "li_00000000000003",
 *             "item_id": null,
 *             "ref_id": null,
 *             "ref_type": null,
 *             "name": "Monthly Plan",
 *             "description": null,
 *             "amount": 99900,
 *             "unit_amount": 99900,
 *             "gross_amount": 99900,
 *             "tax_amount": 0,
 *             "taxable_amount": 99900,
 *             "net_amount": 99900,
 *             "currency": "INR",
 *             "type": "plan",
 *             "tax_inclusive": false,
 *             "hsn_code": null,
 *             "sac_code": null,
 *             "tax_rate": null,
 *             "unit": null,
 *             "quantity": 1,
 *             "taxes": []
 *           }
 *         ],
 *         "payment_id": "pay_00000000000002",
 *         "status": "paid",
 *         "expire_by": null,
 *         "issued_at": 1593344888,
 *         "paid_at": 1593344889,
 *         "cancelled_at": null,
 *         "expired_at": null,
 *         "sms_status": null,
 *         "email_status": null,
 *         "date": 1593344888,
 *         "terms": null,
 *         "partial_payment": false,
 *         "gross_amount": 99900,
 *         "tax_amount": 0,
 *         "taxable_amount": 99900,
 *         "amount": 99900,
 *         "amount_paid": 99900,
 *         "amount_due": 0,
 *         "currency": "INR",
 *         "currency_symbol": "₹",
 *         "description": null,
 *         "notes": [],
 *         "comment": null,
 *         "short_url": "https://rzp.io/i/Ys4feGqEp",
 *         "view_less": true,
 *         "billing_start": 1594405800,
 *         "billing_end": 1597084200,
 *         "type": "invoice",
 *         "group_taxes_discounts": false,
 *         "created_at": 1593344888,
 *         "idempotency_key": null
 *       }
 *     ]
 *   }
 * }
 * ```
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the subscription ID from the URL params
    const { id: subscriptionId } = await params;

    // Get pagination parameters from query string
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1', 10);
    const count = parseInt(searchParams.get('count') || '10', 10);

    // Verify authentication using Supabase
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if the user has access to this subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("*")
      .eq("razorpay_subscription_id", subscriptionId)
      .maybeSingle();

    if (subscriptionError) {
      console.error("[RAZORPAY_ERROR] Error fetching subscription:", subscriptionError);
      return NextResponse.json(
        { success: false, error: "Error fetching subscription" },
        { status: 500 }
      );
    }

    // If no subscription is found, it might have been cancelled or switched
    // We'll still try to fetch invoices directly from Razorpay after verifying the user
    if (!subscription) {
      console.log(`[SUBSCRIPTION_INVOICES] No active subscription found for ID: ${subscriptionId}, checking user authorization`);

      // Check if this user exists
      const { data: businessProfile } = await supabase
        .from("business_profiles")
        .select("id")
        .eq("id", user.id)
        .single();

      if (!businessProfile) {
        return NextResponse.json(
          { success: false, error: "Unauthorized to access this subscription" },
          { status: 403 }
        );
      }

      // User exists, proceed to fetch invoices directly from Razorpay
    } else {
      // If we have a subscription, check if the user is authorized to access it
      const isOwner = subscription.business_profile_id === user.id;

      if (!isOwner) {
        return NextResponse.json(
          { success: false, error: "Unauthorized to access this subscription" },
          { status: 403 }
        );
      }
    }

    // Fetch invoices from Razorpay with pagination
    const result = await getInvoicesForSubscription(subscriptionId, page, count);

    if (!result.success) {
      console.error("[RAZORPAY_ERROR] Error fetching subscription invoices:", result.error);
      return NextResponse.json(
        { success: false, error: "Failed to fetch subscription invoices" },
        { status: 500 }
      );
    }

    // Return paginated data with metadata
    return NextResponse.json({
      success: true,
      invoices: result.data,
      pagination: {
        page,
        count,
        totalCount: result.data?.count || 0,
        totalPages: Math.ceil((result.data?.count || 0) / count)
      }
    });
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Unexpected error in invoices API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "An unexpected error occurred"
      },
      { status: 500 }
    );
  }
}
