"use server";

import { createAdminClient } from "@/utils/supabase/admin";
import { BusinessProfilePublicData, BusinessSortBy } from "./types";
import { applySorting, createSubscriptionMap } from "./utils";

/**
 * Securely fetch business profiles for discover page using the service role key
 */
export async function getSecureBusinessProfilesForDiscover(
  pincodes: string | string[],
  locality?: string | null,
  page: number = 1,
  limit: number = 10,
  sortBy: BusinessSortBy = "created_desc"
): Promise<{
  data?: BusinessProfilePublicData[];
  count?: number;
  error?: string;
}> {
  if (!pincodes || (Array.isArray(pincodes) && pincodes.length === 0)) {
    return { error: "At least one pincode is required." };
  }

  // Convert single pincode to array for consistent handling
  const pincodeArray = Array.isArray(pincodes) ? pincodes : [pincodes];

  try {
    // Use the admin client with service role key to bypass RLS
    const supabaseAdmin = createAdminClient();
    const offset = (page - 1) * limit;

    // Get total count of online businesses
    const countQuery = supabaseAdmin
      .from("business_profiles")
      .select("id", { count: "exact" })
      .in("pincode", pincodeArray)
      .eq("status", "online");

    // Add locality filter if provided
    if (locality) {
      countQuery.eq("locality", locality);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error("Count Error:", countError);
      return { error: "Database error counting profiles." };
    }

    // If there are no profiles, return empty array
    if (!count || count === 0) {
      return { data: [], count: 0 };
    }

    // Define fields to select
    const businessFields = `
      id, business_name, logo_url, member_name, title,
      address_line, city, state, pincode, locality, phone, instagram_url,
      facebook_url, whatsapp_number, about_bio, status, business_slug, theme_color,
      delivery_info, total_likes, total_subscriptions, average_rating, business_hours,
      business_category, trial_end_date, created_at, updated_at, contact_email, established_year, google_maps_url,
      custom_branding, custom_ads
    `;

    // Build the query
    let businessQuery = supabaseAdmin
      .from("business_profiles")
      .select(businessFields)
      .in("pincode", pincodeArray)
      .eq("status", "online")
      .range(offset, offset + limit - 1);

    // Add locality filter if provided
    if (locality) {
      businessQuery.eq("locality", locality);
    }

    // Apply sorting
    businessQuery = applySorting(businessQuery, sortBy);

    // Execute the query
    const { data, error } = await businessQuery;

    if (error) {
      console.error("Query Error:", error);
      return { error: "Database error fetching profiles." };
    }

    // Get subscription data for all profiles
    const profileIds = data.map(profile => profile.id);

    // Fetch subscription data for all profiles
    const { data: subscriptionsData, error: subscriptionsError } = await supabaseAdmin
      .from("payment_subscriptions")
      .select("business_profile_id, subscription_status, plan_id")
      .in("business_profile_id", profileIds)
      .order("created_at", { ascending: false });

    if (subscriptionsError) {
      console.error("Error fetching subscription data:", subscriptionsError);
    }

    // Create a map of business_profile_id to subscription data
    const subscriptionMap = createSubscriptionMap(subscriptionsData);

    // Filter out sensitive data before returning
    const safeData: BusinessProfilePublicData[] = data.map((profile) => {
      // Get subscription data for this profile
      const subData = subscriptionMap.get(profile.id) || {
        subscription_status: null,
        plan_id: null
      };

      return {
        id: profile.id,
        business_name: profile.business_name,
        logo_url: profile.logo_url,
        member_name: profile.member_name,
        title: profile.title,
        address_line: profile.address_line,
        city: profile.city,
        state: profile.state,
        pincode: profile.pincode,
        locality: profile.locality,
        phone: profile.phone,
        instagram_url: profile.instagram_url,
        facebook_url: profile.facebook_url,
        whatsapp_number: profile.whatsapp_number,
        about_bio: profile.about_bio,
        status: profile.status,
        business_slug: profile.business_slug,
        theme_color: profile.theme_color,
        delivery_info: profile.delivery_info,
        business_hours: profile.business_hours,
        business_category: profile.business_category,
        google_maps_url: profile.google_maps_url,
        established_year: profile.established_year,
        total_likes: profile.total_likes,
        total_subscriptions: profile.total_subscriptions,
        average_rating: profile.average_rating,
        custom_branding: profile.custom_branding,
        custom_ads: profile.custom_ads,
        created_at: profile.created_at,
        updated_at: profile.updated_at,
        trial_end_date: profile.trial_end_date,
        contact_email: profile.contact_email,
        subscription_status: subData.subscription_status,
        plan_id: subData.plan_id
      };
    });

    return { data: safeData, count: count || 0 };
  } catch (e) {
    console.error("Exception in getSecureBusinessProfilesForDiscover:", e);
    return { error: "An unexpected error occurred." };
  }
}

/**
 * Securely fetch business profile IDs for discover page products using the service role key
 */
export async function getSecureBusinessProfileIdsForDiscover(
  pincodes: string | string[],
  locality?: string | null,
  sortBy: BusinessSortBy = "created_desc"
): Promise<{
  data?: string[];
  error?: string;
}> {
  if (!pincodes || (Array.isArray(pincodes) && pincodes.length === 0)) {
    return { error: "At least one pincode is required." };
  }

  // Convert single pincode to array for consistent handling
  const pincodeArray = Array.isArray(pincodes) ? pincodes : [pincodes];

  try {
    // Use the admin client with service role key to bypass RLS
    const supabaseAdmin = createAdminClient();

    // Build query for online businesses
    let validBusinessQuery = supabaseAdmin
      .from("business_profiles")
      .select("id")
      .in("pincode", pincodeArray)
      .eq("status", "online");

    // Add locality filter if provided
    if (locality) {
      validBusinessQuery.eq("locality", locality);
    }

    // Apply sorting
    validBusinessQuery = applySorting(validBusinessQuery, sortBy);

    // Execute the query
    const { data, error } = await validBusinessQuery;

    if (error) {
      console.error("Query Error:", error);
      return { error: "Database error fetching profile IDs." };
    }

    // We only need the IDs for this function, so we don't need to fetch subscription data
    // Just return the IDs
    const safeData = data.map((profile) => profile.id);

    return { data: safeData };
  } catch (e) {
    console.error("Exception in getSecureBusinessProfileIdsForDiscover:", e);
    return { error: "An unexpected error occurred." };
  }
}
