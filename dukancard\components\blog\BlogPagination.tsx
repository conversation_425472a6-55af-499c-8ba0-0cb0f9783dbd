"use client";

import {
  Pa<PERSON><PERSON>,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { type PaginationInfo } from "@/lib/types/blog";
import { generatePageNumbers, getDisplayRange } from "@/lib/utils/pagination";

interface BlogPaginationProps {
  paginationInfo: PaginationInfo;
  onPageChange: (_page: number) => void;
  className?: string;
}

export default function BlogPagination({
  paginationInfo,
  onPageChange,
  className,
}: BlogPaginationProps) {
  const { currentPage, totalPages, hasPreviousPage, hasNextPage } = paginationInfo;

  // Don't render pagination if there's only one page or no pages
  if (totalPages <= 1) {
    return null;
  }

  // Generate page numbers to display
  const pageNumbers = generatePageNumbers(currentPage, totalPages);

  return (
    <Pagination className={className}>
      <PaginationContent>
        {/* Previous Button */}
        <PaginationItem>
          <PaginationPrevious
            onClick={() => hasPreviousPage && onPageChange(currentPage - 1)}
            className={
              !hasPreviousPage
                ? "pointer-events-none opacity-50"
                : "cursor-pointer"
            }
          />
        </PaginationItem>

        {/* Page Numbers */}
        {pageNumbers.map((pageItem, index) => (
          <PaginationItem key={index}>
            {pageItem === 'ellipsis' ? (
              <PaginationEllipsis />
            ) : (
              <PaginationLink
                onClick={() => onPageChange(pageItem as number)}
                isActive={pageItem === currentPage}
                className="cursor-pointer"
              >
                {pageItem}
              </PaginationLink>
            )}
          </PaginationItem>
        ))}

        {/* Next Button */}
        <PaginationItem>
          <PaginationNext
            onClick={() => hasNextPage && onPageChange(currentPage + 1)}
            className={
              !hasNextPage
                ? "pointer-events-none opacity-50"
                : "cursor-pointer"
            }
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
}

// Simple pagination info display component
export function PaginationInfo({ paginationInfo }: { paginationInfo: PaginationInfo }) {
  const { currentPage, totalPages, totalCount } = paginationInfo;

  if (totalCount === 0) {
    return (
      <p className="text-sm text-muted-foreground text-center">
        No blog posts found
      </p>
    );
  }

  const { start, end } = getDisplayRange(currentPage, totalCount, paginationInfo.limit);

  return (
    <p className="text-sm text-muted-foreground text-center">
      Showing {start} to {end} of {totalCount} blog posts
      {totalPages > 1 && ` (Page ${currentPage} of ${totalPages})`}
    </p>
  );
}
