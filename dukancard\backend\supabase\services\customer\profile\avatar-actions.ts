'use server';

import { createClient } from '../../../utils/supabase/server';
import { getCustomerAvatarPath } from '../../../lib/utils/storage-paths';
import { revalidatePath } from 'next/cache';

// Action to upload avatar and return public URL
export async function uploadAvatarAndGetUrl(
  formData: FormData
): Promise<{ success: boolean; url?: string; error?: string }> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }
  const userId = user.id;

  const file = formData.get("avatarFile") as File | null;
  if (!file) {
    return { success: false, error: "No avatar file provided." };
  }

  const allowedTypes = ["image/png", "image/jpeg", "image/gif", "image/webp"];
  if (!allowedTypes.includes(file.type)) {
    return { success: false, error: "Invalid file type." };
  }

  // Server-side file size validation (15MB limit)
  if (file.size > 15 * 1024 * 1024) {
    return { success: false, error: "File size must be less than 15MB." };
  }

  try {
    // File is already compressed on client-side, just upload it
    const fileBuffer = Buffer.from(await file.arrayBuffer());

    const bucketName = "customers"; // Plural form - matches the bucket name
    const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000); // Add random milliseconds for extra uniqueness
    const fullPath = getCustomerAvatarPath(userId, timestamp);

    // Upload the processed image
    const { error: uploadError } = await supabase.storage
      .from(bucketName)
      .upload(fullPath, fileBuffer, {
        contentType: file.type, // Use original file type (already compressed)
        upsert: true
      });

    if (uploadError) {
      console.error("Avatar Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload avatar: ${uploadError.message}`,
      };
    }

    // No need to add timestamp to URL as we already have it in the filename
    const { data: urlData } = supabase.storage
      .from(bucketName)
      .getPublicUrl(fullPath);

    if (!urlData?.publicUrl) {
      console.error(
        "Get Public URL Error: URL data is null or missing publicUrl property for path:",
        fullPath
      );
      return {
        success: false,
        error: "Could not retrieve public URL after upload.",
      };
    }

    return { success: true, url: urlData.publicUrl };
  } catch (processingError) {
    console.error("Image Processing/Upload Error:", processingError);
    return { success: false, error: "Failed to process or upload image." };
  }
}

// Action to specifically update only the avatar URL
export async function updateAvatarUrl(
  avatarUrl: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  const { error: updateError } = await supabase
    .from("customer_profiles")
    .update({ avatar_url: avatarUrl, updated_at: new Date().toISOString() })
    .eq("id", user.id);

  if (updateError) {
    console.error("Avatar URL Update Error:", updateError);
    return {
      success: false,
      error: `Failed to update avatar URL: ${updateError.message}`,
    };
  }

  // Revalidate paths to update the UI
  revalidatePath('/dashboard/customer');
  revalidatePath('/dashboard/customer/profile');

  return { success: true };
}
