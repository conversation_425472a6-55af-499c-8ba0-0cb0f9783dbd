# Business Profiles Table Documentation

## Table Overview

The `business_profiles` table is a core table in the Dukancard application that stores information about business profiles. Each business profile is linked to a user account and contains details about the business such as name, contact information, address, subscription status, and various metrics.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | | Primary key, references users.id |
| business_name | text | NO | | Name of the business |
| contact_email | text | YES | | Email address for business contact (unique) |
| has_active_subscription | boolean | NO | false | Indicates if the business has an active paid subscription |
| trial_end_date | timestamptz | YES | | Date when the trial period ends |
| created_at | timestamptz | NO | now() | Timestamp when the record was created |
| updated_at | timestamptz | NO | now() | Timestamp when the record was last updated |
| logo_url | text | YES | | URL to the business logo image |
| member_name | text | YES | | Name of the business owner/member |
| phone | text | YES | | Business phone number |
| instagram_url | text | YES | | URL to the business Instagram profile |
| whatsapp_number | text | YES | | WhatsApp number for the business |
| status | text | NO | 'offline' | Business status (online/offline) |
| title | text | YES | | Business title or tagline |
| address_line | text | YES | | Street address of the business |
| city | text | YES | | City where the business is located |
| state | text | YES | | State where the business is located |
| pincode | text | YES | | Postal/ZIP code |
| locality | text | YES | | Specific locality or area within the city |
| about_bio | text | YES | | Description or bio of the business |
| facebook_url | text | YES | | URL to the business Facebook page |
| average_rating | numeric(2,1) | YES | 0.0 | Average rating of the business |
| total_likes | integer | NO | 0 | Total number of likes received |
| total_subscriptions | integer | NO | 0 | Total number of subscriptions/followers |
| theme_color | text | YES | | Theme color for the business profile |
| business_hours | jsonb | YES | | Business operating hours in JSON format |
| delivery_info | text | YES | | Information about delivery services |
| total_visits | integer | NO | 0 | Total number of profile visits |
| today_visits | integer | NO | 0 | Number of visits today |
| yesterday_visits | integer | NO | 0 | Number of visits yesterday |
| visits_7_days | integer | NO | 0 | Number of visits in the last 7 days |
| visits_30_days | integer | NO | 0 | Number of visits in the last 30 days |
| business_category | text | YES | | Category of the business |
| business_slug | text | YES | | Unique slug for the business URL |
| gallery | jsonb | YES | '[]' | JSON array of gallery image URLs |
| city_slug | text | YES | | Slug version of the city name |
| state_slug | text | YES | | Slug version of the state name |
| locality_slug | text | YES | | Slug version of the locality name |
| custom_branding | jsonb | YES | '{}' | Custom branding settings (Pro/Enterprise only) |
| custom_ads | jsonb | YES | default | Custom advertisement settings (Pro/Enterprise only) |
| established_year | integer | YES | | Year the business was established |
| google_maps_url | text | YES | | Google Maps URL for business location |

## JSONB Column Structures

### custom_branding
Structure for custom branding settings (Pro/Enterprise plans only):
```json
{
  "custom_header_text": "string",
  "custom_header_image_url": "string",
  "custom_header_image_light_url": "string",
  "custom_header_image_dark_url": "string",
  "hide_dukancard_branding": boolean
}
```

**Fields:**
- `custom_header_text`: Custom text to display in header (max 50 characters)
- `custom_header_image_url`: Legacy URL to custom header image (for backward compatibility)
- `custom_header_image_light_url`: URL to custom header image for light theme (WEBP recommended)
- `custom_header_image_dark_url`: URL to custom header image for dark theme (WEBP recommended)
- `hide_dukancard_branding`: Whether to hide default Dukancard branding

**Theme-Specific Display Priority:**
1. **Theme-specific image** (light/dark based on current user theme)
2. **Cross-theme fallback** (if current theme image missing, use opposite theme)
3. **Legacy single image** (backward compatibility with `custom_header_image_url`)
4. **Custom text** (if no images available)
5. **Default Dukancard branding** (final fallback)

**Image Specifications:**
- **Format**: WebP recommended for optimal compression
- **Size Limit**: 5MB maximum upload size
- **Compressed Size**: Automatically compressed to ~150KB
- **Dimensions**: Automatically resized to max 400px while maintaining aspect ratio
- **Storage**: Theme-specific paths (`header_light_timestamp.webp`, `header_dark_timestamp.webp`)

### custom_ads
Structure for custom advertisement settings (Pro/Enterprise plans only):
```json
{
  "enabled": boolean,
  "image_url": "string",
  "link_url": "string",
  "uploaded_at": "ISO timestamp"
}
```

**Fields:**
- `enabled`: Whether the custom ad is active and should be displayed
- `image_url`: URL to the custom advertisement image (4:3 aspect ratio recommended)
- `link_url`: Optional URL to redirect when ad is clicked
- `uploaded_at`: Timestamp when the ad was uploaded

**Default Value:**
```json
{
  "enabled": false,
  "image_url": "",
  "link_url": "",
  "uploaded_at": null
}
```

## Theme-Specific Custom Branding Implementation

### Overview
The custom branding system supports theme-specific header images that automatically switch based on the user's current theme preference (light/dark mode). This provides a seamless user experience across different viewing preferences.

### Implementation Details

#### Frontend Theme Detection
- Uses `next-themes` library with `useTheme()` hook
- Detects `resolvedTheme` value: `"light"`, `"dark"`, or `"system"`
- Automatically switches images based on current theme

#### Upload System
- **Dual Upload Interface**: Side-by-side upload cards for light and dark themes
- **Individual Management**: Each theme image can be uploaded/deleted independently
- **Visual Indicators**: Shows current theme with colored indicators
- **Drag & Drop**: Full drag-and-drop support for both theme uploads

#### Storage Structure
Theme-specific images are stored with the following path structure:
```
business/
  └── {user_id_hash}/
      └── branding/
          ├── header_light_{timestamp}.webp
          ├── header_dark_{timestamp}.webp
          └── header_{timestamp}.webp (legacy)
```

#### Validation & Compression
- **Frontend Validation**: 5MB size limit with user-friendly error messages
- **Backend Validation**: Matching 5MB limit with detailed error reporting
- **Compression**: Ultra-aggressive compression targeting 150KB final size
- **Format**: All images converted to WebP for optimal performance

#### Backward Compatibility
- Existing single header images (`custom_header_image_url`) continue to work
- Legacy images serve as fallback when theme-specific images are unavailable
- Gradual migration path for existing users

#### Database Schema Updates
The `custom_branding` JSONB column now supports:
```json
{
  "custom_header_text": "Powered by YourBrand",
  "custom_header_image_url": "legacy_image_url.webp",
  "custom_header_image_light_url": "light_theme_image.webp",
  "custom_header_image_dark_url": "dark_theme_image.webp",
  "hide_dukancard_branding": true
}
```

## Constraints

### Primary Key
- `business_profiles_pkey` - Primary key constraint on the `id` column

### Foreign Keys
- `business_profiles_id_fkey` - Foreign key constraint linking `id` to `users.id`

### Unique Constraints
- `business_profiles_business_slug_key` - Ensures business_slug is unique

### Check Constraints
Multiple check constraints ensure that required fields are not null.

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| business_profiles_pkey | UNIQUE | id | Primary key index |
| business_profiles_business_slug_key | UNIQUE | business_slug | Unique constraint index |
| idx_business_profiles_city_slug | BTREE | city_slug | Index for faster city_slug lookups |
| idx_business_profiles_state_slug | BTREE | state_slug | Index for faster state_slug lookups |
| idx_business_profiles_locality_slug | BTREE | locality_slug | Index for faster locality_slug lookups |

## Triggers

### update_business_profile_slugs_trigger
- **Events**: INSERT, UPDATE
- **Function**: update_business_profile_slugs()
- **Description**: Updates the slug fields (city_slug, state_slug, locality_slug) when address-related fields are provided (INSERT) or changed (UPDATE)

### Trigger Function Definition

```sql
CREATE OR REPLACE FUNCTION public.update_business_profile_slugs()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
BEGIN
    -- For INSERT operations, always update slugs if address data exists
    -- For UPDATE operations, only update if address fields changed
    IF (TG_OP = 'INSERT' AND (NEW.pincode IS NOT NULL OR NEW.locality IS NOT NULL OR NEW.city IS NOT NULL OR NEW.state IS NOT NULL)) OR
       (TG_OP = 'UPDATE' AND ((NEW.pincode IS DISTINCT FROM OLD.pincode) OR
                              (NEW.locality IS DISTINCT FROM OLD.locality) OR
                              (NEW.city IS DISTINCT FROM OLD.city) OR
                              (NEW.state IS DISTINCT FROM OLD.state))) THEN

        -- Reset slug fields to NULL first
        NEW.city_slug := NULL;
        NEW.state_slug := NULL;
        NEW.locality_slug := NULL;

        -- Try to find matching pincode and locality
        SELECT p.city_slug, p.state_slug, p.locality_slug
        INTO NEW.city_slug, NEW.state_slug, NEW.locality_slug
        FROM pincodes p
        WHERE p.\"Pincode\" = NEW.pincode
          AND LOWER(p.\"OfficeName\") = LOWER(NEW.locality)
        LIMIT 1;

        -- If no match found, try to match by city
        IF NEW.city_slug IS NULL AND NEW.pincode IS NOT NULL AND NEW.city IS NOT NULL THEN
            SELECT p.city_slug, p.state_slug
            INTO NEW.city_slug, NEW.state_slug
            FROM pincodes p
            WHERE p.\"Pincode\" = NEW.pincode
              AND LOWER(p.\"DivisionName\") = LOWER(NEW.city)
            LIMIT 1;
        END IF;

        -- If still no match for state, try to match just by state
        IF NEW.state_slug IS NULL AND NEW.state IS NOT NULL THEN
            SELECT p.state_slug
            INTO NEW.state_slug
            FROM pincodes p
            WHERE LOWER(p.\"StateName\") = LOWER(NEW.state)
            LIMIT 1;
        END IF;
    END IF;

    RETURN NEW;
END;
$function$
```

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| Allow individual read access | SELECT | (auth.uid() = id) | |
| Allow individual insert access | INSERT | | |
| Allow individual update access | UPDATE | (auth.uid() = id) | |
| Allow individual delete access | DELETE | (auth.uid() = id) | |

These policies ensure that users can only access and modify their own business profile data.

## Related Tables

The following tables have foreign key relationships to the `business_profiles` table:

| Table Name | Column Name | Relationship |
|------------|-------------|-------------|
| business_activities | business_profile_id | Many-to-one |
| products_services | business_id | Many-to-one |
| subscriptions | business_profile_id | Many-to-one |
| ratings_reviews | business_profile_id | Many-to-one |
| likes | business_profile_id | Many-to-one |
| payment_subscriptions | business_profile_id | Many-to-one |
| monthly_visit_metrics | business_profile_id | Many-to-one |
| card_visits | business_profile_id | Many-to-one |

## Usage Notes

1. The `business_profiles` table is linked to the `users` table via the `id` column, which serves as both the primary key and a foreign key.
2. Business status is managed through the `status` field, with default value 'offline'.
3. Subscription status is tracked via `has_active_subscription` and related fields.
4. The table includes analytics fields for tracking visits (total, daily, weekly, monthly).
5. Location data includes city, state, pincode, and locality, with corresponding slug fields for URL-friendly versions.
6. The `update_business_profile_slugs` trigger automatically updates slug fields when:
   - **INSERT**: Address information is provided during business profile creation
   - **UPDATE**: Address information is modified in an existing business profile
7. Slug fields are automatically populated from the `pincodes` table using a hierarchical matching approach:
   - First priority: Match by pincode + locality (OfficeName)
   - Second priority: Match by pincode + city (DivisionName)
   - Third priority: Match by state (StateName)
8. RLS policies ensure that users can only access and modify their own business profile data.
9. The `business_slug` field must be unique and is used for generating user-friendly URLs.
10. **Product Limit Enforcement**: Product limits are enforced through database triggers based on subscription plans:
    - The `check_product_limit` trigger prevents adding/enabling products beyond plan limits
    - The `enforce_product_limits_on_plan_change` trigger automatically adjusts product availability when plan changes
    - Plan limits: Free (5), Basic (15), Growth (50), Pro/Enterprise (unlimited)
    - All enforcement is database-level and cannot be bypassed by application code
11. **Theme-Specific Custom Branding**: Pro/Enterprise users can upload separate header images for light and dark themes with automatic theme switching.

## Custom Branding API Endpoints

### Theme-Specific Header Upload
```typescript
// Upload light theme header
POST /api/theme-specific-header-upload
FormData: { image: File, theme: 'light' }

// Upload dark theme header
POST /api/theme-specific-header-upload
FormData: { image: File, theme: 'dark' }

// Delete theme-specific header
DELETE /api/theme-specific-header-upload
Body: { theme: 'light' | 'dark' }
```

### Usage Examples

#### Uploading Theme-Specific Headers
```typescript
// Frontend upload handler
const uploadThemeHeader = async (file: File, theme: 'light' | 'dark') => {
  const formData = new FormData();
  formData.append('image', file);

  const result = await uploadThemeSpecificHeaderImage(formData, theme);
  if (result.success) {
    // Update form with new image URL
    const fieldName = theme === 'light'
      ? 'custom_header_image_light_url'
      : 'custom_header_image_dark_url';
    form.setValue(`custom_branding.${fieldName}`, result.url);
  }
};
```

#### Theme-Aware Display Logic
```typescript
// Get appropriate header image based on current theme
const getHeaderImage = (customBranding, currentTheme) => {
  const isDark = currentTheme === 'dark';

  // Priority 1: Theme-specific image
  if (isDark && customBranding?.custom_header_image_dark_url) {
    return customBranding.custom_header_image_dark_url;
  }
  if (!isDark && customBranding?.custom_header_image_light_url) {
    return customBranding.custom_header_image_light_url;
  }

  // Priority 2: Cross-theme fallback
  if (isDark && customBranding?.custom_header_image_light_url) {
    return customBranding.custom_header_image_light_url;
  }
  if (!isDark && customBranding?.custom_header_image_dark_url) {
    return customBranding.custom_header_image_dark_url;
  }

  // Priority 3: Legacy single image
  if (customBranding?.custom_header_image_url) {
    return customBranding.custom_header_image_url;
  }

  return null; // Fall back to text or default branding
};
```
