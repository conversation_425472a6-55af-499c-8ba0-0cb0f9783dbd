'use server';

import { createClient } from '../../../utils/supabase/server';
import { createAdminClient } from '../../../utils/supabase/admin';
import { getScalableUserPath } from '../../../lib/utils/storage-paths';
import { z } from 'zod';
import { PasswordComplexitySchema } from '../../../../lib/schemas/authSchemas';
import { revalidatePath } from 'next/cache';

// --- Update Email ---

const UpdateEmailSchema = z.object({
  newEmail: z.string().email('Invalid email address.'),
});

export type UpdateEmailFormState = {
  message: string | null;
  errors?: {
    newEmail?: string[];
  };
  success: boolean;
};

export async function updateCustomerEmail(
  _prevState: UpdateEmailFormState,
  formData: FormData
): Promise<UpdateEmailFormState> {
  const supabase = await createClient();

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { message: 'Not authenticated', success: false };
  }

  const validatedFields = UpdateEmailSchema.safeParse({
    newEmail: formData.get('newEmail'),
  });

  if (!validatedFields.success) {
    return {
      message: 'Invalid data provided.',
      errors: validatedFields.error.flatten().fieldErrors,
      success: false,
    };
  }

  const { newEmail } = validatedFields.data;

  // Check if the new email is the same as the current one
  if (newEmail === user.email) {
    return { message: 'New email is the same as the current email.', success: false };
  }

  try {
    // Update email in Supabase Auth
    // This typically sends a confirmation email to both old and new addresses
    const { error: updateError } = await supabase.auth.updateUser({
      email: newEmail,
    });

    if (updateError) {
      // Handle specific Supabase auth error codes
      let errorMessage = 'Failed to update email address.';

      switch (updateError.code) {
        case 'email_exists':
          errorMessage = 'This email address is already registered with another account.';
          break;
        case 'invalid_email':
          errorMessage = 'Please enter a valid email address.';
          break;
        case 'email_change_confirm_limit':
          errorMessage = 'Too many email change requests. Please wait before trying again.';
          break;
        case 'over_email_send_rate_limit':
          errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';
          break;
        case 'email_not_confirmed':
          errorMessage = 'Please confirm your current email address before changing it.';
          break;
        case 'same_email':
          errorMessage = 'The new email address is the same as your current email.';
          break;
        default:
          errorMessage = 'Unable to update email address. Please try again later.';
      }

      return { message: errorMessage, success: false };
    }

    // Revalidate relevant paths if needed, though email change might require user action (confirmation)
    // revalidatePath('/dashboard/customer/settings');
    // revalidatePath('/dashboard/customer/profile'); // Email is shown here

    return {
      message: 'Confirmation email sent to both old and new addresses. Please check your inbox to complete the change.',
      success: true,
    };
  } catch (_error) {
    return { message: 'An unexpected error occurred while updating email.', success: false };
  }
}

// --- Link Email ---

const LinkEmailSchema = z.object({
  email: z.string().email('Invalid email address.'),
});

export type LinkEmailFormState = {
  message: string | null;
  errors?: {
    email?: string[];
  };
  success: boolean;
};

export async function linkCustomerEmail(
  _prevState: LinkEmailFormState,
  formData: FormData
): Promise<LinkEmailFormState> {
  const supabase = await createClient();

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { message: 'Not authenticated', success: false };
  }

  const validatedFields = LinkEmailSchema.safeParse({
    email: formData.get('email'),
  });

  if (!validatedFields.success) {
    return {
      message: 'Invalid data provided.',
      errors: validatedFields.error.flatten().fieldErrors,
      success: false,
    };
  }

  const { email } = validatedFields.data;

  try {
    // Check if user already has an email (email update) or not (email linking)
    const isEmailUpdate = !!user.email;

    if (isEmailUpdate) {
      // User already has email - use email change flow
      const { error: authUpdateError } = await supabase.auth.updateUser({
        email: email,
      });

      if (authUpdateError) {
        // Handle specific Supabase auth error codes
        let errorMessage = 'Failed to update email address.';

        switch (authUpdateError.code) {
          case 'email_exists':
            errorMessage = 'This email address is already registered with another account.';
            break;
          case 'invalid_email':
            errorMessage = 'Please enter a valid email address.';
            break;
          case 'email_change_confirm_limit':
            errorMessage = 'Too many email change requests. Please wait before trying again.';
            break;
          case 'over_email_send_rate_limit':
            errorMessage = 'Email rate limit exceeded. Please wait before requesting another verification email.';
            break;
          case 'email_not_confirmed':
            errorMessage = 'Please confirm your current email address before changing it.';
            break;
          case 'same_email':
            errorMessage = 'The new email address is the same as your current email.';
            break;
          default:
            errorMessage = 'Unable to update email address. Please try again later.';
        }

        return { message: errorMessage, success: false };
      }

      // Note: customer_profiles table will be automatically updated via database trigger

      return {
        message: 'Verification email sent to both old and new addresses. Please check your inbox to complete the change.',
        success: true,
      };
    } else {
      // User doesn't have email - directly link the email without OTP verification
      // Supabase will automatically handle duplicate validation
      const { error: updateError } = await supabase.auth.updateUser({
        email: email,
      });

      if (updateError) {
        let errorMessage = 'Failed to link email address.';

        switch (updateError.code) {
          case 'email_exists':
            errorMessage = 'This email address is already registered with another account.';
            break;
          case 'invalid_email':
            errorMessage = 'Please enter a valid email address.';
            break;
          case 'email_change_confirm_limit':
            errorMessage = 'Too many email requests. Please wait before trying again.';
            break;
          case 'over_email_send_rate_limit':
            errorMessage = 'Email rate limit exceeded. Please wait before trying again.';
            break;
          default:
            errorMessage = 'Unable to link email address. Please try again later.';
        }

        return { message: errorMessage, success: false };
      }

      // Note: customer_profiles table will be automatically updated via database trigger

      return {
        message: 'Email address linked successfully!',
        success: true,
      };
    }
  } catch (_error) {
    return { message: 'An unexpected error occurred while linking email.', success: false };
  }
}

// --- Verify Email OTP ---

const VerifyEmailOTPSchema = z.object({
  email: z.string().email('Invalid email address.'),
  otp: z.string().min(6, 'OTP must be 6 digits.').max(6, 'OTP must be 6 digits.'),
});

export type VerifyEmailOTPFormState = {
  message: string | null;
  errors?: {
    email?: string[];
    otp?: string[];
  };
  success: boolean;
};

export async function verifyEmailOTP(
  _prevState: VerifyEmailOTPFormState,
  formData: FormData
): Promise<VerifyEmailOTPFormState> {
  const supabase = await createClient();

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { message: 'Not authenticated', success: false };
  }

  const validatedFields = VerifyEmailOTPSchema.safeParse({
    email: formData.get('email'),
    otp: formData.get('otp'),
  });

  if (!validatedFields.success) {
    return {
      message: 'Invalid data provided.',
      errors: validatedFields.error.flatten().fieldErrors,
      success: false,
    };
  }

  const { email, otp } = validatedFields.data;

  try {
    // Verify the OTP
    const { error: verifyError } = await supabase.auth.verifyOtp({
      email: email,
      token: otp,
      type: 'email',
    });

    if (verifyError) {
      let errorMessage = 'Failed to verify code.';

      switch (verifyError.code) {
        case 'invalid_otp':
        case 'expired_otp':
          errorMessage = 'Invalid or expired verification code. Please try again.';
          break;
        case 'too_many_requests':
          errorMessage = 'Too many verification attempts. Please wait before trying again.';
          break;
        default:
          errorMessage = 'Unable to verify code. Please try again.';
      }

      return { message: errorMessage, success: false };
    }

    // If OTP verification successful, update the user's email
    const { error: updateError } = await supabase.auth.updateUser({
      email: email,
    });

    if (updateError) {
      return { message: 'Verification successful but failed to link email. Please contact support.', success: false };
    }

    // Note: customer_profiles table will be automatically updated via database trigger

    return {
      message: 'Email address linked successfully!',
      success: true,
    };
  } catch (_error) {
    return { message: 'An unexpected error occurred while verifying code.', success: false };
  }
}

// --- Update Password ---

const UpdatePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required.'),
  newPassword: PasswordComplexitySchema,
});

export type UpdatePasswordFormState = {
  message: string | null;
  errors?: {
    currentPassword?: string[];
    newPassword?: string[];
  };
  success: boolean;
};

export async function updateCustomerPassword(
  _prevState: UpdatePasswordFormState,
  formData: FormData
): Promise<UpdatePasswordFormState> {
  const supabase = await createClient();

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user || !user.email) { // Need email for re-authentication check
    return { message: 'Not authenticated', success: false };
  }

  const validatedFields = UpdatePasswordSchema.safeParse({
    currentPassword: formData.get('currentPassword'),
    newPassword: formData.get('newPassword'),
  });

  if (!validatedFields.success) {
    return {
      message: 'Invalid data provided.',
      errors: validatedFields.error.flatten().fieldErrors,
      success: false,
    };
  }

  const { currentPassword, newPassword } = validatedFields.data;

  // IMPORTANT: Verify the current password first before attempting update.
  // Supabase doesn't directly expose a "verify password" endpoint.
  // The recommended way is to try signing in with the current password.
  // This is a crucial security step.
  const { error: signInError } = await supabase.auth.signInWithPassword({
    email: user.email,
    password: currentPassword,
  });

  if (signInError) {
     // Handle specific Supabase auth error codes for sign-in
     let errorMessage = 'Failed to verify current password.';
     const fieldErrors: { currentPassword?: string[] } = {};

     switch (signInError.code) {
       case 'invalid_credentials':
       case 'email_not_confirmed':
         errorMessage = 'Incorrect current password.';
         fieldErrors.currentPassword = ['Incorrect current password.'];
         break;
       case 'too_many_requests':
         errorMessage = 'Too many failed attempts. Please wait before trying again.';
         break;
       case 'user_not_found':
         errorMessage = 'Account not found. Please contact support.';
         break;
       default:
         errorMessage = 'Unable to verify current password. Please try again.';
     }

     return {
       message: errorMessage,
       errors: fieldErrors,
       success: false
     };
  }


  // If sign-in was successful (current password is correct), proceed to update
  try {
    const { error: updateError } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (updateError) {
      // Handle specific Supabase auth error codes for password update
      let errorMessage = 'Failed to update password.';

      switch (updateError.code) {
        case 'weak_password':
          errorMessage = 'Password is too weak. Please choose a stronger password.';
          break;
        case 'same_password':
          errorMessage = 'New password must be different from your current password.';
          break;
        case 'password_too_short':
          errorMessage = 'Password must be at least 6 characters long.';
          break;
        case 'too_many_requests':
          errorMessage = 'Too many password change requests. Please wait before trying again.';
          break;
        default:
          errorMessage = 'Unable to update password. Please try again later.';
      }

      return { message: errorMessage, success: false };
    }

    // Password updated successfully
    return { message: 'Password updated successfully!', success: true };

  } catch (_error) {
    return { message: 'An unexpected error occurred while updating password.', success: false };
  }
}


// --- Enhanced Delete Account Security Actions ---

// Check user's email and phone availability for delete account verification
export async function checkDeleteAccountVerificationOptions(): Promise<{
  success: boolean;
  hasEmail: boolean;
  hasPhone: boolean;
  message?: string;
}> {
  const supabase = await createClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      hasEmail: false,
      hasPhone: false,
      message: "Authentication required."
    };
  }

  const hasEmail = !!(user.email && user.email.trim() !== '');
  const hasPhone = !!(user.phone && user.phone.trim() !== '');

  return {
    success: true,
    hasEmail,
    hasPhone,
  };
}

// Send OTP to email for delete account verification
export async function sendDeleteAccountOTP(): Promise<{
  success: boolean;
  message: string;
  email?: string;
  isConfigurationError?: boolean;
}> {
  const supabase = await createClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user || !user.email) {
    return {
      success: false,
      message: "Authentication required or no email found."
    };
  }

  try {
    const { error } = await supabase.auth.signInWithOtp({
      email: user.email,
      options: {
        shouldCreateUser: false, // Don't create new user
      },
    });

    if (error) {
      // Handle rate limit errors specifically
      if (error.message?.includes('email_send_rate_limit') || error.message?.includes('over_email_send_rate_limit')) {
        return {
          success: false,
          message: "Email rate limit exceeded. Please try again later.",
          isConfigurationError: true,
        };
      }

      return {
        success: false,
        message: error.message || "Failed to send verification code.",
      };
    }

    return {
      success: true,
      message: "Verification code sent to your email address.",
      email: user.email,
    };
  } catch (_error) {
    return {
      success: false,
      message: "An unexpected error occurred while sending verification code.",
    };
  }
}

// Verify OTP for delete account
export async function verifyDeleteAccountOTP(email: string, otp: string): Promise<{
  success: boolean;
  message: string;
}> {
  const supabase = await createClient();

  try {
    const { error } = await supabase.auth.verifyOtp({
      email: email,
      token: otp,
      type: 'email',
    });

    if (error) {
      let errorMessage = 'Failed to verify code.';

      switch (error.code) {
        case 'invalid_otp':
        case 'expired_otp':
          errorMessage = 'Invalid or expired verification code. Please try again.';
          break;
        case 'too_many_requests':
          errorMessage = 'Too many verification attempts. Please wait before trying again.';
          break;
        default:
          errorMessage = 'Unable to verify code. Please try again.';
      }

      return { success: false, message: errorMessage };
    }

    return { success: true, message: "Verification successful." };
  } catch (_error) {
    return {
      success: false,
      message: "An unexpected error occurred during verification.",
    };
  }
}

// Verify password for delete account (for phone users)
export async function verifyDeleteAccountPassword(password: string): Promise<{
  success: boolean;
  message: string;
}> {
  const supabase = await createClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user || !user.phone) {
    return {
      success: false,
      message: "Authentication required or no phone found."
    };
  }

  try {
    // Verify current password by attempting to sign in
    const { error } = await supabase.auth.signInWithPassword({
      phone: user.phone,
      password: password,
    });

    if (error) {
      return {
        success: false,
        message: "Invalid password. Please try again.",
      };
    }

    return { success: true, message: "Password verified successfully." };
  } catch (_error) {
    return {
      success: false,
      message: "An unexpected error occurred during password verification.",
    };
  }
}

// --- Delete Account ---

export type DeleteAccountFormState = {
  message: string | null;
  success: boolean;
};

export async function deleteCustomerAccount(
  // No prevState or formData needed for this action
): Promise<DeleteAccountFormState> {
  const supabase = await createClient();

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { message: 'Not authenticated', success: false };
  }

  // Check for and clean up any storage data using hash-based structure
  try {
    // Use admin client for storage operations to bypass RLS
    const adminSupabase = createAdminClient();
    const bucketName = "customers"; // Correct bucket name (plural)
    const userStoragePath = getScalableUserPath(user.id);

    // Recursive function to delete all files and folders
    const deleteRecursively = async (path: string): Promise<void> => {
      const { data: items, error: listError } = await adminSupabase.storage
        .from(bucketName)
        .list(path);

      if (listError) {
        console.error(`Error listing files in ${path}:`, listError);
        return;
      }

      if (!items || items.length === 0) {
        return;
      }

      // Separate files and folders
      const files: string[] = [];
      const folders: string[] = [];

      for (const item of items) {
        const fullPath = path ? `${path}/${item.name}` : item.name;

        if (item.metadata === null) {
          // This is a folder
          folders.push(fullPath);
        } else {
          // This is a file
          files.push(fullPath);
        }
      }

      // Delete all files in the current directory
      if (files.length > 0) {
        const { error: deleteError } = await adminSupabase.storage
          .from(bucketName)
          .remove(files);

        if (deleteError && deleteError.message !== "The resource was not found") {
          console.error(`Error deleting files in ${path}:`, deleteError);
        } else {
          console.log(`Successfully deleted ${files.length} files in ${path}`);
        }
      }

      // Recursively delete folders
      for (const folder of folders) {
        await deleteRecursively(folder);
      }
    };

    // Start the recursive deletion from the user's root folder
    await deleteRecursively(userStoragePath);

    console.log('Successfully cleaned up customer storage data');
  } catch (storageError) {
    // Log but continue with deletion
    console.error('Error checking/cleaning customer storage:', storageError);
  }

  // Use the admin client to delete the user and profile
  try {
    const supabaseAdmin = createAdminClient();

    // Delete from customer_profiles table (CASCADE will handle related data)
    console.log('Deleting customer profile...');
    const { error: deleteProfileError } = await supabaseAdmin
      .from('customer_profiles')
      .delete()
      .eq('id', user.id);

    if (deleteProfileError) {
      console.error('Error deleting customer profile:', deleteProfileError);
      return { message: `Failed to delete customer profile: ${deleteProfileError.message}`, success: false };
    }

    console.log('Customer profile deleted successfully. CASCADE constraints handled related data cleanup. Storage cleanup completed.');

    // Sign out the user locally first (while the session is still valid)
    await supabase.auth.signOut();

    // Then delete the user using the admin client
    // Using hard delete (shouldSoftDelete=false) to completely remove the user
    const { error: deleteUserError } = await supabaseAdmin.auth.admin.deleteUser(user.id, false);

    if (deleteUserError) {
      console.error('Error deleting user account:', deleteUserError);
      return { message: `Failed to delete account: ${deleteUserError.message}`, success: false };
    }

    // Revalidate paths if needed
    revalidatePath("/", "layout"); // Revalidate root layout

    return { message: 'Account deleted successfully.', success: true };

  } catch (error) {
     console.error('Unexpected error during account deletion:', error);
     return { message: 'An unexpected error occurred during account deletion.', success: false };
  }

}
