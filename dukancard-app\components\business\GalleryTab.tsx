import React from 'react';
import { View, Text, FlatList, TouchableOpacity, Image } from 'react-native';
import { BusinessGalleryImage } from '../../lib/services/businessCardDataService';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';

interface GalleryTabProps {
  gallery: BusinessGalleryImage[];
  isDark: boolean;
  onImagePress: (index: number) => void;
}

export default function GalleryTab({ gallery, isDark, onImagePress }: GalleryTabProps) {
  const styles = createPublicCardViewStyles(isDark);

  return (
    <View style={styles.section}>
      {gallery && gallery.length > 0 ? (
        <FlatList
          data={gallery}
          keyExtractor={(item) => item.id}
          numColumns={2}
          scrollEnabled={false}
          renderItem={({ item, index }) => (
            <TouchableOpacity
              style={styles.galleryItem}
              onPress={() => onImagePress(index)}
              activeOpacity={0.8}
            >
              <Image source={{ uri: item.url }} style={styles.galleryImage} />
            </TouchableOpacity>
          )}
        />
      ) : (
        <Text style={styles.emptyText}>No gallery images available</Text>
      )}
    </View>
  );
}
