import { RazorpayWebhookData, RazorpaySubscription } from "../../types";
import { SupabaseClient } from "@supabase/supabase-js";
import { SupabaseSubscriptionStatus } from "./types";
import { updateSubscription } from "../utils";
import { createAdminClient } from "@/utils/supabase/admin";
import {
  isTerminalStatus,
  extractWebhookTimestamp
} from "../utils";
import { webhookProcessor, type WebhookProcessingContext } from "../webhookProcessor";

/**
 * Handle subscription.pending event
 *
 * This event is triggered when a subscription payment is pending.
 *
 * @param payload The webhook payload
 * @param supabase The Supabase client
 * @returns The result of handling the event
 */
export async function handleSubscriptionPending(
  payload: RazorpayWebhookData,
  _supabase: SupabaseClient,
  razorpayEventId?: string
): Promise<{ success: boolean; message: string }> {
  let context: WebhookProcessingContext | null = null;

  try {
    // Extract subscription data from payload
    const subscriptionData = payload.payload.subscription;

    if (!subscriptionData || !subscriptionData.entity) {
      console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload");
      return { success: false, message: "Subscription data not found in payload" };
    }

    // Cast to proper type to access properties
    const subscription = subscriptionData.entity as unknown as RazorpaySubscription;
    const subscriptionId = subscription.id;
    console.log(`[RAZORPAY_WEBHOOK] Subscription pending: ${subscriptionId}`);

    // Extract webhook timestamp from Razorpay payload for sequence validation
    const webhookTimestamp = extractWebhookTimestamp(payload);

    // Use centralized webhook processor for race condition protection
    context = {
      subscriptionId,
      eventType: 'subscription.pending',
      eventId: razorpayEventId || `pending_${subscriptionId}_${Date.now()}`,
      payload: payload as unknown as Record<string, unknown>,
      webhookTimestamp
    };

    const processingResult = await webhookProcessor.processWebhookEvent(context);
    if (!processingResult.shouldProcess) {
      return { success: processingResult.success, message: processingResult.message };
    }

    // Get admin client to bypass RLS
    const adminClient = createAdminClient();

    // Check if this subscription is in a terminal state (cancelled, expired, completed)
    const { data: existingSubscription, error: checkError } = await adminClient
      .from("payment_subscriptions")
      .select("cancelled_at, plan_id, subscription_status")
      .eq("razorpay_subscription_id", subscriptionId)
      .maybeSingle();

    if (checkError) {
      console.error(`[RAZORPAY_WEBHOOK] Error checking subscription status for ${subscriptionId}:`, checkError);
      // Continue processing anyway
    } else if (existingSubscription) {
      // CENTRALIZED LOGIC: Improved terminal state detection
      // A subscription is terminal if it's been downgraded to free plan OR has terminal status
      const isTerminalState = existingSubscription.plan_id === "free" ||
                             isTerminalStatus(existingSubscription.subscription_status);

      // FIXED: Allow pending updates for authenticated subscriptions even if they have cancelled_at
      // This handles the case where trial users authenticate their subscription after cancellation
      const isAuthenticatedWithCancelledAt = existingSubscription.subscription_status === "authenticated" &&
                                            existingSubscription.cancelled_at;

      if (isTerminalState && !isAuthenticatedWithCancelledAt) {
        console.log(`[RAZORPAY_WEBHOOK] Subscription ${subscriptionId} is in terminal state (plan_id: ${existingSubscription.plan_id}, status: ${existingSubscription.subscription_status}), skipping pending update`);
        return { success: true, message: "Subscription is in terminal state, skipping pending update" };
      }
    }

    // Update subscription status
    const updateResult = await updateSubscription(
      adminClient,
      subscriptionId,
      SupabaseSubscriptionStatus._PENDING,
      {
        // No additional data to update
        // Removed last_payment_attempt_date as requested
      }
    );

    return updateResult;
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Error handling subscription pending:", error);
    return {
      success: false,
      message: `Error handling subscription pending: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}
