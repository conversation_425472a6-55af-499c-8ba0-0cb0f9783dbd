"use server";

import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";

export async function createCustomerProfile(
  userId: string,
  redirectSlug: string | null = null,
  _message: string | null = null
) {
  if (!userId) {
    return { error: "User ID is required." };
  }

  const supabase = await createClient();

  // Fetch user details first
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    // Error fetching user in action
    return { error: "User not found or authentication error." };
  }

  // Check if customer profile already exists (extra safety check)
  const { data: existingProfile, error: checkError } = await supabase
    .from("customer_profiles") // Use new table name
    .select("id")
    .eq("id", userId)
    .maybeSingle();

  if (checkError) {
    // Error checking existing profile
    return { error: "Database error checking profile." };
  }

  if (existingProfile) {
    // Profile already exists for user
    // Redirect to the card page if redirectSlug is provided, otherwise to customer dashboard
    if (redirectSlug) {
      // Don't pass message parameter back to public card page
      redirect(`/${redirectSlug}`);
    } else {
      // Redirect to customer dashboard anyway, assuming it's the correct type
      redirect("/dashboard/customer");
    }
  }

  // Create the customer profile in the correct table
  const { error: insertError } = await supabase
    .from("customer_profiles")
    .insert({
      // Use new table name
      id: userId, // Should match user.id
      // user_type is implicit by the table name now
      // Populate name and email from auth user data
      name: user.user_metadata?.full_name ?? user.user_metadata?.name ?? null, // Use full_name or name from metadata if available
      email: user.email ?? null, // Use user's primary email
    });

  if (insertError) {
    console.error("Error creating customer profile:", insertError);
    // Handle specific errors like unique constraint violation if necessary
    return { error: "Failed to create profile." };
  }

  // Revalidate relevant paths if needed, though middleware handles redirection mostly
  revalidatePath("/choose-role"); // Revalidate the current path
  revalidatePath("/dashboard/customer"); // Revalidate the target path

  // Redirect to the card page if redirectSlug is provided, otherwise to customer dashboard
  if (redirectSlug) {
    // Don't pass message parameter back to public card page
    redirect(`/${redirectSlug}`);
  } else {
    // Redirect to the customer dashboard after successful creation
    redirect("/dashboard/customer");
  }

  // Note: Redirect should happen before returning, but for clarity:
  // return { success: true };
}
