"use client";

import { useState, useEffect } from "react";
import { Search, MapPin, X, Loader2, Building2, Lock } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList
} from "@/components/ui/command";
import { useCategoryContext } from "../context/CategoryContext";
import { getPincodeDetailsClient, getCitySuggestionsClient, getCityDetailsClient } from "@/lib/client/locationUtils";
import CitySearchSkeleton from "@/app/(main)/discover/components/CitySearchSkeleton";

interface ImprovedSearchSectionProps {
  initialValues: {
    pincode?: string | null;
    city?: string | null;
    locality?: string | null;
  };
}

export default function ImprovedSearchSection({ initialValues }: ImprovedSearchSectionProps) {
  const { performSearch, isSearching, locationInfo } = useCategoryContext();
  const [isPincodeLoading, setIsPincodeLoading] = useState(false);
  const [isLoadingCities, setIsLoadingCities] = useState(false);

  // If we're on a city page (locationInfo.city exists) or pincode page (locationInfo.pincode exists), force pincode search type
  // Otherwise, use the initialValues to determine search type
  const [searchType, setSearchType] = useState<"pincode" | "city">(
    locationInfo?.city || locationInfo?.pincode ? "pincode" : (initialValues.city ? "city" : "pincode")
  );

  // Check if we're on a pincode page
  const isPincodePage = !!locationInfo?.pincode;

  // Check if we're on a locality page
  const isLocalityPage = !!locationInfo?.locality;

  // State for validation
  const [pincodeError, setPincodeError] = useState<string | null>(null);
  const [cityError, setCityError] = useState<string | null>(null);

  // Form state
  const [pincode, setPincode] = useState(initialValues.pincode || "");
  const [city, setCity] = useState(initialValues.city || "");
  const [locality, setLocality] = useState(initialValues.locality || "");

  // City suggestions
  const [citySuggestions, setCitySuggestions] = useState<string[]>([]);
  const [showCitySuggestions, setShowCitySuggestions] = useState(false);

  // City details
  const [cityDetails, setCityDetails] = useState<{
    state?: string;
    isValidState?: boolean;
  } | null>(null);

  // Pincode details
  const [pincodeDetails, setPincodeDetails] = useState<{
    city?: string;
    state?: string;
    localities?: string[];
  } | null>(null);
  const [_showLocalitySuggestions, setShowLocalitySuggestions] = useState(false);

  // Handle pincode change
  const handlePincodeChange = async (value: string) => {
    setPincode(value);
    setPincodeError(null); // Clear previous errors

    // Only proceed if we have a 6-digit pincode
    if (value.length === 6 && /^\d+$/.test(value)) {
      setIsPincodeLoading(true);
      try {
        const details = await getPincodeDetailsClient(value);

        if (!details) {
          setPincodeDetails(null);
          setPincodeError("Invalid pincode. Please enter a valid 6-digit pincode.");
          return;
        }

        // If we're on a city page, validate that the pincode belongs to the current city
        if (locationInfo?.city && details.city !== locationInfo.city) {
          setPincodeDetails(null);
          setPincodeError(`This pincode belongs to ${details.city}, not ${locationInfo.city}. Please enter a pincode for ${locationInfo.city}.`);
          return;
        }

        // If we're on a state page, validate that the pincode belongs to the current state
        if (locationInfo?.state && details.state !== locationInfo.state) {
          setPincodeDetails(null);
          setPincodeError(`This pincode belongs to ${details.state}, not ${locationInfo.state}. Please enter a pincode for ${locationInfo.state}.`);
          return;
        }

        // If all validations pass, set the pincode details
        setPincodeDetails(details);

      } catch (error) {
        console.error("Error fetching pincode details:", error);
        setPincodeDetails(null);
        setPincodeError("Error validating pincode. Please try again.");
      } finally {
        setIsPincodeLoading(false);
      }
    } else {
      setPincodeDetails(null);
      if (value.length > 0 && value.length < 6) {
        setPincodeError("Please enter a complete 6-digit pincode.");
      }
    }
  };

  // Handle city change
  const handleCityChange = async (value: string) => {
    setCity(value);
    console.log("City input changed to:", value);

    if (value.length >= 2) {
      setIsLoadingCities(true);
      try {
        // Use client-side function for better performance
        console.log("Fetching city suggestions for:", value);
        const result = await getCitySuggestionsClient(value);
        console.log("City suggestions result:", result);

        if (result.cities) {
          console.log("Setting city suggestions:", result.cities);
          setCitySuggestions(result.cities);
          setShowCitySuggestions(true);
        } else {
          console.log("No city suggestions found");
          setCitySuggestions([]);
          setShowCitySuggestions(false);
        }
      } catch (error) {
        console.error("Error fetching city suggestions:", error);
        setCitySuggestions([]);
        setShowCitySuggestions(false);
      } finally {
        setIsLoadingCities(false);
      }
    } else {
      setCitySuggestions([]);
      setShowCitySuggestions(false);
    }
  };

  // Handle city selection
  const handleCitySelect = async (selectedCity: string) => {
    setCity(selectedCity);
    setShowCitySuggestions(false);
    setCityError(null);

    // If we're on a state page, validate that the city belongs to the current state
    if (locationInfo?.state) {
      try {
        const details = await getCityDetailsClient(selectedCity, locationInfo.state);

        if (details.error) {
          setCityDetails(null);
          setCityError(`Error validating city. Please try again.`);
          return;
        }

        if (details.isValidState === false) {
          setCityDetails({
            state: details.state,
            isValidState: false
          });
          setCityError(`This city belongs to ${details.state}, not ${locationInfo.state}. Please select a city in ${locationInfo.state}.`);
          return;
        }

        // If validation passes, set the city details
        setCityDetails({
          state: details.state,
          isValidState: true
        });
      } catch (error) {
        console.error("Error validating city:", error);
        setCityDetails(null);
        setCityError("Error validating city. Please try again.");
      }
    }
  };

  // Handle search submission
  const handleSearch = () => {
    // For pincode search, only proceed if we have a valid pincode (no errors and details exist)
    if (searchType === "pincode" && pincode) {
      if (pincodeError) {
        // Don't perform search if there's a pincode error
        return;
      }

      if (!pincodeDetails) {
        // Set error if no pincode details (invalid pincode)
        setPincodeError("Please enter a valid pincode for this location.");
        return;
      }

      performSearch({
        pincode,
        locality: locality || null,
      });
    } else if (searchType === "city" && city) {
      // City search - only allowed if we're not on a city page
      if (!locationInfo?.city) {
        // Don't perform search if there's a city error
        if (cityError) {
          return;
        }

        // If we're on a state page, validate that the city belongs to the current state
        if (locationInfo?.state && cityDetails?.isValidState === false) {
          setCityError(`This city belongs to ${cityDetails.state}, not ${locationInfo.state}. Please select a city in ${locationInfo.state}.`);
          return;
        }

        performSearch({
          city,
          locality: locality || null,
        });
      }
    }
  };

  // Handle clear search
  const handleClearSearch = () => {
    // If we're on a pincode page, don't clear the pincode
    if (!isPincodePage) {
      setPincode("");
      setPincodeDetails(null);
      setPincodeError(null);
    }

    // If we're on a locality page, don't clear the locality
    if (!isLocalityPage) {
      setLocality("");
    }

    setCity("");
    setCityDetails(null);
    setCityError(null);
    setCitySuggestions([]);
    setShowCitySuggestions(false);
    setShowLocalitySuggestions(false);

    // If we're on a pincode page or locality page, reload the pincode details
    if ((isPincodePage || isLocalityPage) && locationInfo?.pincode) {
      const loadPincodeDetails = async () => {
        setIsPincodeLoading(true);
        try {
          // Ensure pincode is a string
          const pincodeStr = String(locationInfo.pincode);
          const details = await getPincodeDetailsClient(pincodeStr);
          if (details && !details.error) {
            setPincodeDetails(details);

            // If we're on a locality page, ensure the locality is set
            if (isLocalityPage && locationInfo.locality) {
              setLocality(locationInfo.locality);
            }
          }
        } catch (error) {
          console.error("Error reloading pincode details:", error);
        } finally {
          setIsPincodeLoading(false);
        }
      };

      loadPincodeDetails();
    }
  };

  // Toggle search type
  const toggleSearchType = () => {
    // If we're on a pincode page, don't allow toggling
    if (isPincodePage) {
      return;
    }

    setSearchType(searchType === "pincode" ? "city" : "pincode");
    handleClearSearch();
  };

  // Handle Enter key press
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // Load pincode details when component mounts if we're on a pincode or locality page
  useEffect(() => {
    if ((isPincodePage || isLocalityPage) && locationInfo?.pincode) {
      // Set the pincode from locationInfo
      setPincode(locationInfo.pincode);

      // If we're on a locality page, set the locality from locationInfo
      if (isLocalityPage && locationInfo.locality) {
        setLocality(locationInfo.locality);
      }

      // Load pincode details
      const loadPincodeDetails = async () => {
        setIsPincodeLoading(true);
        try {
          // Ensure pincode is a string
          const pincodeStr = String(locationInfo.pincode);
          const details = await getPincodeDetailsClient(pincodeStr);

          if (details && !details.error) {
            setPincodeDetails(details);

            // If we're not on a locality page, handle locality selection
            if (!isLocalityPage) {
              // If we have an initial locality value and it's in the list, select it
              if (initialValues.locality && details.localities?.includes(initialValues.locality)) {
                setLocality(initialValues.locality);
              } else if (details.localities && details.localities.length === 1) {
                // If there's only one locality, auto-select it
                setLocality(details.localities[0]);
              }
            }
          }
        } catch (error) {
          console.error("Error loading pincode details:", error);
        } finally {
          setIsPincodeLoading(false);
        }
      };

      loadPincodeDetails();
    }
  }, [isPincodePage, isLocalityPage, locationInfo?.pincode, locationInfo?.locality, initialValues.locality]);

  return (
    <div className="w-full py-6 mt-20">
      <div className="container mx-auto px-4">
        {/* Heading */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="text-center mb-6"
        >
          <h1 className="text-2xl md:text-3xl font-bold text-neutral-800 dark:text-neutral-100 mb-2">
            Find {useCategoryContext().category.name} Near You
          </h1>
          <p className="text-sm md:text-base text-neutral-600 dark:text-neutral-400">
            Search through our extensive database of {useCategoryContext().category.name.toLowerCase()} businesses and products/services
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="max-w-5xl mx-auto"
        >
          <div className="flex flex-col md:flex-row gap-3 items-center w-full">
            {/* Search type selector - disabled when on a city page */}
            <div className="w-full md:w-auto">
              {locationInfo?.city ? (
                // When on a city page, show a disabled selector that only shows pincode
                <div className="w-full md:w-[140px] h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md px-4 flex items-center text-base leading-normal">
                  <MapPin className="mr-2 h-4 w-4 text-[var(--brand-gold)]" />
                  <span>Pincode</span>
                </div>
              ) : (
                // Normal selector when not on a city page
                <Select
                  value={searchType}
                  onValueChange={(value) => {
                    setSearchType(value as "pincode" | "city");
                    toggleSearchType();
                  }}
                >
                  <SelectTrigger className="w-full md:w-[140px] h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md px-4 flex items-center text-base leading-normal">
                    <SelectValue placeholder="Search by" className="text-base leading-normal" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pincode">
                      <div className="flex items-center">
                        <MapPin className="mr-2 h-4 w-4 text-[var(--brand-gold)]" />
                        <span>Pincode</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="city">
                      <div className="flex items-center">
                        <Building2 className="mr-2 h-4 w-4 text-[var(--brand-gold)]" />
                        <span>City</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              )}
            </div>

            {/* Dynamic search inputs based on search type */}
            <div className="flex-1 w-full flex flex-col md:flex-row gap-3 items-center">
              {searchType === "pincode" ? (
                <>
                  {/* Pincode input */}
                  <div className="relative w-full md:flex-1">
                    <div className="relative">
                      <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-neutral-400" />
                      {isPincodePage ? (
                        <>
                          {/* Read-only pincode input when on a pincode page */}
                          <div className="relative">
                            <Input
                              type="tel"
                              value={pincode}
                              readOnly
                              className="pl-12 pr-12 h-12 min-h-[48px] bg-neutral-50 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-md text-base cursor-not-allowed"
                            />
                            <Lock className="absolute right-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400" />
                          </div>
                        </>
                      ) : (
                        <>
                          {/* Normal editable pincode input */}
                          <Input
                            type="tel"
                            placeholder="Enter 6-digit pincode"
                            value={pincode}
                            onChange={(e) => {
                              // Only allow numeric input
                              const value = e.target.value.replace(/\D/g, '');
                              handlePincodeChange(value);
                            }}
                            onKeyDown={(e) => {
                              // Prevent non-numeric input and allow control keys
                              if (
                                !/^\d$/.test(e.key) &&
                                !['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight', 'Enter'].includes(e.key)
                              ) {
                                e.preventDefault();
                              } else {
                                handleKeyDown(e);
                              }
                            }}
                            className="pl-12 h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md text-base"
                            maxLength={6}
                            inputMode="numeric"
                          />
                          {isPincodeLoading && (
                            <Loader2 className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 animate-spin text-neutral-400" />
                          )}
                          {pincode && !isPincodeLoading && (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8"
                              onClick={() => setPincode("")}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          )}
                        </>
                      )}
                    </div>
                  </div>

                  {/* Locality select */}
                  <div className="w-full md:w-[200px]">
                    {isLocalityPage ? (
                      // Read-only locality display when on a locality page
                      <div className="relative">
                        <div className="w-full h-12 min-h-[48px] bg-neutral-50 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-md px-4 flex items-center text-base leading-normal cursor-not-allowed">
                          <span className="text-neutral-700 dark:text-neutral-300 truncate">{locality || locationInfo?.locality}</span>
                          <Lock className="ml-auto h-4 w-4 text-neutral-400" />
                        </div>
                      </div>
                    ) : (
                      // Normal editable locality select
                      <Select
                        value={locality || "_any"}
                        onValueChange={(value) => setLocality(value === "_any" ? "" : value)}
                        disabled={!pincodeDetails?.localities || pincodeDetails.localities.length === 0}
                      >
                        <SelectTrigger className="w-full h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md px-4 flex items-center text-base leading-normal">
                          <SelectValue placeholder="Select locality" className="text-base leading-normal" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="_any">Any Locality</SelectItem>
                          {pincodeDetails?.localities?.map((loc) => (
                            <SelectItem key={loc} value={loc}>
                              {loc}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  </div>
                </>
              ) : (
                <>
                  {/* City input with suggestions */}
                  <div className="relative w-full md:flex-1">
                    <div className="relative">
                      <Building2 className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-neutral-400" />
                      <Input
                        placeholder="Enter city name"
                        className="pl-12 h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md text-base"
                        autoComplete="off"
                        value={city || ""}
                        onChange={(e) => {
                          handleCityChange(e.target.value);
                          console.log("City input changed directly:", e.target.value);
                        }}
                        onKeyDown={handleKeyDown}
                      />
                      {isLoadingCities && (
                        <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-neutral-400" />
                      )}
                      {city && !isLoadingCities && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8"
                          onClick={() => setCity("")}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    {/* City suggestions */}
                    <AnimatePresence>
                      {isLoadingCities && city ? (
                        <CitySearchSkeleton />
                      ) : (
                        showCitySuggestions && citySuggestions.length > 0 && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="absolute z-10 mt-1 w-full bg-white dark:bg-neutral-900 rounded-md border border-neutral-200 dark:border-neutral-800 shadow-lg"
                          >
                            <Command>
                              <CommandList>
                                <CommandGroup>
                                  {citySuggestions.map((cityItem) => (
                                    <CommandItem
                                      key={cityItem}
                                      onSelect={() => handleCitySelect(cityItem)}
                                      className="cursor-pointer"
                                    >
                                      <Building2 className="mr-2 h-4 w-4 text-neutral-400" />
                                      <span>{cityItem}</span>
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </motion.div>
                        )
                      )}
                    </AnimatePresence>
                  </div>
                </>
              )}

              {/* Search button */}
              {isLocalityPage ? (
                // Disabled search button with lock icon when on a locality page
                <Button
                  type="button"
                  disabled={true}
                  className="h-12 min-h-[48px] bg-neutral-200 dark:bg-neutral-700 text-neutral-500 dark:text-neutral-400 w-full md:w-auto px-6 border border-neutral-300 dark:border-neutral-600 rounded-md flex items-center justify-center font-medium text-base cursor-not-allowed"
                >
                  <Lock className="h-4 w-4 mr-2" />
                  Locked
                </Button>
              ) : (
                // Normal search button
                <Button
                  type="button"
                  onClick={handleSearch}
                  disabled={
                    isSearching ||
                    (searchType === "pincode" ? !pincode || !!pincodeError : !city || !!cityError || (!!locationInfo?.state && cityDetails?.isValidState === false))
                  }
                  className="h-12 min-h-[48px] bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-[var(--brand-gold-foreground)] w-full md:w-auto px-6 border border-[var(--brand-gold)] rounded-md flex items-center justify-center font-medium text-base"
                >
                  {isSearching ? (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="mr-2"
                    >
                      <Loader2 className="h-4 w-4" />
                    </motion.div>
                  ) : (
                    <Search className="h-4 w-4 mr-2" />
                  )}
                  Search
                </Button>
              )}
            </div>
          </div>

          {/* Pincode Details Display */}
          {pincodeDetails && searchType === "pincode" && !pincodeError && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-3 text-sm text-neutral-500 dark:text-neutral-400"
            >
              <span className="font-medium">
                {pincodeDetails.city}, {pincodeDetails.state}
              </span>
              {pincodeDetails.localities && pincodeDetails.localities.length > 0 && (
                <span className="ml-2">
                  ({pincodeDetails.localities.length} localities available)
                </span>
              )}
            </motion.div>
          )}

          {/* Pincode Error Display */}
          {pincodeError && searchType === "pincode" && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-3 text-sm text-red-500 dark:text-red-400"
            >
              <span className="font-medium">
                {pincodeError}
              </span>
            </motion.div>
          )}

          {/* City Error Display */}
          {cityError && searchType === "city" && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-3 text-sm text-red-500 dark:text-red-400"
            >
              <span className="font-medium">
                {cityError}
              </span>
            </motion.div>
          )}

          {/* City Details Display */}
          {cityDetails && searchType === "city" && !cityError && cityDetails.isValidState && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-3 text-sm text-neutral-500 dark:text-neutral-400"
            >
              <span className="font-medium">
                City in {cityDetails.state}
              </span>
            </motion.div>
          )}
        </motion.div>
      </div>
    </div>
  );
}
