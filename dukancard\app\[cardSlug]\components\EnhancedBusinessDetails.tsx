"use client";

import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import { cardVariants } from "./animations";
import ProfessionalBusinessTable from "./BusinessDetails/ProfessionalBusinessTable";
import EnhancedMetricsCards from "./BusinessDetails/EnhancedMetricsCards";

// Define the BusinessProfile type
type BusinessProfile = BusinessCardData & {
  total_reviews?: number;
  subscription_status?: string;
  has_active_subscription?: boolean;
  trial_end_date?: Date | string | null;
  total_visits?: number;
  today_visits?: number;
  yesterday_visits?: number;
  visits_7_days?: number;
  visits_30_days?: number;
  created_at?: Date | string;
  updated_at?: Date | string;
};

interface EnhancedBusinessDetailsProps {
  businessProfile: BusinessProfile;
  isAuthenticated: boolean;
  totalLikes: number;
  totalSubscriptions: number;
  averageRating: number;
  // Interactive button props
  isSubscribed: boolean;
  hasLiked: boolean;
  isLoadingInteraction: boolean;
  onSubscribe: () => void;
  onUnsubscribe: () => void;
  onLike: () => void;
  onUnlike: () => void;
  onReviewClick: () => void;
  isOwnBusiness: boolean;
}

export default function EnhancedBusinessDetails({
  businessProfile,
  isAuthenticated,
  totalLikes,
  totalSubscriptions,
  averageRating,
  isSubscribed,
  hasLiked,
  isLoadingInteraction,
  onSubscribe,
  onUnsubscribe,
  onLike,
  onUnlike,
  onReviewClick,
  isOwnBusiness,
}: EnhancedBusinessDetailsProps) {
  const detailsRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(detailsRef, { once: false, amount: 0.2 });

  return (
    <motion.div
      ref={detailsRef}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={cardVariants}
      className="bg-white dark:bg-black rounded-2xl overflow-hidden shadow-lg"
    >
      {/* Enhanced Performance Metrics with Inner Glow */}
      <EnhancedMetricsCards
        totalLikes={totalLikes}
        totalSubscriptions={totalSubscriptions}
        averageRating={averageRating}
        createdAt={businessProfile.created_at}
        isSubscribed={isSubscribed}
        hasLiked={hasLiked}
        isLoadingInteraction={isLoadingInteraction}
        onSubscribe={onSubscribe}
        onUnsubscribe={onUnsubscribe}
        onLike={onLike}
        onUnlike={onUnlike}
        onReviewClick={onReviewClick}
        isOwnBusiness={isOwnBusiness}
      />

      {/* Comprehensive Professional Business Table */}
      <ProfessionalBusinessTable
        businessProfile={businessProfile}
        isAuthenticated={isAuthenticated}
      />
    </motion.div>
  );
}
