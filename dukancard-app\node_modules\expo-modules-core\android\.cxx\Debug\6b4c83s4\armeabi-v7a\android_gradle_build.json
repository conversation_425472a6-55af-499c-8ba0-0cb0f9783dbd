{"buildFiles": ["C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\fbjni\\fbjniConfig.cmake", "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\fbjni\\fbjniConfigVersion.cmake", "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\src\\fabric\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"expo-modules-core::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "expo-modules-core", "output": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\build\\intermediates\\cxx\\Debug\\6b4c83s4\\obj\\armeabi-v7a\\libexpo-modules-core.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8050d15875717ad3c035882deb89d68f\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6e9cabc4b841ecb8bc48e2ce54799a1d\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6e9cabc4b841ecb8bc48e2ce54799a1d\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8050d15875717ad3c035882deb89d68f\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6e9cabc4b841ecb8bc48e2ce54799a1d\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6e9cabc4b841ecb8bc48e2ce54799a1d\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "fabric::@3c04bbf757b97f4dae7c": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "fabric", "output": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\armeabi-v7a\\src\\fabric\\libfabric.a"}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}