import { getCategoryBySlug } from "@/lib/config/categories";
import { NextRequest, NextResponse } from "next/server";
import { MetadataRoute } from "next";
import { INDIAN_STATE_DATA } from "@/lib/config/states";
import { createSitemapClient } from "@/utils/supabase/sitemap";
import { unstable_cache } from "next/cache";
import { CACHE_DURATION_SECONDS, SITEMAP_CONFIG } from "@/app/categories/config";

/**
 * Route handler for city-specific category sitemaps
 * This generates a sitemap for a specific category in a specific state and city
 * including all pincodes in that city
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ categorySlug: string; stateSlug: string; citySlug: string }> }
): Promise<NextResponse> {
  // Temporarily disabled to reduce crawler load on database
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${siteUrl}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>`;

  return new NextResponse(xml, {
    headers: {
      "Content-Type": "application/xml",
      "Cache-Control": "public, max-age=86400", // Cache for 24 hours
    },
  });

  const paramsData = await params;
  const { categorySlug, stateSlug, citySlug } = paramsData;

  try {
    // Get the category by slug
    const category = getCategoryBySlug(categorySlug);

    // If category doesn't exist, return 404
    if (!category) {
      console.warn(`Category not found for slug: ${categorySlug}`);
      return new NextResponse("Category not found", { status: 404 });
    }

    // Find the matching state using the state_slug directly
    const stateData = INDIAN_STATE_DATA.find(state => state.slug === stateSlug);
    const matchingState = stateData?.name;

    // If state doesn't exist, return 404
    if (!matchingState) {
      console.warn(`State not found for slug: ${stateSlug}`);
      return new NextResponse("State not found", { status: 404 });
    }



    // Define a function to find a matching city with caching
    const findMatchingCity = unstable_cache(
      async (stateName: string, citySlugParam: string) => {
        console.log(`Looking up city with slug "${citySlugParam}" in state "${stateName}" (this will be cached)`);

        const supabaseClient = createSitemapClient();

        // Get city name directly using the city_slug
        const { data, error } = await supabaseClient
          .from("pincodes")
          .select("DivisionName")
          .eq("StateName", stateName)
          .eq("city_slug", citySlugParam)
          .limit(1);

        if (error) {
          console.error("Error fetching city by slug:", error);
          return "";
        }

        if (!data || data.length === 0) {
          console.warn(`No city found with slug "${citySlugParam}" in state "${stateName}"`);
          return "";
        }

        return data[0].DivisionName;
      },
      ["city-by-slug-sitemap"], // Cache key prefix
      {
        revalidate: CACHE_DURATION_SECONDS, // Revalidate based on central config
        tags: [`state-city-${matchingState}-${citySlug}`], // Cache tag for this specific lookup
      }
    );

    // Use the cached function to find the matching city
    if (!citySlug) {
      console.warn(`City slug is undefined for state: ${matchingState}`);
      return new NextResponse("City slug not found", { status: 404 });
    }

    // Ensure matchingState is defined before proceeding
    if (!matchingState) {
      console.warn(`State is null for stateSlug: ${stateSlug}`);
      return new NextResponse("State not found", { status: 404 });
    }

    const matchingCity = await findMatchingCity(matchingState as string, citySlug);

    if (!matchingCity) {
      console.warn(`City not found for slug: ${citySlug} in state: ${matchingState}`);
      return new NextResponse("City not found", { status: 404 });
    }

    // Initialize sitemap with the main city-category page
    const currentDate = new Date();
    const sitemap: MetadataRoute.Sitemap = [
      {
        url: `${siteUrl}/categories/${categorySlug}/${stateSlug}/${citySlug}`,
        lastModified: currentDate,
        changeFrequency: SITEMAP_CONFIG.defaultChangeFrequency,
        priority: SITEMAP_CONFIG.priority.cityPage,
      }
    ];

    // Define a function to fetch pincodes for the city with caching
    const getPincodesForCity = unstable_cache(
      async (stateName: string, cityName: string, _categoryName: string) => {
        const supabaseClient = createSitemapClient();

        try {
          console.log(`Fetching distinct pincodes for city ${cityName} in state ${stateName}...`);

          // Use proper pagination to handle Supabase's 1000 row limit
          const allPincodes: string[] = [];
          let page = 0;
          let hasMore = true;
          const PAGE_SIZE = 1000; // Supabase's maximum limit

          while (hasMore) {
            const from = page * PAGE_SIZE;
            const to = from + PAGE_SIZE - 1;

            console.log(`Fetching pincodes page ${page + 1} (range: ${from}-${to})...`);

            const { data, error } = await supabaseClient
              .from("pincodes")
              .select("Pincode")
              .eq("StateName", stateName)
              .eq("DivisionName", cityName)
              .range(from, to);

            if (error) {
              console.error(`Error fetching pincodes page ${page + 1}:`, error);
              break;
            }

            if (!data || data.length === 0) {
              hasMore = false;
            } else {
              // Extract pincode values
              const pincodes = data.map((row: { Pincode: string }) => row.Pincode);
              allPincodes.push(...pincodes);
              page++;

              // If we received fewer records than the page size, we've reached the end
              if (data.length < PAGE_SIZE) {
                hasMore = false;
              }
            }
          }

          if (allPincodes.length === 0) {
            console.warn(`No pincodes found for city ${cityName} in state ${stateName}`);
            return [];
          }

          // Deduplicate pincode values
          const uniquePincodes = [...new Set(allPincodes)];

          console.log(`Found ${uniquePincodes.length} unique pincodes in ${cityName}, ${stateName}`);
          return uniquePincodes;
        } catch (error) {
          console.error(`Unexpected error fetching pincodes for city ${cityName} in state ${stateName}:`, error);
          return [];
        }
      },
      ["pincodes-by-city-category"], // Cache key prefix
      {
        revalidate: CACHE_DURATION_SECONDS, // Revalidate based on central config
        tags: [`city-pincodes-${matchingState}-${matchingCity}-category-${category?.name || 'unknown'}`], // Cache tag for this specific city and category
      }
    );

    // Fetch pincodes using the cached function
    if (!matchingCity) {
      console.warn(`Matching city is null for citySlug: ${citySlug} in state: ${matchingState}`);
      return new NextResponse("City not found", { status: 404 });
    }
    if (!category) {
      console.warn(`Category is null for categorySlug: ${categorySlug}`);
      return new NextResponse("Category not found", { status: 404 });
    }

    // Ensure matchingState is defined before proceeding
    if (!matchingState) {
      console.warn(`State is null for stateSlug: ${stateSlug}`);
      return new NextResponse("State not found", { status: 404 });
    }

    const pincodes = await getPincodesForCity(matchingState as string, matchingCity, category!.name);

    // Add pincode-specific URLs to the sitemap
    for (const pincode of pincodes) {
      // Add direct link to pincode page
      sitemap.push({
        url: `${siteUrl}/categories/${categorySlug}/${stateSlug}/${citySlug}/${pincode}`,
        lastModified: currentDate,
        changeFrequency: SITEMAP_CONFIG.defaultChangeFrequency,
        priority: SITEMAP_CONFIG.priority.pincodePage,
      });

      // Add link to pincode sitemap XML
      sitemap.push({
        url: `${siteUrl}/categories/${categorySlug}/${stateSlug}/${citySlug}/${pincode}/sitemap.xml`,
        lastModified: currentDate,
        changeFrequency: SITEMAP_CONFIG.defaultChangeFrequency,
        priority: SITEMAP_CONFIG.priority.sitemapFile
      });
    }



    // Convert the sitemap to XML
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${sitemap.map((entry) => {
    // Format the date properly, handling both string and Date types
    const lastMod = entry.lastModified
      ? (entry.lastModified instanceof Date
          ? entry.lastModified.toISOString()
          : entry.lastModified)
      : currentDate.toISOString();

    return `
  <url>
    <loc>${entry.url}</loc>
    <lastmod>${lastMod}</lastmod>
    <changefreq>${entry.changeFrequency || SITEMAP_CONFIG.defaultChangeFrequency}</changefreq>
    <priority>${entry.priority || SITEMAP_CONFIG.priority.cityPage}</priority>
  </url>
  `;
  }).join('')}
</urlset>`;



    // Return the XML with the correct content type
    return new NextResponse(xml, {
      headers: {
        'Content-Type': 'application/xml',
      },
    });
  } catch (error: unknown) {
    // Provide more detailed error information
    const errorMessage = error instanceof Error ? (error as Error).message : 'Unknown error occurred';
    console.error(`Error generating sitemap for category ${categorySlug}, state ${stateSlug}, and city ${citySlug}:`, errorMessage);

    // Return a more informative error response
    return new NextResponse(
      `Error generating sitemap: ${errorMessage}. Please try again later or contact support if the issue persists.`,
      {
        status: 500,
        headers: {
          'Content-Type': 'text/plain',
        },
      }
    );
  }
}

// Use ISR with a long revalidation period (60 days = 5184000 seconds)
// This allows the page to be cached but still refreshed periodically
export const revalidate = 5184000; // 60 days in seconds

// Force dynamic rendering to prevent build-time generation
export const dynamic = 'force-dynamic';

// Note: We're using ISR with a long cache duration
// This allows the sitemap to be generated on first request and then cached
// It will be regenerated after the cache period expires (60 days)
