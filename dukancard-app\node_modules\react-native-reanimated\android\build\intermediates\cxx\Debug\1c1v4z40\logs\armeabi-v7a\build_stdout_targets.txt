ninja: Entering directory `C:\Users\<USER>\Desktop\Dukancard Web App\dukancard-app\node_modules\react-native-reanimated\android\.cxx\Debug\1c1v4z40\armeabi-v7a'
[0/2] Re-checking globbed directories...
[1/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/52fda005d3569453cc0bee5bba8e2534/WorkletRuntimeRegistry.cpp.o
[2/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/94a7345ff97fb15f60f90c9e70e598d2/worklets/Tools/JSISerializer.cpp.o
[3/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/be1f011f7ed212bd8275c1433d87a541/cpp/worklets/Tools/JSLogger.cpp.o
[4/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/be1f011f7ed212bd8275c1433d87a541/cpp/worklets/Tools/AsyncQueue.cpp.o
[5/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/be1f011f7ed212bd8275c1433d87a541/cpp/worklets/Tools/JSScheduler.cpp.o
[6/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/94a7345ff97fb15f60f90c9e70e598d2/worklets/SharedItems/Shareables.cpp.o
[7/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/47110ef98cbb7f1f4926e3de89e883e3/Tools/ReanimatedJSIUtils.cpp.o
[8/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/be1f011f7ed212bd8275c1433d87a541/cpp/worklets/Tools/UIScheduler.cpp.o
[9/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/47110ef98cbb7f1f4926e3de89e883e3/Tools/WorkletEventHandler.cpp.o
[10/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/3f07c992310157398ee229dfbb7815a0/RNRuntimeWorkletDecorator.cpp.o
[11/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/3f07c992310157398ee229dfbb7815a0/ReanimatedRuntime.cpp.o
[12/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o
[13/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/47110ef98cbb7f1f4926e3de89e883e3/Tools/ReanimatedVersion.cpp.o
[14/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/3f07c992310157398ee229dfbb7815a0/ReanimatedHermesRuntime.cpp.o
[15/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/47110ef98cbb7f1f4926e3de89e883e3/WorkletRuntime/WorkletRuntime.cpp.o
[16/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o
[17/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/3f07c992310157398ee229dfbb7815a0/WorkletRuntimeDecorator.cpp.o
[18/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/07612551e6c98770effcb79efc0dfee5/AnimatedSensorModule.cpp.o
[19/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o
[20/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o
[21/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/f498314aefe3879b53aeb3797913f823/Fabric/PropsRegistry.cpp.o
[22/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/f498314aefe3879b53aeb3797913f823/Fabric/ShadowTreeCloner.cpp.o
[23/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/e93bf23374171fa7afa054339ccef595/LayoutAnimationsManager.cpp.o
[24/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/f498314aefe3879b53aeb3797913f823/Fabric/ReanimatedCommitHook.cpp.o
[25/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/e93bf23374171fa7afa054339ccef595/LayoutAnimationsProxy.cpp.o
[26/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/e93bf23374171fa7afa054339ccef595/LayoutAnimationsUtils.cpp.o
[27/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/f498314aefe3879b53aeb3797913f823/Fabric/ReanimatedMountHook.cpp.o
[28/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/0ff142ab6057f8cc3aecd53002bdece6/WorkletsModuleProxySpec.cpp.o
[29/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/47110ef98cbb7f1f4926e3de89e883e3/Registries/EventHandlerRegistry.cpp.o
[30/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/f498314aefe3879b53aeb3797913f823/Tools/FeaturesConfig.cpp.o
[31/41] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/0ff142ab6057f8cc3aecd53002bdece6/WorkletsModuleProxy.cpp.o
[32/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/c903566bfa1ded5a75972bfd6a17bf78/ReanimatedModuleProxySpec.cpp.o
[33/41] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\1c1v4z40\obj\armeabi-v7a\libworklets.so
[34/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/037da824c0ad0319f02147e2f7a518f4/UIRuntimeDecorator.cpp.o
[35/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/037da824c0ad0319f02147e2f7a518f4/RNRuntimeDecorator.cpp.o
[36/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o
[37/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o
[38/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o
[39/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/c903566bfa1ded5a75972bfd6a17bf78/ReanimatedModuleProxy.cpp.o
[40/41] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o
[41/41] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\1c1v4z40\obj\armeabi-v7a\libreanimated.so
