"use client";

import { useState, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import { X, Maximize, ExternalLink } from "lucide-react";
import { GalleryImage } from "@/lib/actions/gallery";
import { Button } from "@/components/ui/button";
import { useInView } from "react-intersection-observer";

interface GalleryTabProps {
  images: GalleryImage[];
  totalCount: number;
  businessName: string;
  businessSlug: string;
}

export default function GalleryTab({
  images,
  totalCount,
  businessName,
  businessSlug,
}: GalleryTabProps) {
  const [lightboxImage, setLightboxImage] = useState<string | null>(null);
  const carouselRef = useRef<HTMLDivElement>(null);
  const { ref: inViewRef } = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });





  const openLightbox = (imageUrl: string) => {
    setLightboxImage(imageUrl);
  };

  const closeLightbox = () => {
    setLightboxImage(null);
  };





  if (images.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center py-12"
      >
        <div className="text-neutral-500 dark:text-neutral-400">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-neutral-100 dark:bg-neutral-800 flex items-center justify-center">
            <ExternalLink className="w-8 h-8" />
          </div>
          <h3 className="text-lg font-medium mb-2">No Gallery Images</h3>
          <p className="text-sm">This business hasn&apos;t added any gallery images yet.</p>
        </div>
      </motion.div>
    );
  }

  // Images are already limited to 4 from the API
  const displayImages = images;

  return (
    <motion.div
      className="w-full"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      ref={inViewRef}
    >
      {/* Header */}
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-neutral-800 dark:text-neutral-100">
          Gallery
        </h2>
        <p className="text-sm text-neutral-600 dark:text-neutral-400 mt-1">
          {totalCount} {totalCount === 1 ? 'photo' : 'photos'}
        </p>
      </div>

      {/* Gallery Grid */}
      <motion.div
        className="grid grid-cols-2 sm:grid-cols-4 gap-3 mb-6"
        ref={carouselRef}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {displayImages.map((image, index) => (
          <motion.div
            key={image.id}
            className="aspect-square relative overflow-hidden rounded-xl cursor-pointer group"
            onClick={() => openLightbox(image.url)}
            whileHover={{ scale: 1.03 }}
            transition={{ duration: 0.3 }}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            style={{ transitionDelay: `${index * 0.1}s` }}
          >
            <Image
              src={image.url}
              alt={`${businessName} gallery image`}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-110"
              sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
            />

            {/* Overlay - similar to gallery page */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="absolute bottom-2 right-2 bg-black/50 text-white p-1.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Maximize className="w-4 h-4" />
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Go to Gallery Button */}
      {displayImages.length > 0 && (
        <div className="flex justify-center">
          <Link href={`/${businessSlug}/gallery`} passHref>
            <Button
              variant="outline"
              size="sm"
              className="gap-2 hover:bg-primary hover:text-primary-foreground transition-colors"
            >
              <span>Go to Gallery</span>
              <ExternalLink className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      )}

      {/* Lightbox - similar to gallery page */}
      <AnimatePresence>
        {lightboxImage && (
          <motion.div
            className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={closeLightbox}
          >
            {/* Close button */}
            <button
              className="absolute top-4 right-4 z-10 p-2 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                closeLightbox();
              }}
            >
              <X className="w-6 h-6" />
            </button>



            {/* Image */}
            <div
              className="relative w-full h-full max-w-4xl max-h-[80vh] mx-auto p-4 flex items-center justify-center"
              onClick={(e) => e.stopPropagation()}
            >
              <Image
                src={lightboxImage}
                alt={`${businessName} gallery image`}
                fill
                className="object-contain"
                sizes="100vw"
                priority
              />
            </div>


          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
