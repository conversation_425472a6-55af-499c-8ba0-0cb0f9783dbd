"use server";

import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { createAdminClient } from "@/utils/supabase/admin";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import { getSortingColumn, getSortingDirection } from "@/app/(main)/discover/utils/sortMappings";

// Fetch businesses by locality search
export async function fetchBusinessesByLocalitySearch(params: {
  localityName: string;
  pincode: string;
  businessName?: string;
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy | "price_asc" | "price_desc";
}): Promise<{
  data?: {
    businesses: BusinessCardData[];
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    pincode,
    businessName,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
  } = params;

  try {
    const supabaseAdmin = createAdminClient();
    const offset = (page - 1) * limit;

    // Define fields to select
    const businessFields = `
      id,
      business_name,
      logo_url,
      member_name,
      title,
      address_line,
      city,
      state,
      pincode,
      locality,
      phone,
      instagram_url,
      facebook_url,
      whatsapp_number,
      about_bio,
      status,
      business_slug,
      theme_color,
      delivery_info,
      business_hours,
      business_category,
      total_likes,
      total_subscriptions,
      average_rating,
      created_at,
      updated_at,
      trial_end_date,
      contact_email
    `;

    // Start building the query
    let query = supabaseAdmin
      .from("business_profiles")
      .select(businessFields, { count: "exact" })
      .eq("status", "online")
      .eq("pincode", pincode);

    // Add business name filter if provided
    if (businessName) {
      query = query.ilike("business_name", `%${businessName}%`);
    }

    // Add sorting
    query = query.order(getSortingColumn(sortBy), { ascending: getSortingDirection(sortBy) });

    // Add pagination
    query = query.range(offset, offset + limit - 1);

    // Execute the query
    const { data, count, error } = await query;

    if (error) {
      console.error("Error in fetchBusinessesByLocalitySearch:", error);
      return { error: "An error occurred while fetching businesses." };
    }

    // Calculate if there are more results
    const hasMore = count ? offset + limit < count : false;
    const nextPage = hasMore ? page + 1 : null;

    return {
      data: {
        businesses: data as BusinessCardData[],
        hasMore,
        nextPage,
      },
    };
  } catch (error) {
    console.error("Error in fetchBusinessesByLocalitySearch:", error);
    return { error: "An error occurred while fetching businesses." };
  }
}

// Fetch businesses by locality and location
export async function fetchBusinessesByLocalityAndLocation(params: {
  localityName: string;
  pincode: string;
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy | "price_asc" | "price_desc";
}): Promise<{
  data?: {
    businesses: BusinessCardData[];
    totalCount: number;
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    pincode,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
  } = params;

  try {
    const supabaseAdmin = createAdminClient();
    const offset = (page - 1) * limit;

    // Define fields to select
    const businessFields = `
      id,
      business_name,
      logo_url,
      member_name,
      title,
      address_line,
      city,
      state,
      pincode,
      locality,
      phone,
      instagram_url,
      facebook_url,
      whatsapp_number,
      about_bio,
      status,
      business_slug,
      theme_color,
      delivery_info,
      business_hours,
      business_category,
      total_likes,
      total_subscriptions,
      average_rating,
      created_at,
      updated_at,
      trial_end_date,
      contact_email
    `;

    // Start building the query
    let query = supabaseAdmin
      .from("business_profiles")
      .select(businessFields, { count: "exact" })
      .eq("status", "online")
      .eq("pincode", pincode);

    // Add sorting
    query = query.order(getSortingColumn(sortBy), { ascending: getSortingDirection(sortBy) });

    // Add pagination
    query = query.range(offset, offset + limit - 1);

    // Execute the query
    const { data, count, error } = await query;

    if (error) {
      console.error("Error in fetchBusinessesByLocalityAndLocation:", error);
      return { error: "An error occurred while fetching businesses." };
    }

    // Calculate if there are more results
    const hasMore = count ? offset + limit < count : false;
    const nextPage = hasMore ? page + 1 : null;

    return {
      data: {
        businesses: data as BusinessCardData[],
        totalCount: count || 0,
        hasMore,
        nextPage,
      },
    };
  } catch (error) {
    console.error("Error in fetchBusinessesByLocalityAndLocation:", error);
    return { error: "An error occurred while fetching businesses." };
  }
}

// Fetch more business cards by locality combined
export async function fetchMoreBusinessCardsByLocalityCombined(params: {
  localityName: string;
  pincode: string;
  businessName?: string;
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy | "price_asc" | "price_desc";
}): Promise<{
  data?: {
    businesses: BusinessCardData[];
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    localityName,
    pincode,
    businessName,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
  } = params;

  try {
    // If business name is provided, use search
    if (businessName) {
      const result = await fetchBusinessesByLocalitySearch({
        localityName,
        pincode,
        businessName,
        page,
        limit,
        sortBy,
      });

      if (result.error) {
        return { error: result.error };
      }

      return {
        data: {
          businesses: result.data?.businesses || [],
          hasMore: result.data?.hasMore || false,
          nextPage: result.data?.nextPage || null,
        },
      };
    }

    // Otherwise, use location-based fetch
    const result = await fetchBusinessesByLocalityAndLocation({
      localityName,
      pincode,
      page,
      limit,
      sortBy,
    });

    if (result.error) {
      return { error: result.error };
    }

    return {
      data: {
        businesses: result.data?.businesses || [],
        hasMore: result.data?.hasMore || false,
        nextPage: result.data?.nextPage || null,
      },
    };
  } catch (error) {
    console.error("Error in fetchMoreBusinessCardsByLocalityCombined:", error);
    return { error: "An error occurred while fetching businesses." };
  }
}
