"use server";

import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { revalidatePath } from "next/cache";
import { getProfileImagePath, getScalableUserPath } from "@/lib/utils/storage-paths";
import {
  LOGO_MAX_SIZE_MB,
  ALLOWED_IMAGE_TYPES,
  STORAGE_BUCKET
} from "../utils/constants";

/**
 * Updates only the logo URL in the database
 * @param logoUrl - The new logo URL
 * @returns Success/error response
 */
export async function updateLogoUrl(
  logoUrl: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  const { error: updateError } = await supabase
    .from("business_profiles")
    .update({ logo_url: logoUrl, updated_at: new Date().toISOString() })
    .eq("id", user.id);

  if (updateError) {
    console.error("Logo URL Update Error:", updateError);
    return {
      success: false,
      error: `Failed to update logo URL: ${updateError.message}`,
    };
  }

  revalidatePath("/dashboard/business/card");
  return { success: true };
}

/**
 * Deletes logo from storage and updates the database
 * @returns Success/error response
 */
export async function deleteLogoUrl(): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  // First, get the current logo URL to extract the path
  const { data: profile, error: fetchError } = await supabase
    .from("business_profiles")
    .select("logo_url")
    .eq("id", user.id)
    .single();

  if (fetchError) {
    console.error("Error fetching profile for logo deletion:", fetchError);
    return { success: false, error: "Failed to fetch profile information." };
  }

  // If there's a logo URL, delete the file from storage
  if (profile?.logo_url) {
    try {
      // Extract the file path from the URL
      const urlParts = profile.logo_url.split('/storage/v1/object/public/business/');
      if (urlParts.length === 2) {
        const filePath = urlParts[1].split('?')[0]; // Remove any query parameters

        // Use admin client to delete from storage (required to bypass RLS)
        const adminSupabase = createAdminClient();
        const { error: deleteError } = await adminSupabase.storage
          .from(STORAGE_BUCKET)
          .remove([filePath]);

        if (deleteError && deleteError.message !== "The resource was not found") {
          console.error("Error deleting logo from storage:", deleteError);
          // Continue with database update even if storage deletion fails
        } else {
          console.log("Successfully deleted logo from storage:", filePath);
        }
      } else {
        console.warn("Could not parse logo URL for storage deletion:", profile.logo_url);
      }
    } catch (error) {
      console.error("Error processing logo URL for deletion:", error);
      // Continue with database update even if storage deletion fails
    }
  }

  // Update the database to remove the logo URL
  const { error: updateError } = await supabase
    .from("business_profiles")
    .update({ logo_url: null, updated_at: new Date().toISOString() })
    .eq("id", user.id);

  if (updateError) {
    console.error("Error updating profile after logo deletion:", updateError);
    return {
      success: false,
      error: `Failed to update profile after logo deletion: ${updateError.message}`
    };
  }

  revalidatePath("/dashboard/business/card");
  return { success: true };
}

/**
 * Uploads logo file and returns public URL
 * @param formData - Form data containing the logo file
 * @returns Success/error response with URL
 */
export async function uploadLogoAndGetUrl(
  formData: FormData
): Promise<{ success: boolean; url?: string; error?: string }> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }
  const userId = user.id;

  const file = formData.get("logoFile") as File | null;
  if (!file) {
    return { success: false, error: "No logo file provided." };
  }

  if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
    return { success: false, error: "Invalid file type." };
  }

  // Server-side file size validation
  if (file.size > LOGO_MAX_SIZE_MB * 1024 * 1024) {
    return { success: false, error: `File size must be less than ${LOGO_MAX_SIZE_MB}MB.` };
  }

  const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000);
  const fullPath = getProfileImagePath(userId, timestamp);

  try {
    // Use admin client for cleanup operations to bypass RLS
    const adminSupabase = createAdminClient();

    // Clean up existing logos in the profile folder
    const userPath = getScalableUserPath(userId);
    const profileFolderPath = `${userPath}/profile/`;

    const { data: existingFiles, error: listError } = await adminSupabase.storage
      .from(STORAGE_BUCKET)
      .list(profileFolderPath, { limit: 10 });

    if (!listError && existingFiles && existingFiles.length > 0) {
      const filesToDelete = existingFiles
        .filter(f => f.name.startsWith('logo_'))
        .map(f => `${profileFolderPath}${f.name}`);

      if (filesToDelete.length > 0) {
        const { error: deleteError } = await adminSupabase.storage
          .from(STORAGE_BUCKET)
          .remove(filesToDelete);
        if (deleteError) {
          console.warn(`Error deleting existing logos:`, deleteError.message);
        }
      }
    }
  } catch (e) {
    console.warn("Exception during logo deletion check:", e);
  }

  try {
    // File is already compressed on client-side, just upload it
    const fileBuffer = Buffer.from(await file.arrayBuffer());

    // Use admin client for storage operations to bypass RLS
    const adminSupabase = createAdminClient();

    const { error: uploadError } = await adminSupabase.storage
      .from(STORAGE_BUCKET)
      .upload(fullPath, fileBuffer, {
        contentType: file.type, // Use the file's original type (already compressed)
        upsert: true
      });

    if (uploadError) {
      console.error("Logo Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload logo: ${uploadError.message}`,
      };
    }

    const { data: urlData } = adminSupabase.storage
      .from(STORAGE_BUCKET)
      .getPublicUrl(fullPath);

    if (!urlData?.publicUrl) {
      console.error(
        "Get Public URL Error: URL data is null or missing publicUrl property for path:",
        fullPath
      );
      return {
        success: false,
        error: "Could not retrieve public URL after upload.",
      };
    }

    return { success: true, url: urlData.publicUrl };
  } catch (processingError) {
    console.error("Image Processing/Upload Error:", processingError);
    return { success: false, error: "Failed to process or upload image." };
  }
}
