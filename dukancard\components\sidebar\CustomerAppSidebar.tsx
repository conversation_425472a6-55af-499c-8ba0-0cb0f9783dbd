"use client";

import * as React from "react";
import { SidebarLink } from "./SidebarLink";
import {
  Sidebar,
  SidebarContent,
  <PERSON>barFooter,
  SidebarHeader,
  SidebarRail,
  SidebarGroup,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { NavCustomerUser } from "./NavCustomerUser";
import { ChevronRight } from "lucide-react";
// Import the icon map from NavBusinessMain and add any missing icons
import { iconMap } from "./NavBusinessMain";
import { useCustomerProfile } from "@/contexts/UserDataContext";

// Define types for navigation items
interface NavItem {
  title: string;
  icon?: string;
  url?: string;
  badge?: string;
  badgeVariant?: "default" | "secondary" | "destructive" | "outline" | "upgrade";
}

interface NavSection {
  title: string;
  icon?: string;
  url?: string;
  items: NavItem[];
}

// Define props for customer data
interface CustomerAppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  userName: string | null;
  userAvatarUrl: string | null;
  // Add userEmail if needed later
}

export function CustomerAppSidebar({
  userName: propUserName,
  userAvatarUrl: propUserAvatarUrl,
  ...props
}: CustomerAppSidebarProps) {
  // Use context data with fallback to props
  const { customerProfile } = useCustomerProfile();

  // Determine actual data to use (context first, then props)
  const userName = customerProfile?.name || propUserName;
  const userAvatarUrl = customerProfile?.avatar_url || propUserAvatarUrl;

  // Prepare data structure for nav components
  const userData = {
    name: userName,
    avatar: userAvatarUrl,
    // email: userEmail, // Add if needed
  };

  // Define main navigation items for customer dashboard with collapsible sections
  const navData: NavSection[] = [
    {
      title: "Feed",
      icon: "LayoutList",
      url: "/dashboard/customer",
      items: []
    },
    {
      title: "Overview",
      icon: "LayoutDashboard",
      url: "/dashboard/customer/overview",
      items: []
    },

    {
      title: "Social",
      icon: "Users",
      items: [
        {
          title: "Subscriptions",
          icon: "Bell",
          url: "/dashboard/customer/subscriptions",
        },
        {
          title: "My Likes",
          icon: "Heart",
          url: "/dashboard/customer/likes",
        },
        {
          title: "My Reviews",
          icon: "Star",
          url: "/dashboard/customer/reviews",
        }
      ]
    },
    {
      title: "Account",
      icon: "User",
      items: [
        {
          title: "Profile",
          icon: "User",
          url: "/dashboard/customer/profile",
        },
        {
          title: "Settings",
          icon: "Settings",
          url: "/dashboard/customer/settings",
        }
      ]
    }
  ];

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        {/* Customer sidebar header - can add a simple logo or title if desired */}
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarMenu>
            {navData.map((section, index) => {
              // For Feed and Overview (first two items), render as direct links
              if (index === 0 || index === 1) {
                return (
                  <SidebarMenuItem key={section.title}>
                    <SidebarMenuButton asChild tooltip={section.title}>
                      <SidebarLink href={section.url || "#"}>
                        {section.icon && iconMap[section.icon] && React.createElement(iconMap[section.icon], { className: "h-4 w-4" })}
                        <span>{section.title}</span>
                      </SidebarLink>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              }

              // For other sections, render as collapsible
              return (
                <Collapsible
                  key={section.title}
                  defaultOpen={index === 2} // Open the Social section by default
                  className="group/collapsible"
                >
                  <SidebarMenuItem>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuButton>
                        {section.icon && iconMap[section.icon] && React.createElement(iconMap[section.icon], { className: "h-4 w-4" })}
                        <span>{section.title}</span>
                        <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                      </SidebarMenuButton>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <SidebarMenuSub>
                        {section.items.map((item) => (
                          <SidebarMenuSubItem key={item.title}>
                            <SidebarMenuSubButton asChild>
                              <SidebarLink href={item.url || "#"} className="flex items-center">
                                {item.icon && iconMap[item.icon] && React.createElement(iconMap[item.icon], { className: "h-4 w-4 mr-2" })}
                                <span>{item.title}</span>
                              </SidebarLink>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        ))}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  </SidebarMenuItem>
                </Collapsible>
              );
            })}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <NavCustomerUser user={userData} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
