import { Metadata } from "next";
import { Suspense } from "react";
import { getCategoryBySlug, BUSINESS_CATEGORIES } from "@/lib/config/categories";
import { notFound } from "next/navigation";
import ModernCategoryClient from "./ModernCategoryClient";
import ModernResultsSkeleton from "@/app/(main)/discover/ModernResultsSkeleton";
import { fetchBusinessesByLocation } from "@/lib/actions/categories/locationBasedFetching";

// Force dynamic rendering to prevent build-time generation
export const dynamic = 'force-dynamic';

interface CategoryPageProps {
  params: Promise<{
    categorySlug: string;
  }>;
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const { categorySlug } = resolvedParams;
  const category = getCategoryBySlug(categorySlug);

  if (!category) {
    return {
      title: "Category Not Found",
      description: "The requested category could not be found."
    };
  }

  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const pageUrl = `${siteUrl}/categories/${categorySlug}`;

  return {
    title: `${category.name} Near Me | Best ${category.name} in Your Area`,
    description: `Find ${category.name.toLowerCase()} near you. Browse local ${category.name.toLowerCase()} businesses, products, and services in your area with Dukancard.`,
    openGraph: {
      title: `${category.name} Near Me | Best ${category.name} in Your Area`,
      description: `Find ${category.name.toLowerCase()} near you. Browse local ${category.name.toLowerCase()} businesses, products, and services in your area with Dukancard.`,
      url: pageUrl,
      siteName: "Dukancard",
      locale: "en_IN",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: `${category.name} Near Me | Best ${category.name} in Your Area`,
      description: `Find ${category.name.toLowerCase()} near you. Browse local ${category.name.toLowerCase()} businesses, products, and services in your area with Dukancard.`,
    },
    alternates: {
      canonical: pageUrl,
    },
  };
}

// Server Component - Handles initial rendering
async function CategoryPageContent({ params }: CategoryPageProps) {
  const resolvedParams = await params;
  const { categorySlug } = resolvedParams;
  const category = getCategoryBySlug(categorySlug);

  if (!category) {
    notFound();
  }

  // Create a serializable version of the category without methods
  const serializableCategory = {
    name: category.name,
    slug: category.slug,
    description: category.description,
    isPopular: category.isPopular,
    iconName: category.icon.name // Include the icon name for rendering on the client
  };

  // Fetch businesses in this category
  const { data: businesses, count } = await fetchBusinessesByLocation({
    categoryName: category.name,
    page: 1,
    limit: 20
  });

  return (
    <ModernCategoryClient
      category={serializableCategory}
      initialBusinesses={businesses || []}
      totalCount={count || 0}
    />
  );
}

// Main Page Component using Suspense for streaming
export default async function CategoryPage({ params }: CategoryPageProps) {
  return (
    <div className="min-h-screen bg-white dark:bg-black">
      <Suspense fallback={<ModernResultsSkeleton />}>
        <CategoryPageContent params={params} />
      </Suspense>
    </div>
  );
}

// Generate static params for all categories
export async function generateStaticParams() {
  return BUSINESS_CATEGORIES.map((category) => ({
    categorySlug: category.slug,
  })).filter(params => params.categorySlug); // Filter out categories without slugs
}
