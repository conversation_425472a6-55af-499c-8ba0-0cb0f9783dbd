import React from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useColorScheme } from '@/hooks/useColorScheme';
import { ProductCard } from '@/components/shared/ui';

interface Product {
  id: string;
  name: string;
  description?: string;
  base_price: number;
  discounted_price?: number;
  image_url?: string;
  images?: string[];
  featured_image_index?: number;
  business_id: string;
}

interface ProductRecommendationsProps {
  businessProducts: Product[];
  otherBusinessProducts: (Product & { business_slug: string })[];
  businessSlug: string;
  loading?: boolean;
}

// Convert Product interface to ProductData interface for compatibility
const convertToProductData = (product: Product & { business_slug?: string }) => {
  return {
    id: product.id,
    name: product.name,
    description: product.description,
    base_price: product.base_price,
    discounted_price: product.discounted_price ?? null,
    image_url: product.image_url ?? null,
    images: product.images,
    featured_image_index: product.featured_image_index,
    business_id: product.business_id,
    is_available: true, // Assume available since we're showing them
    business_slug: product.business_slug,
    slug: null, // Products don't have slugs in this context
  };
};

export default function ProductRecommendations({
  businessProducts,
  otherBusinessProducts,
  businessSlug,
  loading = false,
}: ProductRecommendationsProps) {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const subtitleColor = isDark ? '#9CA3AF' : '#6B7280';

  const handleProductPress = (product: Product & { business_slug?: string }) => {
    router.push(`/product/${product.id}`);
  };

  if (loading) {
    return (
      <View style={{ 
        marginTop: 24, 
        paddingHorizontal: 16,
        alignItems: 'center',
        paddingVertical: 20,
      }}>
        <ActivityIndicator size="large" color="#D4AF37" />
        <Text style={{ color: subtitleColor, marginTop: 8, fontSize: 14 }}>
          Loading recommendations...
        </Text>
      </View>
    );
  }

  if (businessProducts.length === 0 && otherBusinessProducts.length === 0) {
    return null;
  }

  return (
    <View style={{ marginTop: 24 }}>
      {/* More from this business */}
      {businessProducts.length > 0 && (
        <View style={{ marginBottom: 24 }}>
          <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
            <Text style={{
              color: textColor,
              fontSize: 20,
              fontWeight: 'bold',
              marginBottom: 4,
            }}>
              More from this business
            </Text>
            <View style={{
              width: 40,
              height: 3,
              backgroundColor: '#D4AF37',
              borderRadius: 2,
            }} />
          </View>

          <FlatList
            data={businessProducts}
            renderItem={({ item }) => (
              <ProductCard
                product={convertToProductData(item)}
                isClickable={true}
                variant="compact"
                width={160}
              />
            )}
            keyExtractor={(item) => `business-${item.id}`}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: 16 }}
            ItemSeparatorComponent={() => <View style={{ width: 12 }} />}
          />
        </View>
      )}

      {/* You might also like */}
      {otherBusinessProducts.length > 0 && (
        <View>
          <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
            <Text style={{
              color: textColor,
              fontSize: 20,
              fontWeight: 'bold',
              marginBottom: 4,
            }}>
              You might also like
            </Text>
            <View style={{
              width: 40,
              height: 3,
              backgroundColor: '#D4AF37',
              borderRadius: 2,
            }} />
          </View>

          <FlatList
            data={otherBusinessProducts}
            renderItem={({ item }) => (
              <ProductCard
                product={convertToProductData(item)}
                isClickable={true}
                variant="compact"
                width={160}
              />
            )}
            keyExtractor={(item) => `other-${item.id}`}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: 16 }}
            ItemSeparatorComponent={() => <View style={{ width: 12 }} />}
          />
        </View>
      )}
    </View>
  );
}
