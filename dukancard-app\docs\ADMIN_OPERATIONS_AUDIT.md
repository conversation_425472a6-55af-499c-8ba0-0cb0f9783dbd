# Admin Operations Audit

This document lists all Supabase admin operations currently used in the React Native app that need to be migrated to Next.js API proxy routes.

## 1. Activity Service (`lib/services/activityService.ts`)

### Admin Operations Required:
- **Get Customer Profiles**: Fetch customer profile data for activity enrichment
  - Table: `customer_profiles`
  - Fields: `id, name, avatar_url, email`
  - Filter: `id IN (userIds[])`
  - Purpose: Enrich activity data with user profile information

- **Get Business Profiles**: Fetch business profile data for activity enrichment
  - Table: `business_profiles`
  - Fields: `id, business_name, business_slug, logo_url`
  - Filter: `id IN (userIds[])`
  - Purpose: Enrich activity data with business profile information

### Required API Endpoints:
```
POST /api/mobile/profiles/batch
Body: { userIds: string[], types: ['customer', 'business'] }
Response: { customerProfiles: [], businessProfiles: [] }
```

## 2. Avatar Upload Service (`lib/services/avatarUploadService.ts`)

### Admin Operations Required:
- **Storage Upload**: Upload avatar images to Supabase storage
  - Bucket: Customer avatars bucket
  - Operation: Upload with upsert
  - Purpose: Bypass RLS for avatar uploads

- **Get Public URL**: Get public URL for uploaded avatars
  - Bucket: Customer avatars bucket
  - Operation: Get public URL
  - Purpose: Return accessible avatar URL

### Required API Endpoints:
```
POST /api/mobile/storage/avatar/upload
Body: { fileBuffer: Buffer, filePath: string, contentType: string }
Response: { success: boolean, url?: string, error?: string }
```

## 3. Onboarding Service (`lib/services/onboardingService.ts`)

### Admin Operations Required:
- **Business Profile Operations**: Create and update business profiles
  - Table: `business_profiles`
  - Operations: INSERT, UPDATE
  - Purpose: Complete business onboarding process

- **Subscription Management**: Handle subscription-related operations
  - Tables: Various subscription-related tables
  - Operations: INSERT, UPDATE
  - Purpose: Set up business subscriptions

### Required API Endpoints:
```
POST /api/mobile/onboarding/business/complete
Body: { onboardingData: OnboardingFormData }
Response: { success: boolean, data?: any, error?: string }
```

## 4. User Data Context (`contexts/UserDataContext.tsx`)

### Admin Operations Required:
- **User Profile Fetching**: Get comprehensive user profile data
  - Tables: `customer_profiles`, `business_profiles`
  - Operations: SELECT with complex joins
  - Purpose: Populate user context with profile data

### Required API Endpoints:
```
GET /api/mobile/user/profile
Response: { customerProfile?: any, businessProfile?: any }
```

## 5. Supabase Admin Utils (`lib/utils/supabaseAdmin.ts`)

### Admin Operations Required:
- **Check Customer Profile**: Verify customer profile existence
  - Table: `customer_profiles`
  - Operation: SELECT with filters
  - Purpose: Profile validation

- **Check Business Profile**: Verify business profile existence
  - Table: `business_profiles`
  - Operation: SELECT with filters
  - Purpose: Profile validation

- **Get User Role Status**: Determine user's role and onboarding status
  - Tables: `customer_profiles`, `business_profiles`
  - Operations: Complex SELECT queries
  - Purpose: Route users to appropriate screens

### Required API Endpoints:
```
GET /api/mobile/user/role-status
Response: { hasCustomerProfile: boolean, hasBusinessProfile: boolean, ... }

POST /api/mobile/user/profile/check
Body: { userId: string, type: 'customer' | 'business' }
Response: { exists: boolean, data?: any }
```

## Security Requirements for API Routes

### Authentication
- All routes must verify user authentication via Supabase session
- Use `createClient()` from `@/utils/supabase/server` to verify user

### Authorization
- Implement API key verification for React Native requests
- Add rate limiting to prevent abuse
- Validate request origins

### Example Security Middleware:
```typescript
// Verify user authentication
const supabase = await createClient();
const { data: { user }, error } = await supabase.auth.getUser();
if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });

// Verify API key for mobile requests
const apiKey = req.headers.get('x-api-key');
if (apiKey !== process.env.MOBILE_API_KEY) {
  return NextResponse.json({ error: 'Invalid API key' }, { status: 401 });
}
```

## Migration Priority

1. **High Priority**: User profile operations (UserDataContext, supabaseAdmin utils)
2. **Medium Priority**: Activity service operations
3. **Low Priority**: Avatar upload and onboarding services

## Next Steps

1. Create API security middleware
2. Implement user profile API routes
3. Create activity service API routes
4. Implement storage proxy routes
5. Update React Native services to use API calls
6. Remove admin client from React Native
7. Test all functionality
