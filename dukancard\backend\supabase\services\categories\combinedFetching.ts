"use server";

import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { createClient } from "@/utils/supabase/server";
import { CategorySearchResult } from "./types";
import { fetchBusinessesByLocation, fetchProductsByLocation } from "@/lib/actions/categories/locationBasedFetching";
import { NearbyProduct } from "@/app/(main)/discover/actions/types";
import { BusinessProfilePublicData } from "@/lib/actions/businessProfiles/types";

// Combined search function that handles both business and product searches for a specific category
export async function searchCategoryCombined(params: {
  categoryName: string;
  businessName?: string | null;
  productName?: string | null;
  state?: string | null;
  pincode?: string | null;
  city?: string | null;
  locality?: string | null;
  stateSlug?: string | null;
  citySlug?: string | null;
  localitySlug?: string | null;
  viewType: "cards" | "products";
  page?: number;
  limit?: number;
  businessSort?: BusinessSortBy | string;
  productSort?: BusinessSortBy | string;
  productType?: "physical" | "service" | null;
}): Promise<{
  data?: CategorySearchResult;
  error?: string;
}> {
  const {
    categoryName,
    businessName,
    productName,
    state,
    pincode,
    city,
    locality,
    stateSlug,
    citySlug,
    localitySlug,
    viewType,
    page = 1,
    limit = viewType === "products" ? 20 : 5,
    businessSort = "created_desc",
    productSort = "created_desc",
    productType = null,
  } = params;

  // Check if category name is provided
  if (!categoryName) {
    return { error: "Category name is required." };
  }

  // Get authentication status
  let isAuthenticated = false;
  try {
    const supabase = await createClient();
    const sessionResponse = await supabase.auth.getSession();
    const session = sessionResponse?.data?.session;
    isAuthenticated = !!session;
  } catch (error) {
    console.error("Error getting authentication status:", error);
    // Default to not authenticated if there's an error
  }

  try {
    // Case 1: Search by product name (only for products view)
    if (viewType === "products") {
      // Fetch products with all available filters
      const productParams = {
        categoryName,
        state, // Use the state parameter passed from the page
        city,
        pincode,
        locality,
        stateSlug, // Pass state slug
        citySlug, // Pass city slug
        localitySlug, // Pass locality slug
        page,
        limit,
        productSortBy: productSort as string,
        productType,
        productName,
      };

      const { data: products, count } = await fetchProductsByLocation(productParams);

      return {
        data: {
          products: products || [],
          isAuthenticated,
          totalCount: count || 0,
          hasMore: count ? (page * limit) < count : false,
          nextPage: count && (page * limit) < count ? page + 1 : null,
          location: {
            state: state || undefined,
            city: city || undefined,
            pincode: pincode || undefined,
            locality: locality || undefined,
          }
        },
      };
    } else {
      // viewType === "cards"
      // Fetch businesses with all available filters
      const businessParams = {
        categoryName,
        state, // Use the state parameter passed from the page
        city,
        pincode,
        locality,
        stateSlug, // Pass state slug
        citySlug, // Pass city slug
        localitySlug, // Pass locality slug
        page,
        limit,
        sortBy: businessSort as BusinessSortBy,
        businessName,
      };

      const { data: businesses, count } = await fetchBusinessesByLocation(businessParams);

      return {
        data: {
          businesses: businesses || [],
          isAuthenticated,
          totalCount: count || 0,
          hasMore: count ? (page * limit) < count : false,
          nextPage: count && (page * limit) < count ? page + 1 : null,
          location: {
            state: state || undefined,
            city: city || undefined,
            pincode: pincode || undefined,
            locality: locality || undefined,
          }
        },
      };
    }
  } catch (error) {
    console.error("Error in searchCategoryCombined:", error);
    return { error: "An error occurred while searching." };
  }
}

// Function to fetch more businesses for a specific category with pagination
export async function fetchMoreBusinessCardsByCategoryCombined(params: {
  categoryName: string;
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy;
  state?: string | null;
  city?: string | null;
  pincode?: string;
  locality?: string;
  stateSlug?: string | null;
  citySlug?: string | null;
  localitySlug?: string | null;
  businessName?: string;
}): Promise<{
  data?: {
    businesses: BusinessProfilePublicData[];
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    categoryName,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
    state,
    city,
    pincode,
    locality,
    stateSlug,
    citySlug,
    localitySlug,
    businessName,
  } = params;

  try {
    const businessParams = {
      categoryName,
      state,
      city,
      pincode,
      locality,
      stateSlug,
      citySlug,
      localitySlug,
      page,
      limit,
      sortBy,
      businessName,
    };

    const { data: businesses, count } = await fetchBusinessesByLocation(businessParams);

    const hasMore = count ? (page * limit) < count : false;
    const nextPage = hasMore ? page + 1 : null;

    return {
      data: {
        businesses: businesses || [],
        hasMore,
        nextPage,
      },
    };
  } catch (error) {
    console.error("Error in fetchMoreBusinessCardsByCategoryCombined:", error);
    return { error: "An error occurred while fetching more businesses." };
  }
}

// Function to fetch more products for a specific category with pagination
export async function fetchMoreProductsByCategoryCombined(params: {
  categoryName: string;
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy | "price_asc" | "price_desc";
  productType?: "physical" | "service" | null;
  state?: string | null;
  city?: string | null;
  pincode?: string;
  locality?: string;
  stateSlug?: string | null;
  citySlug?: string | null;
  localitySlug?: string | null;
  productName?: string;
}): Promise<{
  data?: {
    products: NearbyProduct[];
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    categoryName,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
    productType,
    state,
    city,
    pincode,
    locality,
    stateSlug,
    citySlug,
    localitySlug,
    productName,
  } = params;

  try {
    const productParams = {
      categoryName,
      state,
      city,
      pincode,
      locality,
      stateSlug,
      citySlug,
      localitySlug,
      page,
      limit,
      productSortBy: sortBy,
      productType,
      productName,
    };

    const { data: products, count } = await fetchProductsByLocation(productParams);

    const hasMore = count ? (page * limit) < count : false;
    const nextPage = hasMore ? page + 1 : null;

    return {
      data: {
        products: products || [],
        hasMore,
        nextPage,
      },
    };
  } catch (error) {
    console.error("Error in fetchMoreProductsByCategoryCombined:", error);
    return { error: "An error occurred while fetching more products." };
  }
}
