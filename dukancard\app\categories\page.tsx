import { Metadata } from "next";
import CategoriesPageClient from "./CategoriesPageClient";

export async function generateMetadata(): Promise<Metadata> {
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const pageUrl = `${siteUrl}/categories`;

  return {
    title: "Browse Dukancard Categories",
    description: "Explore business categories on Dukancard. Find local businesses, products, and services by category across India.",
    openGraph: {
      title: "Browse Business Categories | Dukancard",
      description: "Explore business categories on Dukancard. Find local businesses, products, and services by category across India.",
      url: pageUrl,
      siteName: "Dukancard",
      locale: "en_IN",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: "Browse Business Categories | Dukancard",
      description: "Explore business categories on Dukancard. Find local businesses, products, and services by category across India.",
    },
    alternates: {
      canonical: pageUrl,
    },
  };
}

export default function CategoriesPage() {
  return <CategoriesPageClient />;
}
