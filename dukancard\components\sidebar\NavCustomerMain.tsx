"use client";

import React from "react";
import { usePathname } from 'next/navigation';
import { SidebarLink } from "./SidebarLink";
import {
  ChevronRight, // Keep for potential future submenus
  LayoutDashboard,
  Settings,
  User,
  Star,
  Bell,
  Heart,
  type LucideIcon
} from "lucide-react";
import { cn } from "@/lib/utils"; // Added cn import
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";

// Map string icon names to actual Lucide components
const iconMap: { [key: string]: LucideIcon } = {
  LayoutDashboard,
  Settings,
  User,
  Star,
  Bell,
  Heart,
};

interface NavItemData {
  href: string;
  icon: string; // Icon name as string initially
  label: string;
  isActive?: boolean; // Optional active state override
  items?: { // For potential sub-menus
    href: string;
    label: string;
  }[];
}

interface NavCustomerMainProps {
  items: NavItemData[];
}

export function NavCustomerMain({ items }: NavCustomerMainProps) {
  const pathname = usePathname();

  return (
    <SidebarGroup>
      <SidebarMenu>
        {items.map((item) => {
          const IconComponent = iconMap[item.icon];
          // Determine active state based on current path
          const isActive = pathname === item.href || (item.href !== "/dashboard/customer" && pathname.startsWith(item.href));

          // If item has sub-items, render as collapsible
          if (item.items && item.items.length > 0) {
            return (
              <Collapsible
                key={item.label}
                asChild
                defaultOpen={isActive}
                className="group/collapsible"
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild>
                    {/* Apply brand gold styling more directly to active state */}
                    <SidebarMenuButton tooltip={item.label} isActive={isActive} className={cn(isActive && "bg-accent text-[var(--brand-gold)] dark:bg-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]")}>
                      {IconComponent && <IconComponent className={cn(isActive ? "text-[var(--brand-gold)]" : "")}/>}
                      <span>{item.label}</span>
                      <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.items.map((subItem) => {
                         const isSubActive = pathname === subItem.href;
                         return (
                            <SidebarMenuSubItem key={subItem.label}>
                              <SidebarMenuSubButton asChild isActive={isSubActive}>
                                <SidebarLink href={subItem.href}>
                                  <span>{subItem.label}</span>
                                </SidebarLink>
                              </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                         );
                      })}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            );
          }

          // Otherwise, render as a direct link
          return (
            <SidebarMenuItem key={item.label}>
              {/* Apply brand gold styling more directly to active state */}
              <SidebarMenuButton asChild tooltip={item.label} isActive={isActive} className={cn(isActive && "bg-accent text-[var(--brand-gold)] dark:bg-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]")}>
                <SidebarLink href={item.href}>
                  {IconComponent && <IconComponent className={cn(isActive ? "text-[var(--brand-gold)]" : "")}/>}
                  <span>{item.label}</span>
                </SidebarLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
