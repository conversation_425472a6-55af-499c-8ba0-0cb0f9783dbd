# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: expo-modules-core
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/.cxx/Debug/6b4c83s4/arm64-v8a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target expo-modules-core


#############################################
# Order-only phony target for expo-modules-core

build cmake_object_order_depends_target_expo-modules-core: phony || cmake_object_order_depends_target_fabric

build CMakeFiles/expo-modules-core.dir/5126a7643ca8c24f96e2827f80013d4e/expo-modules-core/common/cpp/EventEmitter.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\5126a7643ca8c24f96e2827f80013d4e\expo-modules-core\common\cpp\EventEmitter.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\5126a7643ca8c24f96e2827f80013d4e\expo-modules-core\common\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/22f0323662f9f862765212e7c3fb5a94/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\22f0323662f9f862765212e7c3fb5a94\node_modules\expo-modules-core\common\cpp\JSIUtils.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\22f0323662f9f862765212e7c3fb5a94\node_modules\expo-modules-core\common\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/22f0323662f9f862765212e7c3fb5a94/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/common/cpp/LazyObject.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\22f0323662f9f862765212e7c3fb5a94\node_modules\expo-modules-core\common\cpp\LazyObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\22f0323662f9f862765212e7c3fb5a94\node_modules\expo-modules-core\common\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/5126a7643ca8c24f96e2827f80013d4e/expo-modules-core/common/cpp/NativeModule.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/common/cpp/NativeModule.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\5126a7643ca8c24f96e2827f80013d4e\expo-modules-core\common\cpp\NativeModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\5126a7643ca8c24f96e2827f80013d4e\expo-modules-core\common\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/5126a7643ca8c24f96e2827f80013d4e/expo-modules-core/common/cpp/ObjectDeallocator.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/common/cpp/ObjectDeallocator.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\5126a7643ca8c24f96e2827f80013d4e\expo-modules-core\common\cpp\ObjectDeallocator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\5126a7643ca8c24f96e2827f80013d4e\expo-modules-core\common\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/5126a7643ca8c24f96e2827f80013d4e/expo-modules-core/common/cpp/SharedObject.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/common/cpp/SharedObject.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\5126a7643ca8c24f96e2827f80013d4e\expo-modules-core\common\cpp\SharedObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\5126a7643ca8c24f96e2827f80013d4e\expo-modules-core\common\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/22f0323662f9f862765212e7c3fb5a94/node_modules/expo-modules-core/common/cpp/SharedRef.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/common/cpp/SharedRef.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\22f0323662f9f862765212e7c3fb5a94\node_modules\expo-modules-core\common\cpp\SharedRef.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\22f0323662f9f862765212e7c3fb5a94\node_modules\expo-modules-core\common\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/22f0323662f9f862765212e7c3fb5a94/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/common/cpp/TypedArray.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\22f0323662f9f862765212e7c3fb5a94\node_modules\expo-modules-core\common\cpp\TypedArray.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\22f0323662f9f862765212e7c3fb5a94\node_modules\expo-modules-core\common\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/Exceptions.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\Exceptions.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/ExpoModulesHostObject.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\ExpoModulesHostObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/JNIDeallocator.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JNIDeallocator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/JNIFunctionBody.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JNIFunctionBody.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/JNIInjector.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JNIInjector.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIUtils.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/JNIUtils.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JNIUtils.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIContext.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/JSIContext.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JSIContext.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/JSReferencesCache.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JSReferencesCache.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JSharedObject.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/JSharedObject.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JSharedObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/JavaCallback.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaCallback.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/JavaReferencesCache.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaReferencesCache.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptFunction.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaScriptFunction.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptModuleObject.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaScriptModuleObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptObject.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaScriptObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptRuntime.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaScriptRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptTypedArray.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaScriptTypedArray.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptValue.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaScriptValue.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptWeakObject.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/JavaScriptWeakObject.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\JavaScriptWeakObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/MethodMetadata.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\MethodMetadata.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/RuntimeHolder.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/RuntimeHolder.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\RuntimeHolder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/WeakRuntimeHolder.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\WeakRuntimeHolder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/types/AnyType.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\types\AnyType.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\types
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/types/ExpectedType.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\types\ExpectedType.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\types
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/types/FrontendConverter.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\types\FrontendConverter.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\types
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/types/FrontendConverterProvider.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\types\FrontendConverterProvider.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\types
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/types/JNIToJSIConverter.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\types\JNIToJSIConverter.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\types
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSClassesDecorator.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSClassesDecorator.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators\JSClassesDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSConstantsDecorator.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSConstantsDecorator.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators\JSConstantsDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators\JSDecoratorsBridgingObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSFunctionsDecorator.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSFunctionsDecorator.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators\JSFunctionsDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSObjectDecorator.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSObjectDecorator.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators\JSObjectDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb

build CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSPropertiesDecorator.cpp.o: CXX_COMPILER__expo-modules-core_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/main/cpp/decorators/JSPropertiesDecorator.cpp || cmake_object_order_depends_target_expo-modules-core
  DEFINES = -Dexpo_modules_core_EXPORTS
  DEP_FILE = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators\JSPropertiesDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/src/fabric" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  OBJECT_FILE_DIR = CMakeFiles\expo-modules-core.dir\src\main\cpp\decorators
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target expo-modules-core


#############################################
# Link the shared library ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.so

build ../../../../build/intermediates/cxx/Debug/6b4c83s4/obj/arm64-v8a/libexpo-modules-core.so: CXX_SHARED_LIBRARY_LINKER__expo-modules-core_Debug CMakeFiles/expo-modules-core.dir/5126a7643ca8c24f96e2827f80013d4e/expo-modules-core/common/cpp/EventEmitter.cpp.o CMakeFiles/expo-modules-core.dir/22f0323662f9f862765212e7c3fb5a94/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o CMakeFiles/expo-modules-core.dir/22f0323662f9f862765212e7c3fb5a94/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o CMakeFiles/expo-modules-core.dir/5126a7643ca8c24f96e2827f80013d4e/expo-modules-core/common/cpp/NativeModule.cpp.o CMakeFiles/expo-modules-core.dir/5126a7643ca8c24f96e2827f80013d4e/expo-modules-core/common/cpp/ObjectDeallocator.cpp.o CMakeFiles/expo-modules-core.dir/5126a7643ca8c24f96e2827f80013d4e/expo-modules-core/common/cpp/SharedObject.cpp.o CMakeFiles/expo-modules-core.dir/22f0323662f9f862765212e7c3fb5a94/node_modules/expo-modules-core/common/cpp/SharedRef.cpp.o CMakeFiles/expo-modules-core.dir/22f0323662f9f862765212e7c3fb5a94/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIUtils.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIContext.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JSharedObject.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptWeakObject.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/RuntimeHolder.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSClassesDecorator.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSConstantsDecorator.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSFunctionsDecorator.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSObjectDecorator.cpp.o CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSPropertiesDecorator.cpp.o | C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/liblog.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so src/fabric/libfabric.a C$:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so || src/fabric/libfabric.a
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/liblog.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  -landroid  src/fabric/libfabric.a  C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  -latomic -lm
  OBJECT_DIR = CMakeFiles\expo-modules-core.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libexpo-modules-core.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\expo-modules-core.dir\
  TARGET_FILE = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.so
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\6b4c83s4\obj\arm64-v8a\libexpo-modules-core.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Desktop\Dukancard Web App\dukancard-app\node_modules\expo-modules-core\android\.cxx\Debug\6b4c83s4\arm64-v8a" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Desktop\Dukancard Web App\dukancard-app\node_modules\expo-modules-core\android\.cxx\Debug\6b4c83s4\arm64-v8a" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\Users\<USER>\Desktop\Dukancard Web App\dukancard-app\node_modules\expo-modules-core\android" -B"C:\Users\<USER>\Desktop\Dukancard Web App\dukancard-app\node_modules\expo-modules-core\android\.cxx\Debug\6b4c83s4\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target fabric


#############################################
# Order-only phony target for fabric

build cmake_object_order_depends_target_fabric: phony || src/fabric/CMakeFiles/fabric.dir

build src/fabric/CMakeFiles/fabric.dir/0c0ce0c8b065a0bfeebc2f94ca34aaa0/common/cpp/fabric/ExpoViewComponentDescriptor.cpp.o: CXX_COMPILER__fabric_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/common/cpp/fabric/ExpoViewComponentDescriptor.cpp || cmake_object_order_depends_target_fabric
  DEP_FILE = src\fabric\CMakeFiles\fabric.dir\0c0ce0c8b065a0bfeebc2f94ca34aaa0\common\cpp\fabric\ExpoViewComponentDescriptor.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp/fabric" -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = src\fabric\CMakeFiles\fabric.dir
  OBJECT_FILE_DIR = src\fabric\CMakeFiles\fabric.dir\0c0ce0c8b065a0bfeebc2f94ca34aaa0\common\cpp\fabric
  TARGET_COMPILE_PDB = src\fabric\CMakeFiles\fabric.dir\fabric.pdb
  TARGET_PDB = src\fabric\libfabric.pdb

build src/fabric/CMakeFiles/fabric.dir/0c0ce0c8b065a0bfeebc2f94ca34aaa0/common/cpp/fabric/ExpoViewEventEmitter.cpp.o: CXX_COMPILER__fabric_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/common/cpp/fabric/ExpoViewEventEmitter.cpp || cmake_object_order_depends_target_fabric
  DEP_FILE = src\fabric\CMakeFiles\fabric.dir\0c0ce0c8b065a0bfeebc2f94ca34aaa0\common\cpp\fabric\ExpoViewEventEmitter.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp/fabric" -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = src\fabric\CMakeFiles\fabric.dir
  OBJECT_FILE_DIR = src\fabric\CMakeFiles\fabric.dir\0c0ce0c8b065a0bfeebc2f94ca34aaa0\common\cpp\fabric
  TARGET_COMPILE_PDB = src\fabric\CMakeFiles\fabric.dir\fabric.pdb
  TARGET_PDB = src\fabric\libfabric.pdb

build src/fabric/CMakeFiles/fabric.dir/5126a7643ca8c24f96e2827f80013d4e/expo-modules-core/common/cpp/fabric/ExpoViewProps.cpp.o: CXX_COMPILER__fabric_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/common/cpp/fabric/ExpoViewProps.cpp || cmake_object_order_depends_target_fabric
  DEP_FILE = src\fabric\CMakeFiles\fabric.dir\5126a7643ca8c24f96e2827f80013d4e\expo-modules-core\common\cpp\fabric\ExpoViewProps.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp/fabric" -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = src\fabric\CMakeFiles\fabric.dir
  OBJECT_FILE_DIR = src\fabric\CMakeFiles\fabric.dir\5126a7643ca8c24f96e2827f80013d4e\expo-modules-core\common\cpp\fabric
  TARGET_COMPILE_PDB = src\fabric\CMakeFiles\fabric.dir\fabric.pdb
  TARGET_PDB = src\fabric\libfabric.pdb

build src/fabric/CMakeFiles/fabric.dir/0c0ce0c8b065a0bfeebc2f94ca34aaa0/common/cpp/fabric/ExpoViewShadowNode.cpp.o: CXX_COMPILER__fabric_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/common/cpp/fabric/ExpoViewShadowNode.cpp || cmake_object_order_depends_target_fabric
  DEP_FILE = src\fabric\CMakeFiles\fabric.dir\0c0ce0c8b065a0bfeebc2f94ca34aaa0\common\cpp\fabric\ExpoViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp/fabric" -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = src\fabric\CMakeFiles\fabric.dir
  OBJECT_FILE_DIR = src\fabric\CMakeFiles\fabric.dir\0c0ce0c8b065a0bfeebc2f94ca34aaa0\common\cpp\fabric
  TARGET_COMPILE_PDB = src\fabric\CMakeFiles\fabric.dir\fabric.pdb
  TARGET_PDB = src\fabric\libfabric.pdb

build src/fabric/CMakeFiles/fabric.dir/FabricComponentsRegistry.cpp.o: CXX_COMPILER__fabric_Debug C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/expo-modules-core/android/src/fabric/FabricComponentsRegistry.cpp || cmake_object_order_depends_target_fabric
  DEP_FILE = src\fabric\CMakeFiles\fabric.dir\FabricComponentsRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/../common/cpp/fabric" -IC:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include
  OBJECT_DIR = src\fabric\CMakeFiles\fabric.dir
  OBJECT_FILE_DIR = src\fabric\CMakeFiles\fabric.dir
  TARGET_COMPILE_PDB = src\fabric\CMakeFiles\fabric.dir\fabric.pdb
  TARGET_PDB = src\fabric\libfabric.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target fabric


#############################################
# Link the static library src\fabric\libfabric.a

build src/fabric/libfabric.a: CXX_STATIC_LIBRARY_LINKER__fabric_Debug src/fabric/CMakeFiles/fabric.dir/0c0ce0c8b065a0bfeebc2f94ca34aaa0/common/cpp/fabric/ExpoViewComponentDescriptor.cpp.o src/fabric/CMakeFiles/fabric.dir/0c0ce0c8b065a0bfeebc2f94ca34aaa0/common/cpp/fabric/ExpoViewEventEmitter.cpp.o src/fabric/CMakeFiles/fabric.dir/5126a7643ca8c24f96e2827f80013d4e/expo-modules-core/common/cpp/fabric/ExpoViewProps.cpp.o src/fabric/CMakeFiles/fabric.dir/0c0ce0c8b065a0bfeebc2f94ca34aaa0/common/cpp/fabric/ExpoViewShadowNode.cpp.o src/fabric/CMakeFiles/fabric.dir/FabricComponentsRegistry.cpp.o || C$:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info
  OBJECT_DIR = src\fabric\CMakeFiles\fabric.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = src\fabric\CMakeFiles\fabric.dir\fabric.pdb
  TARGET_FILE = src\fabric\libfabric.a
  TARGET_PDB = src\fabric\libfabric.pdb


#############################################
# Utility command for edit_cache

build src/fabric/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Desktop\Dukancard Web App\dukancard-app\node_modules\expo-modules-core\android\.cxx\Debug\6b4c83s4\arm64-v8a\src\fabric" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build src/fabric/edit_cache: phony src/fabric/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/fabric/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Desktop\Dukancard Web App\dukancard-app\node_modules\expo-modules-core\android\.cxx\Debug\6b4c83s4\arm64-v8a\src\fabric" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\Users\<USER>\Desktop\Dukancard Web App\dukancard-app\node_modules\expo-modules-core\android" -B"C:\Users\<USER>\Desktop\Dukancard Web App\dukancard-app\node_modules\expo-modules-core\android\.cxx\Debug\6b4c83s4\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/fabric/rebuild_cache: phony src/fabric/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build expo-modules-core: phony ../../../../build/intermediates/cxx/Debug/6b4c83s4/obj/arm64-v8a/libexpo-modules-core.so

build fabric: phony src/fabric/libfabric.a

build libexpo-modules-core.so: phony ../../../../build/intermediates/cxx/Debug/6b4c83s4/obj/arm64-v8a/libexpo-modules-core.so

build libfabric.a: phony src/fabric/libfabric.a

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/.cxx/Debug/6b4c83s4/arm64-v8a

build all: phony ../../../../build/intermediates/cxx/Debug/6b4c83s4/obj/arm64-v8a/libexpo-modules-core.so src/fabric/all

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/.cxx/Debug/6b4c83s4/arm64-v8a/src/fabric

build src/fabric/all: phony src/fabric/libfabric.a

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | ../../../../CMakeLists.txt ../../../../src/fabric/CMakeLists.txt ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build ../../../../CMakeLists.txt ../../../../src/fabric/CMakeLists.txt ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake ../prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake C$:/Users/<USER>/Desktop/Dukancard$ Web$ App/dukancard-app/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
