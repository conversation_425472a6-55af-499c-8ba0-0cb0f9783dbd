{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "src/fabric", "jsonFile": "directory-src.fabric-Debug-1d0d1ee8408532e247d5.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "src/fabric", "targetIndexes": [1]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1], "name": "expo-modules-core", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "expo-modules-core::@6890427a1f51a3e7e1df", "jsonFile": "target-expo-modules-core-Debug-4464bf1b29aed2e1e322.json", "name": "expo-modules-core", "projectIndex": 0}, {"directoryIndex": 1, "id": "fabric::@3c04bbf757b97f4dae7c", "jsonFile": "target-fabric-Debug-74a12070ac1c1d4afcf5.json", "name": "fabric", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android/.cxx/Debug/6b4c83s4/arm64-v8a", "source": "C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/expo-modules-core/android"}, "version": {"major": 2, "minor": 3}}