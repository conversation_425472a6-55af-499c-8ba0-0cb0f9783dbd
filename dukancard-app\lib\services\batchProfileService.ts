/**
 * Batch Profile Service for React Native
 * 
 * Handles batch profile fetching operations using direct Supabase client calls
 * with Row Level Security (RLS) policies for security.
 * 
 * This service replaces the need for Next.js proxy API routes by leveraging:
 * - Public read access for profile discovery
 * - Direct Supabase client calls for better performance
 * - Efficient batch operations for activity feeds
 */

import { supabase } from '@/lib/supabase';

// Types for batch profile operations
export interface CustomerProfile {
  id: string;
  name: string;
  avatar_url?: string;
  email: string;
}

export interface BusinessProfile {
  id: string;
  business_name: string;
  business_slug: string;
  logo_url?: string;
}

export interface BatchProfileResponse {
  customerProfiles: CustomerProfile[];
  businessProfiles: BusinessProfile[];
}

export interface ServiceResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Fetch multiple user profiles for activity enrichment
 * Uses public RLS policies for read access
 */
export async function fetchBatchProfiles(
  userIds: string[],
  types: ('customer' | 'business')[]
): Promise<ServiceResult<BatchProfileResponse>> {
  try {
    // Validate input
    if (!Array.isArray(userIds) || userIds.length === 0) {
      return { 
        success: true, 
        data: { customerProfiles: [], businessProfiles: [] } 
      };
    }

    if (!Array.isArray(types) || types.length === 0) {
      return { 
        success: true, 
        data: { customerProfiles: [], businessProfiles: [] } 
      };
    }

    const response: BatchProfileResponse = {
      customerProfiles: [],
      businessProfiles: [],
    };

    // Fetch customer profiles if requested
    if (types.includes('customer')) {
      const { data: customerProfiles, error: customerError } = await supabase
        .from('customer_profiles')
        .select('id, name, avatar_url, email')
        .in('id', userIds);

      if (customerError) {
        console.error('[BATCH_PROFILE_SERVICE] Error fetching customer profiles:', customerError);
        return { success: false, error: 'Failed to fetch customer profiles' };
      }

      response.customerProfiles = customerProfiles || [];
    }

    // Fetch business profiles if requested
    if (types.includes('business')) {
      const { data: businessProfiles, error: businessError } = await supabase
        .from('business_profiles')
        .select('id, business_name, business_slug, logo_url')
        .in('id', userIds);

      if (businessError) {
        console.error('[BATCH_PROFILE_SERVICE] Error fetching business profiles:', businessError);
        return { success: false, error: 'Failed to fetch business profiles' };
      }

      response.businessProfiles = businessProfiles || [];
    }

    return { success: true, data: response };
  } catch (error) {
    console.error('[BATCH_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Fetch customer profiles by IDs
 */
export async function fetchCustomerProfiles(
  userIds: string[]
): Promise<ServiceResult<CustomerProfile[]>> {
  try {
    if (!Array.isArray(userIds) || userIds.length === 0) {
      return { success: true, data: [] };
    }

    const { data: profiles, error } = await supabase
      .from('customer_profiles')
      .select('id, name, avatar_url, email')
      .in('id', userIds);

    if (error) {
      console.error('[BATCH_PROFILE_SERVICE] Error fetching customer profiles:', error);
      return { success: false, error: 'Failed to fetch customer profiles' };
    }

    return { success: true, data: profiles || [] };
  } catch (error) {
    console.error('[BATCH_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Fetch business profiles by IDs
 */
export async function fetchBusinessProfiles(
  userIds: string[]
): Promise<ServiceResult<BusinessProfile[]>> {
  try {
    if (!Array.isArray(userIds) || userIds.length === 0) {
      return { success: true, data: [] };
    }

    const { data: profiles, error } = await supabase
      .from('business_profiles')
      .select('id, business_name, business_slug, logo_url')
      .in('id', userIds);

    if (error) {
      console.error('[BATCH_PROFILE_SERVICE] Error fetching business profiles:', error);
      return { success: false, error: 'Failed to fetch business profiles' };
    }

    return { success: true, data: profiles || [] };
  } catch (error) {
    console.error('[BATCH_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Create a profile lookup map for easy access
 */
export function createProfileLookupMap(
  customerProfiles: CustomerProfile[],
  businessProfiles: BusinessProfile[]
): {
  customers: { [id: string]: CustomerProfile };
  businesses: { [id: string]: BusinessProfile };
} {
  const customers: { [id: string]: CustomerProfile } = {};
  const businesses: { [id: string]: BusinessProfile } = {};

  customerProfiles.forEach(profile => {
    customers[profile.id] = profile;
  });

  businessProfiles.forEach(profile => {
    businesses[profile.id] = profile;
  });

  return { customers, businesses };
}

/**
 * Enrich activity data with profile information
 * Utility function to add profile data to activity items
 */
export async function enrichActivitiesWithProfiles<T extends { user_id?: string; business_id?: string; customer_id?: string }>(
  activities: T[],
  options?: {
    includeCustomers?: boolean;
    includeBusinesses?: boolean;
  }
): Promise<ServiceResult<Array<T & {
  customerProfile?: CustomerProfile;
  businessProfile?: BusinessProfile;
}>>> {
  try {
    if (!Array.isArray(activities) || activities.length === 0) {
      return { success: true, data: [] };
    }

    // Extract unique user IDs from activities
    const userIds = new Set<string>();
    activities.forEach(activity => {
      if (activity.user_id) userIds.add(activity.user_id);
      if (activity.business_id) userIds.add(activity.business_id);
      if (activity.customer_id) userIds.add(activity.customer_id);
    });

    const uniqueUserIds = Array.from(userIds);
    
    if (uniqueUserIds.length === 0) {
      return { success: true, data: activities as any };
    }

    // Determine which profile types to fetch
    const types: ('customer' | 'business')[] = [];
    if (options?.includeCustomers !== false) types.push('customer');
    if (options?.includeBusinesses !== false) types.push('business');

    // Fetch profiles
    const profilesResult = await fetchBatchProfiles(uniqueUserIds, types);
    
    if (!profilesResult.success) {
      return { success: false, error: profilesResult.error };
    }

    const { customerProfiles, businessProfiles } = profilesResult.data!;
    const profileMap = createProfileLookupMap(customerProfiles, businessProfiles);

    // Enrich activities with profile data
    const enrichedActivities = activities.map(activity => {
      const enriched: any = { ...activity };

      // Add customer profile if available
      const customerId = activity.customer_id || activity.user_id;
      if (customerId && profileMap.customers[customerId]) {
        enriched.customerProfile = profileMap.customers[customerId];
      }

      // Add business profile if available
      const businessId = activity.business_id || activity.user_id;
      if (businessId && profileMap.businesses[businessId]) {
        enriched.businessProfile = profileMap.businesses[businessId];
      }

      return enriched;
    });

    return { success: true, data: enrichedActivities };
  } catch (error) {
    console.error('[BATCH_PROFILE_SERVICE] Error enriching activities:', error);
    return { success: false, error: 'Failed to enrich activities with profiles' };
  }
}

/**
 * Get profile display information for a user ID
 * Returns the most appropriate profile for display purposes
 */
export async function getDisplayProfile(
  userId: string
): Promise<ServiceResult<{
  type: 'customer' | 'business' | null;
  profile: CustomerProfile | BusinessProfile | null;
  displayName: string;
  displayImage?: string;
}>> {
  try {
    const profilesResult = await fetchBatchProfiles([userId], ['customer', 'business']);
    
    if (!profilesResult.success) {
      return { success: false, error: profilesResult.error };
    }

    const { customerProfiles, businessProfiles } = profilesResult.data!;
    
    // Prefer business profile over customer profile for display
    if (businessProfiles.length > 0) {
      const businessProfile = businessProfiles[0];
      return {
        success: true,
        data: {
          type: 'business',
          profile: businessProfile,
          displayName: businessProfile.business_name,
          displayImage: businessProfile.logo_url,
        }
      };
    }

    if (customerProfiles.length > 0) {
      const customerProfile = customerProfiles[0];
      return {
        success: true,
        data: {
          type: 'customer',
          profile: customerProfile,
          displayName: customerProfile.name,
          displayImage: customerProfile.avatar_url,
        }
      };
    }

    return {
      success: true,
      data: {
        type: null,
        profile: null,
        displayName: 'Unknown User',
      }
    };
  } catch (error) {
    console.error('[BATCH_PROFILE_SERVICE] Error getting display profile:', error);
    return { success: false, error: 'Failed to get display profile' };
  }
}
