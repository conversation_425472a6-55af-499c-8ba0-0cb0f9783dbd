/**
 * Configuration settings for category pages and sitemaps
 */

// Cache duration in seconds (60 days)
export const CACHE_DURATION_SECONDS = 60 * 60 * 24 * 60;

// Page size for pagination (Supabase has a 1000 row limit per fetch)
export const PAGE_SIZE = 1000;

// Sitemap settings
export const SITEMAP_CONFIG = {
  // Default change frequency for sitemap entries
  defaultChangeFrequency: 'weekly' as const,
  
  // Priority values for different types of pages
  priority: {
    categoryPage: 0.8,
    statePage: 0.7,
    cityPage: 0.6,
    pincodePage: 0.6,
    localityPage: 0.5,
    sitemapFile: 0.4,
  }
};
