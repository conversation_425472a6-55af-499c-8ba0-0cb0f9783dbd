'use client';

import { useState } from 'react';
import Image from 'next/image';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Link as LinkIcon, X } from 'lucide-react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { createPost, updatePost } from '@/lib/actions/posts';
import { postSchema, PostFormData, PostData } from '@/lib/types/posts';
import ProductSelector from './ProductSelector';
import MediaUpload from './MediaUpload';
import LocationDisplay from './LocationDisplay';
import { usePostMediaUpload } from '../hooks/usePostMediaUpload';
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, Plus } from 'lucide-react';

interface PostFormProps {
  post?: PostData;
  onSuccess?: () => void;
  onCancel?: () => void;
  showCard?: boolean;
}

export default function PostForm({ post, onSuccess, onCancel, showCard = true }: PostFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadMethod, setUploadMethod] = useState<'upload' | 'url'>('upload');
  const router = useRouter();

  // Initialize form with existing post data or defaults
  const form = useForm<PostFormData>({
    resolver: zodResolver(postSchema),
    defaultValues: {
      id: post?.id,
      content: post?.content || '',
      image_url: post?.image_url || '',
      product_ids: post?.product_ids || [],
      mentioned_business_ids: post?.mentioned_business_ids || [],
    },
  });

  // Media upload hook
  const {
    uploadError,
    previewUrl,
    isUploading,
    imageToCrop: _imageToCrop,
    originalFile: _originalFile,
    handleFileSelect,
    handleUpload,
    clearImage,
    handleCropComplete: _handleCropComplete,
    handleCropCancel: _handleCropCancel,
  } = usePostMediaUpload({
    onUploadSuccess: (url) => {
      form.setValue('image_url', url, { shouldDirty: true });
    },
    onUploadError: (error) => {
      toast.error('Upload failed', { description: error });
    },
  });

  // Check if business has complete address
  const checkBusinessAddress = async () => {
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      toast.error('Please log in to continue');
      return false;
    }

    const { data: profile, error } = await supabase
      .from('business_profiles')
      .select('pincode, city, state, locality')
      .eq('id', user.id)
      .single();

    if (error) {
      toast.error('Failed to check business profile');
      return false;
    }

    // Check if all address fields are filled
    if (!profile?.pincode || !profile?.city || !profile?.state || !profile?.locality) {
      toast.error('Please complete your business address before creating posts', {
        description: 'You will be redirected to update your business card',
        action: {
          label: 'Update Now',
          onClick: () => router.push('/dashboard/business/card')
        }
      });

      // Redirect after a short delay
      setTimeout(() => {
        router.push('/dashboard/business/card');
      }, 2000);

      return false;
    }

    return true;
  };

  // Handle form submission
  const onSubmit = async (data: PostFormData) => {
    setIsSubmitting(true);

    try {
      // Check business address before creating new posts
      if (!post?.id) {
        const hasValidAddress = await checkBusinessAddress();
        if (!hasValidAddress) {
          setIsSubmitting(false);
          return;
        }
      }
      const finalData = { ...data };

      // Clean up image_url field - convert empty strings to null
      if (!finalData.image_url || finalData.image_url.trim() === '') {
        finalData.image_url = null;
      }

      let result;

      if (post?.id) {
        // Updating existing post
        result = await updatePost(post.id, finalData);
      } else {
        // Creating new post
        if (uploadMethod === 'upload' && previewUrl && !finalData.image_url) {
          // For new posts with uploaded images:
          // 1. Create post first without image_url to get real post ID
          // 2. Upload image using real post ID
          // 3. Update post with image_url

          const createResult = await createPost({ ...finalData, image_url: null });
          if (!createResult.success || !createResult.data) {
            toast.error(createResult.error || 'Failed to create post');
            return;
          }

          // Type assertion for the created post data
          const createdPost = createResult.data as { id: string; created_at: string };

          // Upload the image using the real post ID and creation date
          const uploadedUrl = await handleUpload(createdPost.id, undefined, createdPost.created_at);
          if (uploadedUrl) {
            // Update the post with the image URL
            const updateResult = await updatePost(createdPost.id, {
              ...finalData,
              image_url: uploadedUrl
            });
            result = updateResult;
          } else {
            // Image upload failed, but post was created
            result = createResult;
          }
        } else {
          // No image upload needed, create post normally
          result = await createPost(finalData);
        }
      }

      if (result.success) {
        toast.success(post?.id ? 'Post updated successfully' : 'Post created successfully');
        form.reset();
        clearImage();
        if (onSuccess) onSuccess();
      } else {
        toast.error(result.error || 'Failed to save post');
      }
    } catch (error) {
      console.error('Error saving post:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle image URL change (for URL method)
  const handleImageUrlChange = (url: string) => {
    form.setValue('image_url', url);
  };

  // Clear all images
  const handleClearAllImages = () => {
    form.setValue('image_url', '');
    clearImage();
  };

  const formContent = (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="space-y-4">
          {/* Location Display */}
          <LocationDisplay />

          {/* Post content */}
          <FormField
            control={form.control}
            name="content"
            render={({ field }) => {
              const charCount = field.value?.length || 0;
              const maxChars = 2000;
              const isOverLimit = charCount > maxChars;

              return (
                <FormItem>
                  <FormLabel>Content</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Textarea
                        placeholder="What's on your mind?"
                        className={`min-h-[120px] pr-16 ${isOverLimit ? 'border-destructive focus:border-destructive' : ''}`}
                        {...field}
                      />
                      <div className={`absolute bottom-2 right-2 text-xs ${
                        isOverLimit ? 'text-destructive' : 'text-muted-foreground'
                      }`}>
                        {charCount}/{maxChars}
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />

          {/* Image Upload/URL Section */}
          <FormField
            control={form.control}
            name="image_url"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Add Image (optional)</FormLabel>

                {/* Upload method tabs */}
                <Tabs value={uploadMethod} onValueChange={(value) => setUploadMethod(value as 'upload' | 'url')}>
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="upload">Upload Image</TabsTrigger>
                    <TabsTrigger value="url">Image URL</TabsTrigger>
                  </TabsList>

                  <TabsContent value="upload" className="space-y-4">
                    <MediaUpload
                      previewUrl={previewUrl || (uploadMethod === 'upload' ? (field.value || null) : null)}
                      isUploading={isUploading}
                      uploadError={uploadError}
                      onFileSelect={handleFileSelect}
                      onClearImage={handleClearAllImages}
                      disabled={isSubmitting}
                    />
                  </TabsContent>

                  <TabsContent value="url" className="space-y-4">
                    <div className="flex space-x-2">
                      <FormControl>
                        <Input
                          placeholder="https://example.com/image.jpg"
                          {...field}
                          value={field.value || ''}
                          onChange={(e) => handleImageUrlChange(e.target.value)}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      {field.value && (
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={handleClearAllImages}
                          disabled={isSubmitting}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    {/* URL image preview */}
                    {uploadMethod === 'url' && field.value && (
                      <div className="relative mt-2 rounded-md overflow-hidden border border-border">
                        <div className="relative w-full max-w-md mx-auto">
                          <Image
                            src={field.value}
                            alt="Preview"
                            width={400}
                            height={300}
                            className="object-contain w-full h-auto"
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            onError={() => {
                              toast.error('Failed to load image');
                              form.setValue('image_url', '');
                            }}
                          />
                        </div>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>

                <FormMessage />
              </FormItem>
            )}
          />

          <Separator className="my-4" />

          {/* Product selector */}
          <FormField
            control={form.control}
            name="product_ids"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1">
                  <LinkIcon className="h-4 w-4" />
                  Link Products
                </FormLabel>
                <FormDescription>
                  Link your products to this post to showcase them to your customers
                </FormDescription>
                <FormControl>
                  <ProductSelector
                    selectedProductIds={field.value}
                    onProductsChange={field.onChange}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex flex-col-reverse sm:flex-row gap-3 pt-6">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
              className="flex-1 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200"
            >
              Cancel
            </Button>
          )}

          <motion.div
            whileTap={{ scale: 0.98 }}
            className="flex-1"
          >
            <Button
              type="submit"
              disabled={isSubmitting}
              className={`
                w-full relative overflow-hidden
                bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-dark)]
                hover:from-[var(--brand-gold-dark)] hover:to-[var(--brand-gold)]
                text-[var(--brand-gold-foreground)] font-medium
                shadow-lg hover:shadow-xl
                transition-all duration-300
                ${isSubmitting ? 'cursor-not-allowed opacity-80' : ''}
              `}
            >
              <AnimatePresence mode="wait">
                {isSubmitting ? (
                  <motion.div
                    key="submitting"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className="flex items-center justify-center"
                  >
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {post?.id ? 'Updating...' : 'Posting...'}
                  </motion.div>
                ) : (
                  <motion.div
                    key="submit"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className="flex items-center justify-center"
                  >
                    {post?.id ? (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Update Post
                      </>
                    ) : (
                      <>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Post
                      </>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </motion.div>
        </div>
      </form>

      {/* Image cropping removed - using original images */}
    </Form>
  );

  if (!showCard) {
    return formContent;
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{post?.id ? 'Edit Post' : 'Create New Post'}</CardTitle>
      </CardHeader>
      <CardContent>
        {formContent}
      </CardContent>
    </Card>
  );
}
