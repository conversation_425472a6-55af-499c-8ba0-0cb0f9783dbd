'use client';

import { motion } from 'framer-motion';
import { FeedFilterType } from '@/lib/types/posts';
import FilterPills from './FilterPills';

interface ModernFeedHeaderProps {
  activeFilter: FeedFilterType;
  onFilterChange: (_filter: FeedFilterType) => void;
  isLoading?: boolean;
}

export default function ModernFeedHeader({
  activeFilter,
  onFilterChange,
  isLoading = false
}: ModernFeedHeaderProps) {
  return (
    <div className="space-y-6 mb-6">
      {/* Filter Pills */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="bg-white dark:bg-black rounded-xl border border-neutral-200 dark:border-neutral-800 p-4 shadow-sm"
      >
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-semibold text-neutral-900 dark:text-neutral-100">
            Filter Posts
          </h3>
          <div className="text-xs text-neutral-500 dark:text-neutral-400">
            Choose your feed preference
          </div>
        </div>

        <FilterPills
          activeFilter={activeFilter}
          onFilterChange={onFilterChange}
          isLoading={isLoading}
        />
      </motion.div>
    </div>
  );
}
