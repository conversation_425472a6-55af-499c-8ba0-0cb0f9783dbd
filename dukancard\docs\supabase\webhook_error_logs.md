# Webhook Error Logs Table Documentation

## Table Overview

The `webhook_error_logs` table in the Dukancard application records errors that occur during webhook processing. It provides detailed information about failed webhook events, enabling debugging, monitoring, and potential retry of problematic events, particularly for payment-related webhooks from Razorpay.
 
## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | uuid_generate_v4() | Primary key for the error log record |
| event_type | text | NO | | Type of webhook event that failed (e.g., 'payment.authorized', 'subscription.charged') |
| event_id | text | YES | | Unique identifier for the webhook event (typically provided by the webhook source) |
| subscription_id | text | YES | | ID of the subscription related to the webhook event (if applicable) |
| error_message | text | NO | | Detailed error message describing what went wrong |
| payload | jsonb | NO | | Complete JSON payload of the webhook event that failed |
| retry_count | integer | NO | 0 | Number of times processing has been retried |
| status | text | NO | | Current status of the error ('pending', 'retrying', 'resolved', 'failed') |
| created_at | timestamptz | NO | now() | Timestamp when the error was first logged |
| updated_at | timestamptz | NO | now() | Timestamp when the error log was last updated |

## Constraints

### Primary Key
- `webhook_error_logs_pkey` - Primary key constraint on the `id` column

### Not Null Constraints
Multiple check constraints ensure that required fields are not null.

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| webhook_error_logs_pkey | UNIQUE | id | Primary key index |

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| Admin users can access webhook error logs | ALL | (auth.role() = 'service_role'::text) | |

This policy ensures that only users with the 'service_role' can access webhook error logs, providing an additional layer of security for sensitive payment data.

## Related Tables

### processed_webhook_events
While not directly linked via foreign key, the `webhook_error_logs` table is complementary to the `processed_webhook_events` table, with the former tracking failed events and the latter tracking successfully processed events.

## Usage Notes

1. **Error Tracking**:
   - The table provides a comprehensive record of webhook processing failures
   - Each record includes the complete webhook payload and a detailed error message
   - This information is crucial for diagnosing and resolving integration issues

2. **Retry Mechanism**:
   - The `retry_count` field tracks how many times processing has been attempted
   - The `status` field indicates the current state of the error handling process
   - This supports a systematic approach to retrying failed webhook events

3. **Payment Integration**:
   - Based on project memory, the primary webhook source is Razorpay payment gateway
   - The `subscription_id` field specifically tracks errors related to subscription payments
   - This is critical for ensuring subscription status changes are properly processed

4. **Security**:
   - RLS policies restrict access to users with the 'service_role'
   - This ensures that sensitive payment information is only accessible to administrative users
   - The restriction is particularly important as the table contains complete webhook payloads

5. **Monitoring and Alerting**:
   - The table can be used to generate alerts for webhook processing failures
   - Monitoring the volume and patterns of errors can help identify systemic issues
   - The `created_at` and `updated_at` timestamps support time-based analysis

6. **Debugging Process**:
   - When investigating webhook issues, administrators can:
     - Review the error message for specific failure reasons
     - Examine the complete payload to verify the webhook content
     - Check the retry count and status to understand the handling history
     - Use the event_id to correlate with external systems

7. **Data Retention**:
   - The table may grow over time as it records all webhook processing errors
   - A data retention policy might be needed to periodically clean up resolved errors
   - Maintaining a history of recent errors is valuable for troubleshooting patterns

8. **Relationship to Business Operations**:
   - Webhook errors, particularly for subscription events, can directly impact business operations
   - Failed subscription updates might prevent businesses from accessing paid features
   - Timely resolution of webhook errors is therefore critical to business continuity
