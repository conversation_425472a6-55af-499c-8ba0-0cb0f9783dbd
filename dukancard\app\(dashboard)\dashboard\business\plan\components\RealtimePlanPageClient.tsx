"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { realtimeService } from "@/backend/supabase/services/realtime/realtimeService";
import EnhancedPlanPageWithManager from "./EnhancedPlanPageWithManager";
import BusinessPlanSkeleton from "./BusinessPlanSkeleton";
import { SubscriptionStatus } from "../page";
import { PricingPlan } from "@/lib/PricingPlans";


interface RealtimePlanPageClientProps {
  userId: string;
  currentPlanDetails?: PricingPlan;
  subscriptionStatus: SubscriptionStatus;
  trialEndDate: string | null;
  subscriptionEndDate: string | null;
  monthlyPlans: PricingPlan[];
  yearlyPlans: PricingPlan[];
  currentSubscriptionId: string | null;
  nextBillingDate: string | null;
  cancellationRequestedAt: string | null;
  cancelledAt: string | null;
  planCycle: "monthly" | "yearly";
  authenticatedSubscriptionStartDate?: string | null;
  // New props for subscription dates from Supabase
  subscriptionStartDate?: string | null;
  subscriptionExpiryTime?: string | null;
  subscriptionChargeTime?: string | null;
  isEligibleForRefund?: boolean;
  // Payment method
  lastPaymentMethod?: string | null;
  // Razorpay subscription data to determine switch vs fresh subscription
  razorpaySubscriptionId?: string | null;
}

// Define the type for payment_subscriptions table
interface PaymentSubscription {
  id: string;
  business_profile_id: string;
  razorpay_subscription_id: string | null;
  razorpay_customer_id: string | null;
  subscription_status: string;
  plan_id: string;
  plan_cycle: string;
  subscription_start_date: string | null;
  subscription_expiry_time: string | null;
  subscription_charge_time: string | null;
  last_payment_date: string | null;
  last_payment_method: string | null;
  subscription_paused_at: string | null;
  cancellation_requested_at: string | null;
  cancelled_at: string | null;
  created_at: string;
  updated_at: string;
}

export default function RealtimePlanPageClient(props: RealtimePlanPageClientProps) {
  const router = useRouter();
  const [isUpdated, setIsUpdated] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Detect client-side rendering
  useEffect(() => {
    setIsClient(true);

    // Simulate data loading with a short timeout
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800); // Show skeleton for 800ms to ensure smooth transition

    return () => clearTimeout(timer);
  }, []);

  // Set up Supabase real-time subscription to payment_subscriptions table
  // Only run this effect on the client side
  useEffect(() => {
    if (!isClient || !props.userId) return;

    console.log("[REALTIME_PLAN] Setting up real-time subscription for user:", props.userId);

    // Subscribe to changes in payment_subscriptions table
    const subscription = realtimeService.subscribeToPaymentSubscriptions(
      props.userId,
      async (payload) => {
        console.log("[REALTIME_PLAN] Subscription update received:", payload);

        // Handle different event types
        if (payload.eventType === 'UPDATE' && payload.new && 'cancelled_at' in payload.new && payload.new.cancelled_at && payload.old && !('cancelled_at' in payload.old && payload.old.cancelled_at)) {
          // Handle cancellation event (when cancelled_at is updated from null to a date)
          console.log("[REALTIME_PLAN] Subscription cancelled:", payload.new);

          // Show toast notification about the cancellation
          toast.info("Subscription Cancelled", {
            description: "Your subscription cancellation has been confirmed by Razorpay.",
          });

          // Highlight the update
          setIsUpdated(true);
          setTimeout(() => setIsUpdated(false), 1000);

          // Refresh the page to update the UI
          router.refresh();
        }
        // Handle INSERT and UPDATE events
        else if (payload.new && 'subscription_status' in payload.new) {
          const newData = payload.new as PaymentSubscription;

          // Show toast notification about the update
          const statusMessage = getStatusMessage(newData.subscription_status);
          if (statusMessage) {
            toast.info("Subscription Updated", {
              description: statusMessage,
            });
          }

          // Highlight the update
          setIsUpdated(true);
          setTimeout(() => setIsUpdated(false), 1000);

          // Refresh the page to get the latest data
          router.refresh();
        }
      },
      'plan-page'
    );

    // Cleanup function
    return () => {
      console.log("[REALTIME_PLAN] Cleaning up subscription");
      subscription.unsubscribe();
    };
  }, [props.userId, isClient]); // Removed router from dependencies to prevent re-subscription on refresh

  // Helper function to get a user-friendly message based on subscription status
  function getStatusMessage(status: string): string | null {
    switch (status.toLowerCase()) {
      case "active":
        return "Your subscription is now active!";
      case "authenticated":
        return "Your payment has been authorized. Your subscription will start on the scheduled date.";
      case "created":
        return "Your subscription has been created. Please complete the payment process.";
      case "pending":
        return "Your subscription is pending payment authorization.";
      case "halted":
        return "Your subscription has been halted due to payment issues. Please update your payment method.";
      case "cancelled":
        return "Your subscription has been cancelled. You can subscribe again anytime.";
      case "pending_cancellation":
        return "Your subscription will be cancelled at the end of the current billing cycle.";
      case "completed":
        return "Your subscription has been completed. Thank you for being a subscriber!";
      case "expired":
        return "Your subscription has expired. Please renew to continue using premium features.";
      case "paused":
        return "Your subscription has been paused. You can resume it anytime.";
      default:
        return null;
    }
  }

  // Use a stable class name for server-side rendering to avoid hydration mismatch
  const containerClassName = isClient
    ? `transition-all duration-300 ${isUpdated ? 'bg-primary/5 rounded-xl p-2 -m-2' : ''}`
    : 'transition-all duration-300';

  // Show skeleton while loading
  if (isLoading) {
    return <BusinessPlanSkeleton />;
  }

  return (
    <div className={containerClassName}>
      <EnhancedPlanPageWithManager {...props} />
    </div>
  );
}
