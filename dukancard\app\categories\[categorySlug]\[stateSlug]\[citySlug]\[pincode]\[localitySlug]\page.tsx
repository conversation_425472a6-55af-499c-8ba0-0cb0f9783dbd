import { Metadata } from "next";
import { Suspense } from "react";
import { getCategoryBySlug } from "@/lib/config/categories";
import { notFound } from "next/navigation";
import ModernCategoryClient from "../../../../ModernCategoryClient";
import ModernResultsSkeleton from "@/app/(main)/discover/ModernResultsSkeleton";
import { INDIAN_STATE_DATA } from "@/lib/config/states";
import { createSitemapClient } from "@/utils/supabase/sitemap";
import { unstable_cache } from "next/cache";
import { fetchBusinessesByLocation } from "@/lib/actions/categories/locationBasedFetching";

// Force dynamic rendering to prevent build-time generation
export const dynamic = 'force-dynamic';

interface CategoryLocalityPageProps {
  params: Promise<{
    categorySlug: string;
    stateSlug: string;
    citySlug: string;
    pincode: string;
    localitySlug: string;
  }>;
}

export async function generateMetadata({ params }: CategoryLocalityPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const { categorySlug, stateSlug, citySlug, pincode, localitySlug } = resolvedParams;

  // Get the category by slug
  const category = getCategoryBySlug(categorySlug);
  if (!category) {
    return {
      title: "Category Not Found",
      description: "The requested category could not be found."
    };
  }

  // Find the matching state using the state_slug directly
  const stateData = INDIAN_STATE_DATA.find(state => state.slug === stateSlug);
  const matchingState = stateData?.name;

  if (!matchingState) {
    return {
      title: "State Not Found",
      description: "The requested state could not be found."
    };
  }

  // Format state name for display (Title Case)
  const formattedState = matchingState
    .toLowerCase()
    .replace(/\b\w/g, (char) => char.toUpperCase());

  // Define a function to find a matching locality with caching
  const findMatchingLocality = unstable_cache(
    async (stateName: string, citySlugParam: string, pincodeParam: string, localitySlugParam: string) => {
      console.log(`Looking up locality with slug "${localitySlugParam}" in ${citySlugParam}, ${pincodeParam}, ${stateName} (this will be cached)`);

      const supabaseClient = createSitemapClient();

      // Get city and locality directly using the slugs
      const { data, error } = await supabaseClient
        .from("pincodes")
        .select("DivisionName, OfficeName")
        .eq("StateName", stateName)
        .eq("city_slug", citySlugParam)
        .eq("Pincode", pincodeParam)
        .eq("locality_slug", localitySlugParam)
        .limit(1);

      if (error) {
        console.error("Error fetching locality by slug:", error);
        return { city: "", locality: "" };
      }

      if (!data || data.length === 0) {
        console.warn(`No locality found with slug "${localitySlugParam}" in city "${citySlugParam}", pincode "${pincodeParam}", state "${stateName}"`);
        return { city: "", locality: "" };
      }

      return {
        city: data[0].DivisionName,
        locality: data[0].OfficeName
      };
    },
    ["locality-by-city-pincode-slug"], // Cache key prefix
    {
      revalidate: 60 * 60 * 24 * 30, // Revalidate every 30 days (in seconds)
      tags: [`state-city-pincode-locality-${matchingState}-${citySlug}-${pincode}-${localitySlug}`], // Cache tag for this specific lookup
    }
  );

  // Use the cached function to find the matching locality
  const { city: matchingCity, locality: matchingLocality } = await findMatchingLocality(
    matchingState,
    citySlug,
    pincode,
    localitySlug
  );

  if (!matchingCity || !matchingLocality) {
    return {
      title: "Location Not Found",
      description: "The requested location could not be found."
    };
  }

  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const pageUrl = `${siteUrl}/categories/${categorySlug}/${stateSlug}/${citySlug}/${pincode}/${localitySlug}`;

  return {
    title: `${category.name} in ${matchingLocality}, ${matchingCity} ${pincode} | Best ${category.name} in ${matchingLocality}`,
    description: `Find ${category.name.toLowerCase()} in ${matchingLocality}, ${matchingCity}, ${pincode}, ${formattedState}. Browse local ${category.name.toLowerCase()} businesses, products, and services in ${matchingLocality} with Dukancard.`,
    openGraph: {
      title: `${category.name} in ${matchingLocality}, ${matchingCity} ${pincode} | Best ${category.name} in ${matchingLocality}`,
      description: `Find ${category.name.toLowerCase()} in ${matchingLocality}, ${matchingCity}, ${pincode}, ${formattedState}. Browse local ${category.name.toLowerCase()} businesses, products, and services in ${matchingLocality} with Dukancard.`,
      url: pageUrl,
      siteName: "Dukancard",
      locale: "en_IN",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: `${category.name} in ${matchingLocality}, ${matchingCity} ${pincode} | Best ${category.name} in ${matchingLocality}`,
      description: `Find ${category.name.toLowerCase()} in ${matchingLocality}, ${matchingCity}, ${pincode}, ${formattedState}. Browse local ${category.name.toLowerCase()} businesses, products, and services in ${matchingLocality} with Dukancard.`,
    },
    alternates: {
      canonical: pageUrl,
    },
  };
}

// Server Component - Handles initial rendering
async function CategoryLocalityPageContent({ params }: CategoryLocalityPageProps) {
  const resolvedParams = await params;
  const { categorySlug, stateSlug, citySlug, pincode, localitySlug } = resolvedParams;

  // Get the category by slug
  const category = getCategoryBySlug(categorySlug);
  if (!category) {
    notFound();
  }

  // Find the matching state using the state_slug directly
  const stateData = INDIAN_STATE_DATA.find(state => state.slug === stateSlug);
  const matchingState = stateData?.name;

  if (!matchingState) {
    notFound();
  }

  // Define a function to find a matching locality with caching
  const findMatchingLocality = unstable_cache(
    async (stateName: string, citySlugParam: string, pincodeParam: string, localitySlugParam: string) => {
      console.log(`Looking up locality with slug "${localitySlugParam}" in ${citySlugParam}, ${pincodeParam}, ${stateName} (this will be cached)`);

      const supabaseClient = createSitemapClient();

      // Get city and locality directly using the slugs
      const { data, error } = await supabaseClient
        .from("pincodes")
        .select("DivisionName, OfficeName")
        .eq("StateName", stateName)
        .eq("city_slug", citySlugParam)
        .eq("Pincode", pincodeParam)
        .eq("locality_slug", localitySlugParam)
        .limit(1);

      if (error) {
        console.error("Error fetching locality by slug:", error);
        return { city: "", locality: "" };
      }

      if (!data || data.length === 0) {
        console.warn(`No locality found with slug "${localitySlugParam}" in city "${citySlugParam}", pincode "${pincodeParam}", state "${stateName}"`);
        return { city: "", locality: "" };
      }

      return {
        city: data[0].DivisionName,
        locality: data[0].OfficeName
      };
    },
    ["locality-by-city-pincode-slug"], // Cache key prefix
    {
      revalidate: 60 * 60 * 24 * 30, // Revalidate every 30 days (in seconds)
      tags: [`state-city-pincode-locality-${matchingState}-${citySlug}-${pincode}-${localitySlug}`], // Cache tag for this specific lookup
    }
  );

  // Use the cached function to find the matching locality
  const { city: matchingCity, locality: matchingLocality } = await findMatchingLocality(
    matchingState,
    citySlug,
    pincode,
    localitySlug
  );

  if (!matchingCity || !matchingLocality) {
    notFound();
  }

  // Create a serializable version of the category without methods
  const serializableCategory = {
    name: category.name,
    slug: category.slug,
    description: category.description,
    isPopular: category.isPopular,
    iconName: category.icon.name // Include the icon name for rendering on the client
  };

  // Fetch businesses in this category filtered by state, city, pincode, and locality slugs
  const { data: businesses, count } = await fetchBusinessesByLocation({
    categoryName: category.name,
    state: matchingState,
    city: matchingCity,
    pincode: pincode,
    locality: matchingLocality,
    stateSlug: stateSlug,
    citySlug: citySlug,
    localitySlug: localitySlug,
    page: 1,
    limit: 20
  });

  return (
    <ModernCategoryClient
      category={serializableCategory}
      initialBusinesses={businesses || []}
      totalCount={count || 0}
      locationInfo={{
        state: matchingState,
        city: matchingCity,
        pincode: pincode,
        locality: matchingLocality
      }}
    />
  );
}

// Main Page Component using Suspense for streaming
export default async function CategoryLocalityPage({ params }: CategoryLocalityPageProps) {
  return (
    <div className="min-h-screen bg-white dark:bg-black">
      <Suspense fallback={<ModernResultsSkeleton />}>
        <CategoryLocalityPageContent params={params} />
      </Suspense>
    </div>
  );
}

// Note: We don't need generateStaticParams here as the localities are too numerous
// and will be generated on-demand instead
