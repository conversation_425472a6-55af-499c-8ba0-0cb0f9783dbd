"use client";

import { useRef, useEffect } from "react";
import { Loader2 } from "lucide-react";
import { motion } from "framer-motion";
import { NearbyProduct } from "@/app/(main)/discover/actions/types";
import ProductGrid from "@/app/(main)/discover/components/ProductGrid";
import { ProductSortOption, ProductFilterOption } from "../context/types";

interface ProductResultsProps {
  products: NearbyProduct[];
  totalCount: number;
  hasMore: boolean;
  isLoadingMore: boolean;
  onLoadMore: () => void;
  onSortChange: (_sortBy: ProductSortOption) => void;
  onFilterChange: (_filterBy: ProductFilterOption) => void;
  onSearch: (_searchTerm: string) => void;
  currentSortBy: ProductSortOption;
  currentFilterBy: ProductFilterOption;
  isLoading: boolean;
  initialSearchTerm?: string | null;
}

export default function ProductResults({
  products,
  hasMore,
  isLoadingMore,
  onLoadMore,
  onSortChange,
  onFilter<PERSON>hange,
  onSearch,
  currentSortBy,
  currentFilterBy,
  isLoading,
  initialSearchTerm,
}: ProductResultsProps) {
  const observerTarget = useRef<HTMLDivElement>(null);

  // Set up intersection observer for infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoadingMore) {
          onLoadMore();
        }
      },
      { threshold: 0.1, rootMargin: "100px" }
    );

    const currentTarget = observerTarget.current;
    if (currentTarget) {
      observer.observe(currentTarget);
    }

    return () => {
      if (currentTarget) {
        observer.unobserve(currentTarget);
      }
    };
  }, [hasMore, isLoadingMore, onLoadMore]);

  return (
    <div>
      <ProductGrid
        products={products}
        onSortChange={onSortChange}
        onFilterChange={onFilterChange}
        onSearch={onSearch}
        currentSortBy={currentSortBy}
        currentFilterBy={currentFilterBy}
        isLoading={isLoading}
        initialSearchTerm={initialSearchTerm}
      />

      {/* Loading More Indicator - Always render the observer target */}
      {!isLoading && (
        <div
          ref={observerTarget}
          className="flex justify-center items-center py-8"
        >
          {isLoadingMore ? (
            <motion.div
              className="flex flex-col items-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <Loader2 className="h-6 w-6 animate-spin text-[var(--brand-gold)]" />
              <span className="mt-2 text-sm text-neutral-600 dark:text-neutral-400">
                Loading more products...
              </span>
            </motion.div>
          ) : hasMore ? (
            <span className="text-xs text-neutral-500 dark:text-neutral-500">
              Scroll to load more
            </span>
          ) : products.length > 0 ? (
            <span className="text-xs text-neutral-500 dark:text-neutral-500">
              You&apos;ve reached the end
            </span>
          ) : null}
        </div>
      )}
    </div>
  );
}
