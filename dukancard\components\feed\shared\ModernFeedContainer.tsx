'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface ModernFeedContainerProps {
  children: ReactNode;
  className?: string;
}

export default function ModernFeedContainer({ children, className }: ModernFeedContainerProps) {
  return (
    <div
      className={cn(
        "min-h-screen bg-gradient-to-br from-neutral-50 via-white to-neutral-50",
        "dark:from-neutral-950 dark:via-black dark:to-neutral-950",
        className
      )}
    >
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="container max-w-4xl mx-auto px-1 sm:px-2 md:px-4 py-6 md:py-8"
      >
        {children}
      </motion.div>
    </div>
  );
}
