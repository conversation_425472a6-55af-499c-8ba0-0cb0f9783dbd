'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { Check, ChevronsUpDown, Loader2, X, Package, Search, GripVertical } from 'lucide-react';
import Image from 'next/image';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

import { cn } from '@/lib/utils';
import { ProductData } from '@/lib/types/posts';
import { searchBusinessProducts, getSelectedProducts } from '@/lib/actions/shared/productActions';

interface ProductSelectorProps {
  selectedProductIds: string[];
  onProductsChange: (_productIds: string[]) => void;
}

// Sortable Product Item Component
interface SortableProductItemProps {
  product: ProductData;
  onRemove: (_productId: string) => void;
  formatPrice: (_price: number | null) => string;
}

function SortableProductItem({ product, onRemove, formatPrice }: SortableProductItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: product.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 bg-muted/50 rounded-lg border min-h-[60px] sm:min-h-[68px]"
    >
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="cursor-grab active:cursor-grabbing text-muted-foreground hover:text-foreground shrink-0"
      >
        <GripVertical className="h-4 w-4" />
      </div>

      {/* Product Image */}
      <div className="relative h-8 w-8 sm:h-10 sm:w-10 shrink-0 rounded-md overflow-hidden bg-background">
        {product.image_url ? (
          <Image
            src={product.image_url}
            alt={product.name}
            fill
            className="object-cover"
            sizes="(max-width: 640px) 32px, 40px"
          />
        ) : (
          <div className="flex items-center justify-center h-full w-full">
            <Package className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
          </div>
        )}
      </div>

      {/* Product Details */}
      <div className="flex-1 min-w-0 pr-1 sm:pr-2">
        {/* Product Name - Truncated to single line for consistent height */}
        <div className="font-medium text-xs sm:text-sm leading-tight mb-1">
          <span className="line-clamp-1 break-words">
            {product.name}
          </span>
        </div>

        {/* Price - Single line layout */}
        <div className="text-xs text-muted-foreground">
          {product.discounted_price ? (
            <div className="flex items-center gap-1 flex-wrap">
              <span className="text-primary font-medium">
                {formatPrice(product.discounted_price)}
              </span>
              <span className="line-through text-xs">
                {formatPrice(product.base_price)}
              </span>
            </div>
          ) : (
            <span className="font-medium">
              {formatPrice(product.base_price)}
            </span>
          )}
        </div>
      </div>

      {/* Remove Button */}
      <Button
        variant="ghost"
        size="icon"
        className="h-6 w-6 sm:h-8 sm:w-8 shrink-0 hover:bg-destructive/10 hover:text-destructive"
        onClick={() => onRemove(product.id)}
      >
        <X className="h-3 w-3 sm:h-4 sm:w-4" />
      </Button>
    </div>
  );
}

export default function ProductSelector({
  selectedProductIds,
  onProductsChange,
}: ProductSelectorProps) {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [products, setProducts] = useState<ProductData[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<ProductData[]>([]);
  const [hasSearched, setHasSearched] = useState(false);

  // Debounce timer ref
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = selectedProducts.findIndex((product) => product.id === active.id);
      const newIndex = selectedProducts.findIndex((product) => product.id === over?.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const newSelectedProducts = arrayMove(selectedProducts, oldIndex, newIndex);
        setSelectedProducts(newSelectedProducts);

        // Update the product IDs array to maintain the new order
        const newSelectedIds = newSelectedProducts.map(product => product.id);
        onProductsChange(newSelectedIds);
      }
    }
  };



  // Load selected products details using server action
  const loadSelectedProducts = useCallback(async () => {
    if (selectedProductIds.length === 0) {
      setSelectedProducts([]);
      return;
    }

    setIsLoading(true);
    try {
      const result = await getSelectedProducts(selectedProductIds);

      if (result.success && result.data) {
        // Maintain the order of selectedProductIds
        const orderedProducts = selectedProductIds
          .map(id => result.data?.find(product => product.id === id))
          .filter(Boolean) as ProductData[];

        setSelectedProducts(orderedProducts);
      } else {
        console.error('Error loading selected products:', result.error);
        setSelectedProducts([]);
      }
    } catch (error) {
      console.error('Error loading selected products:', error);
      setSelectedProducts([]);
    } finally {
      setIsLoading(false);
    }
  }, [selectedProductIds]);

  // Load selected products on mount
  useEffect(() => {
    if (selectedProductIds.length > 0) {
      loadSelectedProducts();
    }
  }, [selectedProductIds, loadSelectedProducts]);

  // Search products using server action
  const searchProducts = async (query: string) => {
    setIsLoading(true);
    setHasSearched(false);
    try {
      const result = await searchBusinessProducts(query);

      if (result.success && result.data) {
        setProducts(result.data);
      } else {
        console.error('Error searching products:', result.error);
        setProducts([]);
      }
    } catch (error) {
      console.error('Error searching products:', error);
      setProducts([]);
    } finally {
      setIsLoading(false);
      setHasSearched(true);
    }
  };

  // Handle search input change with debouncing
  const handleSearchChange = (value: string) => {
    setSearchValue(value);

    // Clear existing timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Clear products if search is too short
    if (value.length < 2) {
      setProducts([]);
      setHasSearched(false);
      return;
    }

    // Set new timer for debounced search
    debounceTimerRef.current = setTimeout(() => {
      searchProducts(value);
    }, 300); // 300ms debounce delay
  };

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // Toggle product selection
  const toggleProduct = (product: ProductData) => {
    const isSelected = selectedProductIds.includes(product.id);
    let newSelectedIds: string[];

    if (isSelected) {
      newSelectedIds = selectedProductIds.filter(id => id !== product.id);
      setSelectedProducts(prev => prev.filter(p => p.id !== product.id));
    } else {
      // Check if maximum limit is reached
      if (selectedProductIds.length >= 5) {
        return; // Don't add more products if limit is reached
      }
      newSelectedIds = [...selectedProductIds, product.id];
      setSelectedProducts(prev => [...prev, product]);
    }

    onProductsChange(newSelectedIds);
  };

  // Remove a selected product
  const removeProduct = (productId: string) => {
    const newSelectedIds = selectedProductIds.filter(id => id !== productId);
    setSelectedProducts(prev => prev.filter(p => p.id !== productId));
    onProductsChange(newSelectedIds);
  };

  // Format price display
  const formatPrice = (price: number | null) => {
    if (price === null) return 'N/A';
    return `₹${price.toLocaleString('en-IN')}`;
  };

  return (
    <div className="space-y-4">
      {/* Product Search Combobox */}
      <Popover open={open} onOpenChange={(newOpen) => {
        setOpen(newOpen);
        if (!newOpen) {
          // Clear search when closing
          setSearchValue('');
          setProducts([]);
          setHasSearched(false);
        }
      }}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between h-auto min-h-[40px] px-3 py-2"
          >
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4 text-muted-foreground" />
              <span className="text-left text-muted-foreground">
                Search and add products...
              </span>
            </div>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="p-0"
          align="start"
          sideOffset={4}
          style={{ width: 'var(--radix-popover-trigger-width)' }}
        >
          <Command>
            <CommandInput
              placeholder="Search your products..."
              value={searchValue}
              onValueChange={handleSearchChange}
              className="h-9 border-0 focus:ring-0 focus:ring-offset-0"
            />
            <CommandList className="max-h-[300px]">
              {/* Show loading state */}
              {isLoading && (
                <div className="flex items-center justify-center py-8">
                  <div className="flex flex-col items-center gap-2">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                    <span className="text-sm text-muted-foreground">Searching products...</span>
                  </div>
                </div>
              )}

              {/* Show empty state only when not loading and no products */}
              {!isLoading && products.length === 0 && (
                <CommandEmpty>
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <Package className="h-8 w-8 text-muted-foreground mb-2" />
                    <span className="text-sm font-medium">
                      {searchValue.length < 2
                        ? 'Type at least 2 characters to search'
                        : hasSearched
                        ? 'No products found'
                        : ''}
                    </span>
                    {searchValue.length >= 2 && hasSearched && (
                      <span className="text-xs text-muted-foreground mt-1">
                        Try a different search term
                      </span>
                    )}
                  </div>
                </CommandEmpty>
              )}

              {/* Show products only when not loading and we have products */}
              {!isLoading && products.length > 0 && (
                <CommandGroup>
                  {products.map((product) => (
                  <CommandItem
                    key={product.id}
                    value={product.slug || product.id}
                    onSelect={() => {
                      // Only allow selection if not already at max limit or if already selected
                      if (selectedProductIds.length < 5 || selectedProductIds.includes(product.id)) {
                        toggleProduct(product);
                        setOpen(false);
                        setSearchValue('');
                        setProducts([]);
                        setHasSearched(false);
                      }
                    }}
                    disabled={selectedProductIds.length >= 5 && !selectedProductIds.includes(product.id)}
                    className={cn(
                      "flex items-center gap-3 p-3 cursor-pointer",
                      selectedProductIds.length >= 5 && !selectedProductIds.includes(product.id)
                        ? "opacity-50 cursor-not-allowed"
                        : ""
                    )}
                  >
                    {/* Product Image */}
                    <div className="relative h-10 w-10 shrink-0 rounded-md overflow-hidden bg-muted">
                      {product.image_url ? (
                        <Image
                          src={product.image_url}
                          alt={product.name}
                          fill
                          className="object-cover"
                          sizes="40px"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full w-full">
                          <Package className="h-5 w-5 text-muted-foreground" />
                        </div>
                      )}
                    </div>

                    {/* Product Details */}
                    <div className="flex-1 min-w-0 pr-2">
                      <div className="font-medium text-sm truncate mb-1">{product.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {product.discounted_price ? (
                          <div className="flex items-center gap-1 flex-wrap">
                            <span className="text-primary font-medium">
                              {formatPrice(product.discounted_price)}
                            </span>
                            <span className="line-through text-xs">
                              {formatPrice(product.base_price)}
                            </span>
                          </div>
                        ) : (
                          <span className="font-medium">
                            {formatPrice(product.base_price)}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Check Icon */}
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        selectedProductIds.includes(product.id)
                          ? "opacity-100 text-primary"
                          : "opacity-0"
                      )}
                    />
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Selected products with drag and drop */}
      <div className="space-y-3">
        {/* Show loading state when loading selected products */}
        {isLoading && selectedProductIds.length > 0 && selectedProducts.length === 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium text-foreground">
              <Package className="h-4 w-4" />
              <span>Loading Selected Products...</span>
            </div>
            <div className="flex justify-center py-6">
              <div className="flex flex-col items-center gap-2">
                <Loader2 className="h-6 w-6 animate-spin text-[var(--brand-gold)]" />
                <span className="text-xs text-muted-foreground">Fetching product details...</span>
              </div>
            </div>
          </div>
        )}

        {/* Show selected products when loaded */}
        {selectedProducts.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium text-foreground">
              <Package className="h-4 w-4" />
              <span>Selected Products</span>
              <span className="text-xs text-muted-foreground font-normal">
                (Drag to reorder)
              </span>
            </div>
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={selectedProducts.map(p => p.id)}
                strategy={verticalListSortingStrategy}
              >
                <div className="grid gap-2">
                  {selectedProducts.map((product) => (
                    <SortableProductItem
                      key={product.id}
                      product={product}
                      onRemove={removeProduct}
                      formatPrice={formatPrice}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          </div>
        )}

        {/* Product limit indicator */}
        <div className="flex justify-between items-center text-xs">
          <span className="text-muted-foreground">
            {selectedProductIds.length === 0
              ? "No products selected"
              : `${selectedProductIds.length} product${selectedProductIds.length !== 1 ? 's' : ''} selected`}
          </span>
          <div className="flex items-center gap-1">
            <span className={cn(
              "font-medium",
              selectedProductIds.length >= 5 ? "text-destructive" : "text-muted-foreground"
            )}>
              {selectedProductIds.length}/5
            </span>
            <span className="text-muted-foreground">max</span>
          </div>
        </div>

        {/* Warning message when limit is reached */}
        {selectedProductIds.length >= 5 && (
          <div className="flex items-center gap-2 p-2 bg-destructive/10 text-destructive rounded-md">
            <Package className="h-4 w-4 shrink-0" />
            <span className="text-xs font-medium">
              Maximum limit of 5 products reached
            </span>
          </div>
        )}
      </div>
    </div>
  );
}
