/**
 * Rate Limiting Utility
 * 
 * Simple in-memory rate limiting for API routes.
 * In production, consider using Redis or a dedicated rate limiting service.
 */

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

class InMemoryRateLimit {
  private store = new Map<string, RateLimitEntry>();
  private windowMs: number;
  private maxRequests: number;

  constructor(windowMs: number = 60000, maxRequests: number = 100) {
    this.windowMs = windowMs;
    this.maxRequests = maxRequests;
    
    // Clean up expired entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  async limit(identifier: string) {
    const now = Date.now();
    const resetTime = now + this.windowMs;
    
    const entry = this.store.get(identifier);
    
    if (!entry || now > entry.resetTime) {
      // First request or window expired
      this.store.set(identifier, { count: 1, resetTime });
      return {
        success: true,
        limit: this.maxRequests,
        remaining: this.maxRequests - 1,
        reset: resetTime,
      };
    }
    
    if (entry.count >= this.maxRequests) {
      // Rate limit exceeded
      return {
        success: false,
        limit: this.maxRequests,
        remaining: 0,
        reset: entry.resetTime,
      };
    }
    
    // Increment count
    entry.count++;
    this.store.set(identifier, entry);
    
    return {
      success: true,
      limit: this.maxRequests,
      remaining: this.maxRequests - entry.count,
      reset: entry.resetTime,
    };
  }

  private cleanup() {
    const now = Date.now();
    for (const [key, entry] of this.store.entries()) {
      if (now > entry.resetTime) {
        this.store.delete(key);
      }
    }
  }
}

// Create rate limiter instance
// 100 requests per minute per identifier
export const ratelimit = new InMemoryRateLimit(60000, 100);

// Stricter rate limiter for sensitive operations
export const strictRateLimit = new InMemoryRateLimit(60000, 20);
