"use client";

import { Store, Package, RefreshCw } from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";

interface ViewToggleProps {
  viewType: "cards" | "products";
  onViewChange: (_view: "cards" | "products") => void;
  disabled?: boolean;
  hasFilters?: boolean;
}

export default function ViewToggle({
  viewType,
  onViewChange,
  disabled = false,
  hasFilters = false,
}: ViewToggleProps) {
  // Function to reset all filters and show all businesses/products
  const handleResetFilters = () => {
    // Use window.location.href to force a full page reload
    window.location.href = window.location.pathname;
  };

  return (
    <div className="flex flex-col items-center gap-4">
      <motion.div
        className="relative bg-white/90 dark:bg-neutral-800/90 p-1 sm:p-1.5 rounded-xl border border-neutral-200/70 dark:border-neutral-700/70 flex gap-1 backdrop-blur-md overflow-hidden shadow-sm"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
        whileHover={{ boxShadow: "0 8px 30px rgba(0, 0, 0, 0.06)" }}
      >
        {/* Decorative elements */}
        <div className="absolute -top-6 -right-6 w-12 h-12 bg-[var(--brand-gold)]/10 blur-xl rounded-full"></div>
        <div className="absolute -bottom-6 -left-6 w-12 h-12 bg-purple-500/10 blur-xl rounded-full"></div>

        {/* Products Button - First */}
        <motion.div
          whileHover={!disabled ? { scale: 1.05 } : undefined}
          whileTap={!disabled ? { scale: 0.95 } : undefined}
          className="relative z-10"
        >
          <Button
            variant="ghost"
            onClick={() => onViewChange("products")}
            disabled={disabled}
            className={`
              relative px-3 sm:px-4 py-1 sm:py-1.5 h-8 sm:h-9 rounded-lg transition-all duration-300
              ${
                viewType === "products"
                  ? "text-black dark:text-white font-medium"
                  : "text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300"
              }
              ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
            `}
          >
            <div className="flex items-center">
              <div className="relative">
                <Package className="mr-1 sm:mr-1.5 h-3.5 w-3.5 sm:h-4 sm:w-4" />
              </div>
              <span className="text-xs sm:text-xs md:text-sm font-medium">Products</span>
            </div>

            {/* Active indicator */}
            {viewType === "products" && (
              <motion.div
                layoutId="activeViewIndicator"
                className="absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/20 dark:from-[var(--brand-gold)]/15 dark:to-[var(--brand-gold)]/25 border border-[var(--brand-gold)]/30 rounded-lg -z-10 shadow-inner"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              />
            )}
          </Button>
        </motion.div>

        {/* Digital Cards Button - Second */}
        <motion.div
          whileHover={!disabled ? { scale: 1.05 } : undefined}
          whileTap={!disabled ? { scale: 0.95 } : undefined}
          className="relative z-10"
        >
          <Button
            variant="ghost"
            onClick={() => onViewChange("cards")}
            disabled={disabled}
            className={`
              relative px-3 sm:px-4 py-1 sm:py-1.5 h-8 sm:h-9 rounded-lg transition-all duration-300
              ${
                viewType === "cards"
                  ? "text-black dark:text-white font-medium"
                  : "text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300"
              }
              ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
            `}
          >
            <div className="flex items-center">
              <div className="relative">
                <Store className="mr-1 sm:mr-1.5 h-3.5 w-3.5 sm:h-4 sm:w-4" />
              </div>
              <span className="text-xs sm:text-xs md:text-sm font-medium">
                Digital Cards
              </span>
            </div>

            {/* Active indicator */}
            {viewType === "cards" && (
              <motion.div
                layoutId="activeViewIndicator"
                className="absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/20 dark:from-[var(--brand-gold)]/15 dark:to-[var(--brand-gold)]/25 border border-[var(--brand-gold)]/30 rounded-xl -z-10"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              />
            )}
          </Button>
        </motion.div>
      </motion.div>

      {/* Reset Filters Button - Only show when filters are applied */}
      {hasFilters && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button
            onClick={handleResetFilters}
            variant="outline"
            size="sm"
            className="flex items-center gap-1 sm:gap-2 bg-white dark:bg-neutral-800 border-[var(--brand-gold)]/30 dark:border-[var(--brand-gold)]/30 hover:bg-[var(--brand-gold)]/10 dark:hover:bg-[var(--brand-gold)]/20 text-neutral-700 dark:text-neutral-300 text-xs sm:text-sm shadow-sm transition-all duration-300"
          >
            <RefreshCw className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-[var(--brand-gold)]" />
            <span>Reset Filters</span>
          </Button>
        </motion.div>
      )}
    </div>
  );
}
