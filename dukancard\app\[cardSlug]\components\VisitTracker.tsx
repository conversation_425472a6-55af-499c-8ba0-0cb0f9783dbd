"use client";

/**
 * VisitTracker Component
 *
 * This component tracks visits to public business cards. It's designed to:
 * 1. Track unique visitors using a persistent identifier stored in localStorage
 * 2. Only track visits for cards with "online" status
 * 3. Track both authenticated and non-authenticated users
 * 4. Prevent duplicate visit records within a short time period (1 hour)
 *
 * How it works:
 *
 * 1. When a user visits a card page, this component:
 *    - Checks if the card status is "online" (ignores offline cards)
 *    - Generates or retrieves a unique visitor identifier from localStorage
 *    - Checks if the user has visited this card recently (within the last hour)
 *    - If not, records the visit via the recordCardVisit server action
 *
 * 2. The recordCardVisit action:
 *    - Double-checks that the card status is "online"
 *    - Inserts a record into the card_visits table
 *    - This triggers the update_visit_counts() database function
 *    - Which updates the pre-aggregated metrics in the business_profiles table
 *
 * 3. Database Implementation:
 *    - card_visits table: Stores individual visit records with visited_at timestamp in UTC
 *    - business_profiles table: Stores pre-aggregated metrics
 *    - update_visit_counts() function: Updates metrics when a new visit is recorded
 *      This function uses the most recent visit date in IST as "today" and converts all
 *      timestamps to IST using 'visited_at AT TIME ZONE 'Asia/Kolkata''
 *    - handle_new_visit trigger: Calls update_visit_counts() on new visit records
 *    - reset_all_visit_counts() function: Resets daily counts at midnight IST
 *    - clean_old_card_visits() function: Deletes visit records older than 31 days
 *    - Cron jobs:
 *      - reset-visit-counts: Runs reset_all_visit_counts() at midnight IST (6:30 PM UTC)
 *      - clean-card-visits: Runs clean_old_card_visits() at 1 AM IST (7:30 PM UTC)
 *
 * This approach ensures:
 * - Accurate tracking of unique visitors
 * - Efficient data retrieval for analytics
 * - Real-time updates to the UI
 * - Proper handling of timezone differences (using IST)
 * - Consistent metrics regardless of when database functions run
 */

import { useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { recordCardVisit } from "../actions";

interface VisitTrackerProps {
  businessProfileId: string;
  status: string;
}

export default function VisitTracker({
  businessProfileId,
  status,
}: VisitTrackerProps) {
  const [hasRecorded, setHasRecorded] = useState(false);

  useEffect(() => {
    // Only track visits for cards with "online" status
    if (status !== "online" || hasRecorded) {
      return;
    }

    const trackVisit = async () => {
      try {
        // Get or create a visitor identifier
        let visitorId = localStorage.getItem("dukancard_visitor_id");

        if (!visitorId) {
          visitorId = uuidv4();
          localStorage.setItem("dukancard_visitor_id", visitorId);
        }

        // Record the visit
        const result = await recordCardVisit({
          businessProfileId,
          visitorIdentifier: visitorId,
        });

        if (result.success) {
          setHasRecorded(true);

          // Store the last visit timestamp to prevent recording multiple visits in a short period
          const now = new Date().getTime();
          localStorage.setItem(`dukancard_last_visit_${businessProfileId}`, now.toString());
        } else {
          console.error("Failed to record visit:", result.error);
        }
      } catch (error) {
        console.error("Error recording visit:", error);
      }
    };

    // Check if we've visited this card recently (within the last hour)
    const lastVisitStr = localStorage.getItem(`dukancard_last_visit_${businessProfileId}`);
    if (lastVisitStr) {
      const lastVisit = parseInt(lastVisitStr, 10);
      const now = new Date().getTime();
      const oneHour = 60 * 60 * 1000;

      // If it's been less than an hour since the last visit, don't record a new visit
      if (now - lastVisit < oneHour) {
        setHasRecorded(true);
        return;
      }
    }

    // Track the visit
    trackVisit();
  }, [businessProfileId, status, hasRecorded]);

  // This component doesn't render anything
  return null;
}
