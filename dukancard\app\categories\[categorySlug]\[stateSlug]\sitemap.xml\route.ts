import { getCategoryBySlug } from "@/lib/config/categories";
import { NextRequest, NextResponse } from "next/server";
import { MetadataRoute } from "next";
import { INDIAN_STATE_DATA } from "@/lib/config/states";
import { createSitemapClient } from "@/utils/supabase/sitemap";
import { unstable_cache } from "next/cache";
import { CACHE_DURATION_SECONDS, SITEMAP_CONFIG } from "@/app/categories/config";

/**
 * Route handler for state-specific category sitemaps
 * This generates a sitemap for a specific category in a specific state
 * including links to all cities in that state
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ categorySlug: string; stateSlug: string }> }
): Promise<NextResponse> {
  // Temporarily disabled to reduce crawler load on database
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${siteUrl}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>`;

  return new NextResponse(xml, {
    headers: {
      "Content-Type": "application/xml",
      "Cache-Control": "public, max-age=86400", // Cache for 24 hours
    },
  });

  const paramsData = await params;
  const { categorySlug, stateSlug } = paramsData;

  try {
    // Get the category by slug
    const category = getCategoryBySlug(categorySlug);

    // If category doesn't exist, return 404
    if (!category) {
      console.warn(`Category not found for slug: ${categorySlug}`);
      return new NextResponse("Category not found", { status: 404 });
    }

    // Find the matching state using the state_slug directly
    const stateData = INDIAN_STATE_DATA.find(state => state.slug === stateSlug);
    const matchingState = stateData?.name;

    // If state doesn't exist, return 404
    if (!matchingState) {
      console.warn(`State not found for slug: ${stateSlug}`);
      return new NextResponse("State not found", { status: 404 });
    }

    // Initialize sitemap with the main state-category page
    const currentDate = new Date();
    const sitemap: MetadataRoute.Sitemap = [
      {
        url: `${siteUrl}/categories/${categorySlug}/${stateSlug}`,
        lastModified: currentDate,
        changeFrequency: SITEMAP_CONFIG.defaultChangeFrequency,
        priority: SITEMAP_CONFIG.priority.statePage,
      }
    ];

    // Define a function to fetch cities with caching
    const getCitiesForState = unstable_cache(
      async (stateName: string, _categoryName: string) => {
        const supabaseClient = createSitemapClient();

        try {
          console.log(`Fetching distinct cities for state ${stateName}...`);

          // Use proper pagination to handle Supabase's 1000 row limit
          const allCities: string[] = [];
          let page = 0;
          let hasMore = true;
          const PAGE_SIZE = 1000; // Supabase's maximum limit

          while (hasMore) {
            const from = page * PAGE_SIZE;
            const to = from + PAGE_SIZE - 1;

            console.log(`Fetching cities page ${page + 1} (range: ${from}-${to})...`);

            const { data, error } = await supabaseClient
              .from("pincodes")
              .select("DivisionName")
              .eq("StateName", stateName)
              .range(from, to);

            if (error) {
              console.error(`Error fetching cities page ${page + 1}:`, error);
              break;
            }

            if (!data || data.length === 0) {
              hasMore = false;
            } else {
              // Extract city names
              const cityNames = data.map((row: { DivisionName: string }) => row.DivisionName);
              allCities.push(...cityNames);
              page++;

              // If we received fewer records than the page size, we've reached the end
              if (data.length < PAGE_SIZE) {
                hasMore = false;
              }
            }
          }

          if (allCities.length === 0) {
            console.warn(`No cities found for state ${stateName}`);
            return [];
          }

          // Deduplicate city names
          const uniqueCities = [...new Set(allCities)];

          console.log(`Found ${uniqueCities.length} unique cities in ${stateName}`);
          return uniqueCities;
        } catch (error) {
          console.error(`Unexpected error fetching cities for state ${stateName}:`, error);
          return [];
        }
      },
      ["cities-by-state-category"], // Cache key prefix
      {
        revalidate: CACHE_DURATION_SECONDS, // Revalidate based on central config
        tags: [`state-cities-${matchingState}-category-${category?.name || 'unknown'}`], // Cache tag for this specific state and category
      }
    );

    // Fetch cities with their slugs using the cached function
    if (!category) {
      console.warn(`Category is null for categorySlug: ${categorySlug}`);
      return new NextResponse("Category not found", { status: 404 });
    }

    // Ensure matchingState is defined before proceeding
    if (!matchingState) {
      console.warn(`State is null for stateSlug: ${stateSlug}`);
      return new NextResponse("State not found", { status: 404 });
    }

    const cities = await getCitiesForState(matchingState as string, category!.name);

    // Add city-specific URLs to the sitemap
    for (const city of cities) {
      // Get the city slug from the database
      const supabaseClient = createSitemapClient();
      const { data: cityData } = await supabaseClient
        .from("pincodes")
        .select("city_slug")
        .eq("StateName", matchingState)
        .eq("DivisionName", city)
        .limit(1);

      if (!cityData || (cityData as any[]).length === 0) {
        console.warn(`No city slug found for "${city}" in ${matchingState}`);
        continue;
      }

      const citySlug = (cityData as any[])[0]?.city_slug;
      if (!citySlug) {
        console.warn(`City slug is null for "${city}" in ${matchingState}`);
        continue;
      }

      // Add direct link to city page
      sitemap.push({
        url: `${siteUrl}/categories/${categorySlug}/${stateSlug}/${citySlug}`,
        lastModified: currentDate,
        changeFrequency: SITEMAP_CONFIG.defaultChangeFrequency,
        priority: SITEMAP_CONFIG.priority.cityPage,
      });

      // Add link to city sitemap XML
      sitemap.push({
        url: `${siteUrl}/categories/${categorySlug}/${stateSlug}/${citySlug}/sitemap.xml`,
        lastModified: currentDate,
        changeFrequency: SITEMAP_CONFIG.defaultChangeFrequency,
        priority: SITEMAP_CONFIG.priority.sitemapFile
      });
    }

    // Convert the sitemap to XML
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${sitemap.map((entry) => {
    // Format the date properly, handling both string and Date types
    const lastMod = entry.lastModified
      ? (entry.lastModified instanceof Date
          ? entry.lastModified.toISOString()
          : entry.lastModified)
      : currentDate.toISOString();

    return `
  <url>
    <loc>${entry.url}</loc>
    <lastmod>${lastMod}</lastmod>
    <changefreq>${entry.changeFrequency || SITEMAP_CONFIG.defaultChangeFrequency}</changefreq>
    <priority>${entry.priority || SITEMAP_CONFIG.priority.statePage}</priority>
  </url>
  `;
  }).join('')}
</urlset>`;

    // Return the XML with the correct content type
    return new NextResponse(xml, {
      headers: {
        'Content-Type': 'application/xml',
      },
    });
  } catch (error: unknown) {
    // Provide more detailed error information
    const errorMessage = error instanceof Error ? (error as Error).message : 'Unknown error occurred';
    console.error(`Error generating sitemap for category ${categorySlug} and state ${stateSlug}:`, errorMessage);

    // Return a more informative error response
    return new NextResponse(
      `Error generating sitemap: ${errorMessage}. Please try again later or contact support if the issue persists.`,
      {
        status: 500,
        headers: {
          'Content-Type': 'text/plain',
        },
      }
    );
  }
}

// Use ISR with a long revalidation period (60 days = 5184000 seconds)
// This allows the page to be cached but still refreshed periodically
export const revalidate = 5184000; // 60 days in seconds

// Force dynamic rendering to prevent build-time generation
export const dynamic = 'force-dynamic';

// Note: We're using ISR with a long cache duration
// This allows the sitemap to be generated on first request and then cached
// It will be regenerated after the cache period expires (60 days)
