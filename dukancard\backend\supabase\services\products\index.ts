// Re-export all types
export * from './types';

// Re-export all schemas
export * from './schemas';

// Re-export all functions
export { getProductServices } from './getProducts';
export { addProductService } from './addProduct';
export { updateProductService } from './updateProduct';
export { deleteProductService } from './deleteProduct';
export { handleMultipleImageUpload } from './imageHandlers';

// Variant-related functions
export { addProductVariant, addMultipleVariants } from './addVariant';
export { updateProductVariant, updateMultipleVariants } from './updateVariant';
export { deleteProductVariant, deleteMultipleVariants, deleteAllProductVariants } from './deleteVariant';
export { getProductWithVariants } from './getProductWithVariants';