import { createAdminClient } from '@/utils/supabase/admin';

export interface BusinessDiscoveryData {
  id: string;
  business_name: string;
  business_slug: string;
  logo_url?: string;
  member_name?: string;
  title?: string;
  address_line?: string;
  city?: string;
  state?: string;
  pincode?: string;
  locality?: string;
  phone?: string;
  instagram_url?: string;
  facebook_url?: string;
  whatsapp_number?: string;
  about_bio?: string;
  status: string;
  business_category?: string;
  business_hours?: any;
  delivery_info?: string;
  established_year?: number;
  google_maps_url?: string;
  theme_color?: string;
  total_likes?: number;
  total_subscriptions?: number;
  average_rating?: number;
  created_at: string;
  updated_at: string;
  custom_ads?: any;
  user_plan?: string;
}

export interface BusinessDiscoveryResult {
  success: boolean;
  data?: BusinessDiscoveryData;
  error?: string;
}

/**
 * Discovers and fetches business data by slug for QR code scanning
 * @param businessSlug - The business slug from QR code
 * @returns Business discovery result
 */
export async function discoverBusinessBySlug(businessSlug: string): Promise<BusinessDiscoveryResult> {
  if (!businessSlug || typeof businessSlug !== 'string') {
    return {
      success: false,
      error: 'Invalid business slug'
    };
  }

  const cleanSlug = businessSlug.trim().toLowerCase();

  if (!cleanSlug) {
    return {
      success: false,
      error: 'Empty business slug'
    };
  }

  try {
    // Fetch business profile with all necessary data for display using admin client
    const supabaseAdmin = createAdminClient();
    const businessClient = supabaseAdmin.from('business_profiles');
    const { data: businessProfile, error } = await businessClient
      .select(`
        id,
        business_name,
        business_slug,
        logo_url,
        member_name,
        title,
        address_line,
        city,
        state,
        pincode,
        locality,
        phone,
        instagram_url,
        facebook_url,
        whatsapp_number,
        about_bio,
        status,
        business_category,
        business_hours,
        delivery_info,
        established_year,
        google_maps_url,
        theme_color,
        total_likes,
        total_subscriptions,
        average_rating,
        created_at,
        updated_at,
        custom_ads
      `)
      .eq('business_slug', cleanSlug)
      .eq('status', 'online')
      .maybeSingle();

    if (error) {
      console.error('Error discovering business:', error);
      return {
        success: false,
        error: 'Failed to load business information'
      };
    }

    if (!businessProfile) {
      return {
        success: false,
        error: 'Business not found or is currently offline'
      };
    }

    // Fetch subscription data to get plan information
    const subscriptionClient = supabaseAdmin.from('payment_subscriptions');
    const { data: subscriptionData, error: subscriptionError } = await subscriptionClient
      .select('plan_id, subscription_status')
      .eq('business_profile_id', businessProfile.id)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (subscriptionError) {
      console.error('Error fetching subscription data:', subscriptionError);
      // Continue without subscription data
    }

    // Combine business profile with subscription data
    const businessWithPlan = {
      ...businessProfile,
      user_plan: subscriptionData?.plan_id || 'free' // Default to free if no subscription
    };

    return {
      success: true,
      data: businessWithPlan
    };
  } catch (error) {
    console.error('Exception discovering business:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Gets basic business information for quick preview
 * @param businessSlug - The business slug
 * @returns Basic business info or null
 */
export async function getBusinessPreview(businessSlug: string): Promise<{
  business_name: string;
  logo_url?: string;
  member_name?: string;
  city?: string;
  state?: string;
} | null> {
  try {
    const supabaseAdmin = createAdminClient();
    const businessInfoClient = supabaseAdmin.from('business_profiles');
    const { data, error } = await businessInfoClient
      .select('business_name, logo_url, member_name, city, state')
      .eq('business_slug', businessSlug)
      .eq('status', 'online')
      .maybeSingle();

    if (error || !data) {
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error getting business preview:', error);
    return null;
  }
}

/**
 * Checks if business is accessible and returns basic status
 * @param businessSlug - The business slug
 * @returns Business accessibility status
 */
export async function checkBusinessAccessibility(businessSlug: string): Promise<{
  accessible: boolean;
  exists: boolean;
  isOnline: boolean;
  businessName?: string;
}> {
  try {
    const supabaseAdmin = createAdminClient();
    const businessStatusClient = supabaseAdmin.from('business_profiles');
    const { data, error } = await businessStatusClient
      .select('business_name, status')
      .eq('business_slug', businessSlug)
      .maybeSingle();

    if (error) {
      return {
        accessible: false,
        exists: false,
        isOnline: false
      };
    }

    if (!data) {
      return {
        accessible: false,
        exists: false,
        isOnline: false
      };
    }

    const isOnline = data.status === 'online';

    return {
      accessible: isOnline,
      exists: true,
      isOnline,
      businessName: data.business_name
    };
  } catch (error) {
    console.error('Error checking business accessibility:', error);
    return {
      accessible: false,
      exists: false,
      isOnline: false
    };
  }
}

/**
 * Discovers business with user-friendly error messages
 * @param businessSlug - The business slug from QR code
 * @returns User-friendly discovery result
 */
export async function discoverBusinessForUser(businessSlug: string): Promise<BusinessDiscoveryResult> {
  const result = await discoverBusinessBySlug(businessSlug);
  
  if (!result.success && result.error) {
    // Convert technical errors to user-friendly messages
    const userFriendlyErrors: Record<string, string> = {
      'Invalid business slug': 'Invalid business URL.',
      'Empty business slug': 'Business URL is empty.',
      'Business not found or is currently offline': 'This business is not available right now. It may be temporarily offline or the QR code may be outdated.',
      'Failed to load business information': 'Unable to load business information. Please check your internet connection and try again.',
      'An unexpected error occurred': 'Something went wrong. Please try again.'
    };

    const userFriendlyError = userFriendlyErrors[result.error] || result.error;
    
    return {
      ...result,
      error: userFriendlyError
    };
  }

  return result;
}
