import { createClient } from '@/utils/supabase/client';
import { CustomerPostFormData } from '@/lib/types/posts';

export interface ActionResponse {
  success: boolean;
  message: string;
  error?: string;
  data?: unknown;
}

/**
 * Create a new customer post
 */
export async function createCustomerPost(formData: CustomerPostFormData): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: 'Authentication required',
      error: 'You must be logged in to create a post'
    };
  }

  // Get the user's customer profile
  const { data: customerProfile, error: profileError } = await supabase
    .from('customer_profiles')
    .select('id, city_slug, state_slug, locality_slug, pincode')
    .eq('id', user.id)
    .single();

  if (profileError || !customerProfile) {
    return {
      success: false,
      message: 'Customer profile not found',
      error: 'You must have a customer profile to create a post'
    };
  }

  // Prepare post data
  const postData = {
    customer_id: user.id,
    content: formData.content,
    image_url: formData.image_url || null,
    city_slug: customerProfile.city_slug,
    state_slug: customerProfile.state_slug,
    locality_slug: customerProfile.locality_slug,
    pincode: customerProfile.pincode,
    mentioned_business_ids: formData.mentioned_business_ids || []
  };

  // Insert the post
  const { data, error } = await supabase
    .from('customer_posts')
    .insert(postData)
    .select()
    .single();

  if (error) {
    console.error('Error creating customer post:', error);
    return {
      success: false,
      message: 'Failed to create post',
      error: error.message
    };
  }

  return {
    success: true,
    message: 'Post created successfully',
    data
  };
}

/**
 * Update an existing customer post
 */
export async function updateCustomerPost(postId: string, formData: CustomerPostFormData): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: 'Authentication required',
      error: 'You must be logged in to update a post'
    };
  }

  // Check if the post exists and belongs to the user
  const { data: existingPost, error: postError } = await supabase
    .from('customer_posts')
    .select('id')
    .eq('id', postId)
    .eq('customer_id', user.id)
    .single();

  if (postError || !existingPost) {
    return {
      success: false,
      message: 'Post not found',
      error: 'The post does not exist or you do not have permission to update it'
    };
  }

  // Prepare update data
  const updateData = {
    content: formData.content,
    image_url: formData.image_url || null,
    mentioned_business_ids: formData.mentioned_business_ids || [],
    updated_at: new Date().toISOString()
  };

  // Update the post
  const { data, error } = await supabase
    .from('customer_posts')
    .update(updateData)
    .eq('id', postId)
    .select()
    .single();

  if (error) {
    console.error('Error updating customer post:', error);
    return {
      success: false,
      message: 'Failed to update post',
      error: error.message
    };
  }

  return {
    success: true,
    message: 'Post updated successfully',
    data
  };
}

/**
 * Delete a customer post
 */
export async function deleteCustomerPost(postId: string): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: 'Authentication required',
      error: 'You must be logged in to delete a post'
    };
  }

  // Check if the post exists and belongs to the user
  const { data: existingPost, error: postError } = await supabase
    .from('customer_posts')
    .select('id')
    .eq('id', postId)
    .eq('customer_id', user.id)
    .single();

  if (postError || !existingPost) {
    return {
      success: false,
      message: 'Post not found',
      error: 'The post does not exist or you do not have permission to delete it'
    };
  }

  // Delete the post
  const { error } = await supabase
    .from('customer_posts')
    .delete()
    .eq('id', postId);

  if (error) {
    console.error('Error deleting customer post:', error);
    return {
      success: false,
      message: 'Failed to delete post',
      error: error.message
    };
  }

  return {
    success: true,
    message: 'Post deleted successfully'
  };
}

/**
 * Get customer posts for a specific customer
 */
export async function getCustomerPosts(customerId?: string, page: number = 1, limit: number = 10): Promise<ActionResponse> {
  const supabase = await createClient();

  let query = supabase
    .from('customer_posts')
    .select(`
      *,
      customer_profiles (
        id,
        name,
        avatar_url,
        city,
        state,
        locality
      )
    `)
    .order('created_at', { ascending: false });

  // If customerId is provided, filter by it
  if (customerId) {
    query = query.eq('customer_id', customerId);
  }

  // Add pagination
  const from = (page - 1) * limit;
  const to = from + limit - 1;
  query = query.range(from, to);

  const { data, error, count } = await query;

  if (error) {
    console.error('Error fetching customer posts:', error);
    return {
      success: false,
      message: 'Failed to fetch posts',
      error: error.message
    };
  }

  return {
    success: true,
    message: 'Posts fetched successfully',
    data: {
      items: data || [],
      totalCount: count || 0,
      hasMore: data ? data.length === limit : false
    }
  };
}

/**
 * Get a single customer post by ID
 */
export async function getCustomerPost(postId: string): Promise<ActionResponse> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('customer_posts')
    .select(`
      *,
      customer_profiles (
        id,
        name,
        avatar_url,
        city,
        state,
        locality
      )
    `)
    .eq('id', postId)
    .single();

  if (error) {
    console.error('Error fetching customer post:', error);
    return {
      success: false,
      message: 'Failed to fetch post',
      error: error.message
    };
  }

  return {
    success: true,
    message: 'Post fetched successfully',
    data
  };
}
