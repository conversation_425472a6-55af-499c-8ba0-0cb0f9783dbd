/**
 * Shared utility to determine the correct post-login redirect path for a user.
 * Checks both customer_profiles and business_profiles, and returns the appropriate dashboard, onboarding, or choose-role path.
 * Returns "/" as a fallback in case of errors.
 */

import { SupabaseClient } from "@supabase/supabase-js";

export async function getPostLoginRedirectPath(
  supabase: SupabaseClient,
  userId: string
): Promise<string> {
  try {
    // Check both profiles concurrently
    const [customerRes, businessRes] = await Promise.all([
      supabase
        .from("customer_profiles")
        .select("id")
        .eq("id", userId),
      supabase
        .from("business_profiles")
        .select("id, business_slug")
        .eq("id", userId),
    ]);

    if (customerRes.error || businessRes.error) {
      // Query error, fallback to choose-role for new users, only fallback to "/" for critical errors
      console.error("[redirectAfterLogin] Supabase query error:", customerRes.error, businessRes.error);
      // If both errors are "no rows found" or similar, treat as new user
      if (
        customerRes.error?.code === "PGRST116" || // PostgREST "No rows found"
        businessRes.error?.code === "PGRST116" ||
        customerRes.error?.message?.toLowerCase().includes("no rows") ||
        businessRes.error?.message?.toLowerCase().includes("no rows")
      ) {
        return "/choose-role";
      }
      return "/?view=home";
    }

    if (customerRes.data && Array.isArray(customerRes.data) && customerRes.data.length > 0) {
      // Customer profile exists
      return "/dashboard/customer";
    }

    if (businessRes.data && Array.isArray(businessRes.data) && businessRes.data.length > 0) {
      // Business profile exists
      const businessProfile = businessRes.data[0];
      if (businessProfile.business_slug) {
        // Onboarding complete
        return "/dashboard/business";
      } else {
        // Onboarding needed
        return "/onboarding";
      }
    }

    // No profile exists yet, needs role selection
    return "/choose-role";
  } catch (err) {
    console.error("[redirectAfterLogin] Unexpected error:", err);
    // Unexpected error, fallback to home
    return "/?view=home";
  }
}