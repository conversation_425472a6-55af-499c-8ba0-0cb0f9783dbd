import React, { useState, useRef } from 'react';
import { View, Text, TextInput, TouchableOpacity, Modal, FlatList } from 'react-native';
import { Search, X, SortAsc } from 'lucide-react-native';
import { AnimatedLoader } from '../ui/AnimatedLoader';
import { BusinessProduct, ProductSortOption } from '../../lib/services/businessCardDataService';
import { ProductCard } from '../shared/ui';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';
import { ProductGridSkeleton } from '../ui/ProductSkeleton';

interface ProductsTabProps {
  products: BusinessProduct[];
  businessId: string;
  isDark: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  searchQuery: string;
  sortBy: ProductSortOption;
  onLoadMore: () => void;
  onSearch: (query: string) => void;
  onSort: (sortBy: ProductSortOption) => void;
  useScrollView?: boolean; // New prop to use ScrollView instead of FlatList
  searchSortLoading?: boolean; // New prop for search/sort loading state
}

export default function ProductsTab({
  products,
  businessId,
  isDark,
  loadingMore,
  hasMore,
  searchQuery,
  sortBy,
  onLoadMore,
  onSearch,
  onSort,
  useScrollView = false,
  searchSortLoading = false,
}: ProductsTabProps) {
  const styles = createPublicCardViewStyles(isDark);
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [showSortModal, setShowSortModal] = useState(false);
  const searchTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Handle search input change with debouncing
  const handleSearchChange = (text: string) => {
    setLocalSearchQuery(text);

    // Clear existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set new timeout for debounced search
    searchTimeoutRef.current = setTimeout(() => {
      onSearch(text);
    }, 500); // 500ms debounce delay
  };

  // Handle clear search
  const handleClearSearch = () => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    setLocalSearchQuery('');
    onSearch('');
  };

  // Handle sort selection
  const handleSortSelect = (newSortBy: ProductSortOption) => {
    onSort(newSortBy);
    setShowSortModal(false);
  };

  // Get sort label
  const getSortLabel = (sort: ProductSortOption) => {
    switch (sort) {
      case 'newest': return 'Newest First';
      case 'price_low': return 'Price: Low to High';
      case 'price_high': return 'Price: High to Low';
      case 'name_asc': return 'Name: A to Z';
      case 'name_desc': return 'Name: Z to A';
      default: return 'Newest First';
    }
  };

  // Convert BusinessProduct to ProductData for compatibility
  const convertBusinessProductToProductData = (product: BusinessProduct) => {
    return {
      id: product.id,
      name: product.name,
      description: product.description || null,
      base_price: product.base_price || null,
      discounted_price: product.discounted_price || null,
      image_url: product.image_url || null,
      images: product.images || null,
      featured_image_index: product.featured_image_index || null,
      business_id: businessId,
      is_available: true,
      slug: product.slug || `product-${product.id}`,
    };
  };

  return (
    <View style={styles.section}>
      {/* Search and Sort Controls */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Search color={isDark ? '#9CA3AF' : '#6B7280'} size={20} style={styles.searchIcon} />
          <TextInput
            style={[styles.searchInput, { color: isDark ? '#FFFFFF' : '#000000' }]}
            placeholder="Search products..."
            placeholderTextColor={isDark ? '#9CA3AF' : '#6B7280'}
            value={localSearchQuery}
            onChangeText={handleSearchChange}
          />
          {localSearchQuery.length > 0 && (
            <TouchableOpacity onPress={handleClearSearch} style={styles.clearButton}>
              <X color={isDark ? '#9CA3AF' : '#6B7280'} size={20} />
            </TouchableOpacity>
          )}
        </View>

        {/* Sort Button */}
        <TouchableOpacity
          style={styles.sortButton}
          onPress={() => setShowSortModal(true)}
        >
          <SortAsc color={isDark ? '#D4AF37' : '#D4AF37'} size={20} />
          <Text style={[styles.sortButtonText, { color: isDark ? '#FFFFFF' : '#000000' }]}>
            Sort
          </Text>
        </TouchableOpacity>
      </View>

      {searchSortLoading ? (
        // Show skeleton loading during search/sort operations
        <ProductGridSkeleton />
      ) : products && products.length > 0 ? (
        useScrollView ? (
          // Use simple View for nested scrolling - parent ScrollView handles scroll
          <View style={styles.flatListContent}>
            <View style={styles.productsGrid}>
              {products.map((item) => (
                <View key={item.id} style={styles.productGridItem}>
                  <ProductCard
                    product={convertBusinessProductToProductData(item)}
                    isClickable={true}
                    variant="default"
                  />
                </View>
              ))}
            </View>
            {/* Infinite scroll trigger element */}
            {hasMore && (
              <View style={styles.infiniteScrollTrigger} />
            )}
            {loadingMore && hasMore && (
              <View style={styles.loadMoreContainer}>
                <AnimatedLoader size={24} color="#D4AF37" />
                <Text style={styles.loadMoreText}>Loading more products...</Text>
              </View>
            )}
          </View>
        ) : (
          // Use FlatList for standalone scrolling
          <FlatList
            data={products}
            keyExtractor={(item) => item.id}
            numColumns={2}
            columnWrapperStyle={styles.productRow}
            renderItem={({ item }) => (
              <View style={styles.productGridItem}>
                <ProductCard
                  product={convertBusinessProductToProductData(item)}
                  isClickable={true}
                  variant="default"
                />
              </View>
            )}
            onEndReached={() => {
              if (hasMore && !loadingMore) {
                onLoadMore();
              }
            }}
            onEndReachedThreshold={0.1}
            ListFooterComponent={() => {
              if (loadingMore && hasMore) {
                return (
                  <View style={styles.loadMoreContainer}>
                    <AnimatedLoader size={24} color="#D4AF37" />
                    <Text style={styles.loadMoreText}>Loading more products...</Text>
                  </View>
                );
              }
              return null;
            }}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.flatListContent}
          />
        )
      ) : (
        <Text style={styles.emptyText}>No products available</Text>
      )}

      {/* Sort Modal */}
      <Modal
        visible={showSortModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowSortModal(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowSortModal(false)}
        >
          <View style={styles.sortModal}>
            <Text style={styles.sortModalTitle}>Sort Products</Text>

            {/* Date Section */}
            <View style={styles.sortSection}>
              <Text style={styles.sortSectionTitle}>Date</Text>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'newest' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('newest')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'newest' && styles.sortOptionTextSelected]}>
                  Newest First
                </Text>
              </TouchableOpacity>
            </View>

            {/* Price Section */}
            <View style={styles.sortSection}>
              <Text style={styles.sortSectionTitle}>Price</Text>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'price_low' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('price_low')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'price_low' && styles.sortOptionTextSelected]}>
                  Price: Low to High
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'price_high' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('price_high')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'price_high' && styles.sortOptionTextSelected]}>
                  Price: High to Low
                </Text>
              </TouchableOpacity>
            </View>

            {/* Name Section */}
            <View style={styles.sortSection}>
              <Text style={styles.sortSectionTitle}>Name</Text>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'name_asc' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('name_asc')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'name_asc' && styles.sortOptionTextSelected]}>
                  Name: A to Z
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'name_desc' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('name_desc')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'name_desc' && styles.sortOptionTextSelected]}>
                  Name: Z to A
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}
