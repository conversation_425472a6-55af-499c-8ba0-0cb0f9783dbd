import { Metadata } from "next";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import { getProductServices } from "@/backend/supabase/services/products/getProducts";
import { getPlanLimit } from "@/lib/PricingPlans";
import ProductsPageClient from "./components/ProductsPageClient";

// Add metadata
export const metadata: Metadata = {
  title: "Manage Products",
  robots: "noindex, nofollow",
};

export default async function ProductsPage() {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect("/login"); // Redirect to login if not authenticated
  }

  // Fetch initial products data (first page)
  const initialProductsResult = await getProductServices(1, 10); // Fetch page 1, limit 10

  // Fetch user's plan from payment_subscriptions to pass the limit down
  const { data: subscriptionData, error: subscriptionError } = await supabase
    .from("payment_subscriptions")
    .select("plan_id")
    .eq("business_profile_id", user.id)
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (subscriptionError) {
    // Handle error fetching subscription - maybe show an error message?
    console.error("Error fetching subscription for plan limit:", subscriptionError);
    // For now, let's proceed but the limit might be wrong/default
  }

  const planId = subscriptionData?.plan_id || "free";
  const planLimit = getPlanLimit(planId);

  return (
    <ProductsPageClient
      initialData={initialProductsResult.data ?? []}
      initialCount={initialProductsResult.count ?? 0}
      planLimit={planLimit}
      error={initialProductsResult.error}
    />
  );
}
