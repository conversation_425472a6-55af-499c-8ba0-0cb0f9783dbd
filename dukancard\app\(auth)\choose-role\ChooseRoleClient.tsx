"use client";

import React, { useState, useTransition, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Loader2, User, Briefcase, ChevronRight } from "lucide-react";
import { toast } from "sonner";
import { createCustomerProfile } from "@/backend/supabase/services/auth/roleActions";

interface ChooseRoleClientProps {
  userId: string;
  redirectSlug?: string | null;
  message?: string | null;
}

export default function ChooseRoleClient({ userId, redirectSlug, message }: ChooseRoleClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  const [selectedRole, setSelectedRole] = useState<
    "customer" | "business" | null
  >(null);
  const [cardRedirect, setCardRedirect] = useState<string | null>(redirectSlug || null);
  const [messageParam, setMessageParam] = useState<string | null>(message || null);

  // Get the redirect and message parameters from the URL or localStorage if not passed as props
  useEffect(() => {
    if (!cardRedirect) {
      // First check URL parameters
      const redirect = searchParams.get('redirect');
      if (redirect) {
        setCardRedirect(redirect);
        console.log(`Got redirect from URL: ${redirect}`);
      } else if (typeof window !== 'undefined') {
        // Then check localStorage
        const storedRedirect = localStorage.getItem('chooseRoleRedirect');
        if (storedRedirect) {
          setCardRedirect(storedRedirect);
          console.log(`Got redirect from localStorage: ${storedRedirect}`);
          // Clear the stored redirect to prevent it from being used again
          localStorage.removeItem('chooseRoleRedirect');
        }
      }
    }

    if (!messageParam) {
      // First check URL parameters
      const message = searchParams.get('message');
      if (message) {
        setMessageParam(message);
        console.log(`Got message from URL: ${message}`);
      } else if (typeof window !== 'undefined') {
        // Then check localStorage
        const storedMessage = localStorage.getItem('chooseRoleMessage');
        if (storedMessage) {
          setMessageParam(storedMessage);
          console.log(`Got message from localStorage: ${storedMessage}`);
          // Clear the stored message to prevent it from being used again
          localStorage.removeItem('chooseRoleMessage');
        }
      }
    }
  }, [searchParams, cardRedirect, messageParam]);

  const handleCustomerClick = () => {
    setSelectedRole("customer");
    startTransition(async () => {
      const result = await createCustomerProfile(userId, cardRedirect, messageParam);
      if (result?.error) {
        toast.error(`Failed to set up account: ${result.error}`);
        setSelectedRole(null);
      }
      // On success, the action handles the redirect
    });
  };

  const handleBusinessClick = () => {
    setSelectedRole("business");
    // Pass the redirect and message parameters to the onboarding page if available
    let onboardingUrl = "/onboarding";
    const params = new URLSearchParams();

    if (cardRedirect) {
      params.append("redirect", cardRedirect);
    }

    if (messageParam) {
      params.append("message", messageParam);
    }

    if (params.toString()) {
      onboardingUrl += `?${params.toString()}`;
    }

    router.push(onboardingUrl);
  };

  const isLoading = isPending;
  // Card animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  // Option button animation variants
  const buttonVariants = {
    hover: {
      scale: 1.02,
      boxShadow: "0 10px 25px -5px rgba(212, 175, 55, 0.15)",
      transition: { type: "spring", stiffness: 300 }
    },
    tap: { scale: 0.98 }
  };

  return (
    <div className="w-full min-h-[calc(100vh-80px)] md:min-h-[calc(100vh-64px)] flex items-center justify-center bg-neutral-50 dark:bg-neutral-950 p-2 sm:p-4 pt-6 pb-20 md:pb-6">
      <motion.div
        initial="hidden"
        animate="visible"
        variants={cardVariants}
        className="w-full max-w-[90%] sm:max-w-md md:max-w-lg"
      >
        <Card className="overflow-hidden border dark:border-[#D4AF37]/30 border-[#D4AF37]/20 bg-white dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black shadow-lg rounded-xl">
            <CardHeader className="text-center relative pb-3 sm:pb-6 px-3 sm:px-6 pt-4 sm:pt-6">
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              <div className="flex justify-center items-center">
                <div className="flex flex-col items-center">
                  <div className="flex items-center mb-1 sm:mb-2">
                    <span className="font-bold text-base sm:text-lg md:text-xl text-[var(--brand-gold)]">
                      Dukan<span className="text-foreground dark:text-white">card</span>
                    </span>
                    <motion.span
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.8, duration: 0.5 }}
                    >
                      <span className="ml-1"></span>
                    </motion.span>
                  </div>
                  <CardTitle className="text-xl sm:text-2xl md:text-3xl font-bold text-neutral-800 dark:text-white">
                    Choose Your Role
                  </CardTitle>
                </div>
              </div>
            </motion.div>
            <CardDescription className="text-neutral-500 dark:text-neutral-400 pt-1 sm:pt-2 text-xs sm:text-sm md:text-base max-w-md mx-auto">
              Select how you&apos;ll be using our platform. This is a one-time setup that cannot be changed later.
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-3 sm:space-y-4 md:space-y-6 px-3 sm:px-6 md:px-8 pb-4 sm:pb-6 md:pb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              <motion.div
                variants={buttonVariants}
                whileHover="hover"
                whileTap="tap"
              >
                <div className="group">
                <Button
                  onClick={handleCustomerClick}
                  className="cursor-pointer w-full h-auto py-2 sm:py-3 md:py-6 px-2 sm:px-3 md:px-5 bg-neutral-50 hover:bg-neutral-100 dark:bg-neutral-800/70 dark:hover:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 hover:border-[#D4AF37]/50 dark:hover:border-[#D4AF37]/50 text-neutral-800 dark:text-white flex items-center justify-between rounded-lg sm:rounded-xl group-hover:shadow-md transition-all"
                  disabled={isLoading}
                >
                  <div className="flex items-center space-x-2 sm:space-x-3 md:space-x-4 max-w-[85%]">
                    <div className="bg-[#D4AF37]/10 dark:bg-[#D4AF37]/20 p-1.5 sm:p-2 md:p-3 rounded-md sm:rounded-lg flex-shrink-0">
                      {isLoading && selectedRole === "customer" ? (
                        <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4AF37] animate-spin" />
                      ) : (
                        <User className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4AF37]" />
                      )}
                    </div>
                    <div className="text-left min-w-0">
                      <span className="text-sm sm:text-base md:text-lg font-semibold block mb-0 sm:mb-0.5 md:mb-1 truncate">As a Customer</span>
                      <p className="text-xs sm:text-xs md:text-sm text-neutral-500 dark:text-neutral-400 line-clamp-2">
                        Browse and connect with businesses.
                      </p>
                    </div>
                  </div>
                  <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-neutral-400 dark:text-neutral-500 group-hover:text-[#D4AF37] transition-colors mr-1 sm:mr-1 md:mr-2 flex-shrink-0" />
                </Button>
                </div>
              </motion.div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.5 }}
            >
              <motion.div
                variants={buttonVariants}
                whileHover="hover"
                whileTap="tap"
              >
                <div className="group">
                <Button
                  onClick={handleBusinessClick}
                  className="cursor-pointer w-full h-auto py-2 sm:py-3 md:py-6 px-2 sm:px-3 md:px-5 bg-neutral-50 hover:bg-neutral-100 dark:bg-neutral-800/70 dark:hover:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 hover:border-[#D4AF37]/50 dark:hover:border-[#D4AF37]/50 text-neutral-800 dark:text-white flex items-center justify-between rounded-lg sm:rounded-xl group-hover:shadow-md transition-all"
                  disabled={isLoading}
                >
                  <div className="flex items-center space-x-2 sm:space-x-3 md:space-x-4 max-w-[85%]">
                    <div className="bg-[#D4AF37]/10 dark:bg-[#D4AF37]/20 p-1.5 sm:p-2 md:p-3 rounded-md sm:rounded-lg flex-shrink-0">
                      {isLoading && selectedRole === "business" ? (
                        <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4AF37] animate-spin" />
                      ) : (
                        <Briefcase className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#D4AF37]" />
                      )}
                    </div>
                    <div className="text-left min-w-0">
                      <span className="text-sm sm:text-base md:text-lg font-semibold block mb-0 sm:mb-0.5 md:mb-1 truncate">As a Business</span>
                      <p className="text-xs sm:text-xs md:text-sm text-neutral-500 dark:text-neutral-400 line-clamp-2">
                        Create your digital card and store.
                      </p>
                    </div>
                  </div>
                  <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-neutral-400 dark:text-neutral-500 group-hover:text-[#D4AF37] transition-colors mr-1 sm:mr-1 md:mr-2 flex-shrink-0" />
                </Button>
                </div>
              </motion.div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.7, duration: 0.5 }}
            >
              <div className="pt-1 sm:pt-2 md:pt-4 text-center">
                <p className="text-[10px] sm:text-xs md:text-sm text-red-500 dark:text-red-400 font-medium">
                  Note: This choice is permanent and cannot be changed later.
                </p>
              </div>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}