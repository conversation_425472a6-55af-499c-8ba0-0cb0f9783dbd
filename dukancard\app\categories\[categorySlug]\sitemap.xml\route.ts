import { getCategoryBySlug } from "@/lib/config/categories";
import { NextRequest, NextResponse } from "next/server";
import { MetadataRoute } from "next";
import { INDIAN_STATE_DATA } from "@/lib/config/states";
import { SITEMAP_CONFIG } from "../../config";

/**
 * Route handler for category-specific sitemaps
 * This approach uses a route handler instead of sitemap.ts to access dynamic params
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ categorySlug: string }> }
): Promise<NextResponse> {
  // Temporarily disabled to reduce crawler load on database
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${siteUrl}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>`;

  return new NextResponse(xml, {
    headers: {
      "Content-Type": "application/xml",
      "Cache-Control": "public, max-age=86400", // Cache for 24 hours
    },
  });

  const paramsData = await params;
  const { categorySlug } = paramsData;

  try {
    // Get the category by slug
    const category = getCategoryBySlug(categorySlug);

    // If category doesn't exist, return 404
    if (!category) {
      console.warn(`Category not found for slug: ${categorySlug}`);
      return new NextResponse("Category not found", { status: 404 });
    }

    // Create the sitemap entries
    const currentDate = new Date();

    // Start with the main category page
    const sitemap: MetadataRoute.Sitemap = [
      {
        url: `${siteUrl}/categories/${categorySlug}`,
        lastModified: currentDate,
        changeFrequency: SITEMAP_CONFIG.defaultChangeFrequency,
        priority: SITEMAP_CONFIG.priority.categoryPage,
      }
    ];

    // Add references to state-specific sitemaps
    for (const stateData of INDIAN_STATE_DATA) {
      // Use the state slug directly from the data
      const stateSlug = stateData.slug;

      sitemap.push({
        url: `${siteUrl}/categories/${categorySlug}/${stateSlug}/sitemap.xml`,
        lastModified: currentDate,
        changeFrequency: SITEMAP_CONFIG.defaultChangeFrequency,
        priority: SITEMAP_CONFIG.priority.sitemapFile,
      });
    }

    // Convert the sitemap to XML
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${sitemap.map((entry) => {
    // Format the date properly, handling both string and Date types
    const lastMod = entry.lastModified
      ? (entry.lastModified instanceof Date
          ? entry.lastModified.toISOString()
          : entry.lastModified)
      : currentDate.toISOString();

    return `
  <url>
    <loc>${entry.url}</loc>
    <lastmod>${lastMod}</lastmod>
    <changefreq>${entry.changeFrequency || SITEMAP_CONFIG.defaultChangeFrequency}</changefreq>
    <priority>${entry.priority || SITEMAP_CONFIG.priority.categoryPage}</priority>
  </url>
  `;
  }).join('')}
</urlset>`;

    // Return the XML with the correct content type
    return new NextResponse(xml, {
      headers: {
        'Content-Type': 'application/xml',
      },
    });
  } catch (error) {
    console.error("Error generating category sitemap:", error);
    return new NextResponse("Error generating sitemap", { status: 500 });
  }
}

// Use ISR with a long revalidation period (60 days = 5184000 seconds)
// This allows the page to be cached but still refreshed periodically
export const revalidate = 5184000; // 60 days in seconds

// Force dynamic rendering to prevent build-time generation
export const dynamic = 'force-dynamic';

// Note: We're using ISR with a long cache duration
// This allows the sitemap to be generated on first request and then cached
// It will be regenerated after the cache period expires (60 days)
