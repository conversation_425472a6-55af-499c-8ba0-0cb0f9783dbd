'use client';

import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';
import ModernPostCard from './ModernPostCard';
import { PostWithBusinessProfile } from '@/lib/types/posts';

interface UnifiedPostCardProps {
  post: UnifiedPost;
  index?: number;
  onPostUpdate?: (_postId: string, _newContent: string) => void;
  onPostDelete?: (_postId: string) => void;
  onProductsUpdate?: (_postId: string, _newProductIds: string[]) => void;
  showActualAspectRatio?: boolean;
  disablePostClick?: boolean;
  enableImageFullscreen?: boolean;
}

export default function UnifiedPostCard({
  post,
  index = 0,
  onPostUpdate,
  onPostDelete,
  onProductsUpdate,
  showActualAspectRatio = false,
  disablePostClick = false,
  enableImageFullscreen = false
}: UnifiedPostCardProps) {

  // Transform unified post to business post format for consistent rendering
  // Now using author_name and author_avatar directly from the unified_posts view
  const businessPost: PostWithBusinessProfile = {
    id: post.id,
    business_id: post.author_id,
    content: post.content,
    image_url: post.image_url,
    created_at: post.created_at,
    updated_at: post.updated_at,
    city_slug: post.city_slug,
    state_slug: post.state_slug,
    locality_slug: post.locality_slug,
    pincode: post.pincode,
    product_ids: post.product_ids,
    mentioned_business_ids: post.mentioned_business_ids,
    business_profiles: {
      id: post.author_id,
      business_name: post.author_name || (post.post_source === 'customer' ? 'Customer' : 'Business'),
      logo_url: post.author_avatar,
      business_slug: post.business_slug, // Now available from unified_posts view
      phone: post.phone, // Now available from unified_posts view
      whatsapp_number: post.whatsapp_number, // Now available from unified_posts view
      city: null,
      state: null
    }
  };

  return (
    <ModernPostCard
      post={businessPost}
      index={index}
      onPostUpdate={onPostUpdate}
      onPostDelete={onPostDelete}
      onProductsUpdate={onProductsUpdate}
      showActualAspectRatio={showActualAspectRatio}
      disablePostClick={disablePostClick}
      enableImageFullscreen={enableImageFullscreen}
    />
  );
}
