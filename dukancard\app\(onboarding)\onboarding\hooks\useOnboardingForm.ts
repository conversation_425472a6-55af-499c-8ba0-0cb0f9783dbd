"use client";

import { useState, useEffect, useTransition, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSearchParams, useRouter } from "next/navigation";
import { toast } from "sonner";
import { PricingPlan } from "@/lib/PricingPlans";
import { formSchema, OnboardingFormData, User, ExistingBusinessProfileData } from "../types/onboarding";
import { stepFields } from "../constants/onboardingSteps";
import { cleanPhoneFromAuth } from "@/lib/utils";
import { createBusinessProfile } from "../actions";
import { getCoordinatesFromPincodeAndLocality } from "@/backend/supabase/services/location/location";

interface UseOnboardingFormOptions {
  redirectSlug?: string | null;
  message?: string | null;
  user: User | null;
  existingData?: ExistingBusinessProfileData | null;
  slugAvailable?: boolean | null;
  selectedPlan: PricingPlan | null;
}

export function useOnboardingForm({
  redirectSlug,
  message,
  user,
  existingData: _existingData,
  slugAvailable,
  selectedPlan,
}: UseOnboardingFormOptions) {
  const searchParams = useSearchParams();
  const [isSubmitting, startSubmitTransition] = useTransition();
  const [currentStep, setCurrentStep] = useState(1);
  const [cardRedirect, setCardRedirect] = useState<string | null>(redirectSlug || null);
  const [messageParam, setMessageParam] = useState<string | null>(message || null);
  const [isSubmitIntended, setIsSubmitIntended] = useState<boolean>(false);

  const form = useForm<OnboardingFormData>({
    resolver: zodResolver(formSchema),
    mode: "onChange", // Validate on change for better UX
    defaultValues: {
      businessName: "",
      email: "",
      memberName: "",
      title: "",
      phone: "",
      businessCategory: "",
      businessSlug: "",
      addressLine: "",
      pincode: "",
      city: "",
      state: "",
      locality: "",
      businessStatus: "online" as const,
      planId: "",
    },
  });

  // Get the redirect and message parameters from the URL or localStorage if not passed as prop
  useEffect(() => {
    if (!cardRedirect) {
      // First check URL parameters
      const redirect = searchParams.get('redirect');
      if (redirect) {
        setCardRedirect(redirect);
      } else if (typeof window !== 'undefined') {
        // Then check localStorage for redirect saved during auth callback
        const storedRedirect = localStorage.getItem('postOnboardingRedirect');
        if (storedRedirect) {
          setCardRedirect(storedRedirect);
          // Clear the stored redirect to prevent it from being used again
          localStorage.removeItem('postOnboardingRedirect');
        }
      }
    }

    if (!messageParam) {
      // Check URL parameters for message
      const message = searchParams.get('message');
      if (message) {
        setMessageParam(message);
      } else if (typeof window !== 'undefined') {
        // Then check localStorage for message saved during auth callback
        const storedMessage = localStorage.getItem('postOnboardingMessage');
        if (storedMessage) {
          setMessageParam(storedMessage);
          // Clear the stored message to prevent it from being used again
          localStorage.removeItem('postOnboardingMessage');
        }
      }
    }
  }, [searchParams, cardRedirect, messageParam]);

  // Pre-fill email when user data is available, but allow editing
  useEffect(() => {
    if (user?.email && !form.getValues("email")) {
      form.setValue("email", user.email, { shouldValidate: true });
    }
  }, [user, form]);

  // Pre-fill memberName from auth.users full_name or name
  useEffect(() => {
    if (user && !form.getValues("memberName")) {
      const userName = user.user_metadata?.full_name || user.user_metadata?.name || "";
      if (userName) {
        form.setValue("memberName", userName, { shouldValidate: true });
      }
    }
  }, [user, form]);

  // Pre-fill phone when user data is available, but allow editing
  useEffect(() => {
    if (user?.phone && !form.getValues("phone")) {
      const processedPhone = cleanPhoneFromAuth(user.phone);
      if (processedPhone) {
        form.setValue("phone", processedPhone, { shouldValidate: true });
      }
    }
  }, [user, form]);

  // Pre-fill member name from auth.users display_name when available
  useEffect(() => {
    if (user?.user_metadata?.display_name && !form.getValues("memberName")) {
      form.setValue("memberName", user.user_metadata.display_name, { shouldValidate: true });
    }
  }, [user, form]);

  // --- Step Navigation ---
  const handleNextStep = useCallback(async () => {
    const fieldsToValidate = stepFields[currentStep - 1];
    const isValid = await form.trigger(fieldsToValidate);

    if (currentStep === 2) {
      if (!isValid) return;
      if (slugAvailable !== true) {
        form.setError("businessSlug", {
          type: "manual",
          message:
            slugAvailable === false
              ? "This URL slug is already taken."
              : "Please enter a valid slug and wait for check.",
        });
        return;
      }
    } else if (!isValid) {
      return;
    }

    // Reset submit intention when navigating between steps
    setIsSubmitIntended(false);

    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep, form, slugAvailable]);

  const handlePreviousStep = useCallback(() => {
    // Reset submit intention when navigating between steps
    setIsSubmitIntended(false);

    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  // --- Form Submission ---
  const onSubmitHandler = useCallback((data: OnboardingFormData) => {
    // Prevent automatic submission - only allow explicit button click
    if (!isSubmitIntended) {
      return;
    }

    if (!selectedPlan) {
      toast.error("Please select a plan.");
      return;
    }

    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        formData.append(key, value as string);
      }
    });
    formData.set("planId", selectedPlan.id);

    startSubmitTransition(async () => {
      try {
        // Fetch coordinates from pincode and locality before submission
        let latitude: number | undefined;
        let longitude: number | undefined;

        if (data.pincode && data.locality) {
          const coordinatesResult = await getCoordinatesFromPincodeAndLocality(
            data.pincode,
            data.locality
          );

          if (coordinatesResult.success) {
            latitude = coordinatesResult.latitude;
            longitude = coordinatesResult.longitude;
          } else {
            console.warn('Could not fetch coordinates:', coordinatesResult.error);
            // Continue with submission even if coordinates fetch fails
          }
        }

        // Create FormData with coordinates
        const formDataWithCoords = new FormData();
        Object.entries(data).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            formDataWithCoords.append(key, value as string);
          }
        });

        // Add coordinates if available
        if (latitude !== undefined) {
          formDataWithCoords.append("latitude", latitude.toString());
        }
        if (longitude !== undefined) {
          formDataWithCoords.append("longitude", longitude.toString());
        }

        formDataWithCoords.set("planId", selectedPlan.id);

        const result = await createBusinessProfile({
          formData: formDataWithCoords,
          planId: selectedPlan.id,
          redirectSlug: cardRedirect,
          message: messageParam,
        });

        if (result?.error) {
          toast.error(`Onboarding failed: ${result.error}`);
          // Handle business slug error specifically
          if (result.error?.includes('slug')) {
            setCurrentStep(2);
          }
        } else {
          // If no error, the function will redirect automatically
          toast.success("Onboarding complete! Welcome aboard.");
        }
      } catch (error) {
        console.error('Onboarding error:', error);
        toast.error("An unexpected error occurred. Please try again.");
      }
    });
  }, [isSubmitIntended, selectedPlan, cardRedirect, messageParam]);

  return {
    form,
    isSubmitting,
    currentStep,
    handleNextStep,
    handlePreviousStep,
    onSubmitHandler,
    setIsSubmitIntended,
  };
}
