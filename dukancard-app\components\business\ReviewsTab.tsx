import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Modal, FlatList, Image } from 'react-native';
import { Star, SortAsc, Building2, User } from 'lucide-react-native';
import { AnimatedLoader } from '../ui/AnimatedLoader';
import { useRouter } from 'expo-router';
import { BusinessReview, BusinessCardData, ReviewSortOption } from '../../lib/services/businessCardDataService';
import { formatIndianNumberShort } from '@/lib/utils';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';
import { ReviewListSkeleton } from '../ui/ReviewSkeleton';

interface ReviewsTabProps {
  reviews: BusinessReview[];
  reviewStats: BusinessCardData['reviewStats'] | null;
  isDark: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  sortBy: ReviewSortOption;
  onLoadMore: () => void;
  onSort: (sortBy: ReviewSortOption) => void;
  useScrollView?: boolean; // New prop to use ScrollView instead of FlatList
  sortLoading?: boolean; // New prop for sort loading state
}

export default function ReviewsTab({
  reviews,
  reviewStats,
  isDark,
  loadingMore,
  hasMore,
  sortBy,
  onLoadMore,
  onSort,
  useScrollView = false,
  sortLoading = false,
}: ReviewsTabProps) {
  const styles = createPublicCardViewStyles(isDark);
  const router = useRouter();
  const [showSortModal, setShowSortModal] = useState(false);

  // Handle sort selection
  const handleSortSelect = (newSortBy: ReviewSortOption) => {
    onSort(newSortBy);
    setShowSortModal(false);
  };

  // Handle business reviewer press
  const handleBusinessReviewerPress = (reviewerSlug: string) => {
    if (reviewerSlug) {
      router.push(`/business/${reviewerSlug}`);
    }
  };

  // Get sort label
  const getSortLabel = (sort: ReviewSortOption) => {
    switch (sort) {
      case 'newest': return 'Newest First';
      case 'oldest': return 'Oldest First';
      case 'highest_rating': return 'Highest Rating';
      case 'lowest_rating': return 'Lowest Rating';
      default: return 'Newest First';
    }
  };

  return (
    <View style={styles.section}>
      {reviewStats && (
        <View style={styles.reviewStatsContainer}>
          <View style={styles.ratingOverview}>
            <Text style={styles.averageRating}>{reviewStats.averageRating.toFixed(1)}</Text>
            <View style={styles.starsContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  size={16}
                  color={star <= reviewStats.averageRating ? '#f39c12' : '#ddd'}
                  fill={star <= reviewStats.averageRating ? '#f39c12' : 'transparent'}
                />
              ))}
            </View>
            <Text style={styles.totalReviews}>({formatIndianNumberShort(reviewStats.totalReviews)} reviews)</Text>
          </View>
        </View>
      )}

      {/* Sort Button */}
      {reviews && reviews.length > 0 && (
        <View style={styles.sortContainer}>
          <TouchableOpacity
            style={styles.sortButton}
            onPress={() => setShowSortModal(true)}
          >
            <SortAsc color={isDark ? '#D4AF37' : '#D4AF37'} size={20} />
            <Text style={[styles.sortButtonText, { color: isDark ? '#FFFFFF' : '#000000' }]}>
              Sort by: {getSortLabel(sortBy)}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {sortLoading ? (
        // Show skeleton loading during sort operations
        <ReviewListSkeleton count={3} />
      ) : reviews && reviews.length > 0 ? (
        useScrollView ? (
          // Use simple View for nested scrolling - parent ScrollView handles scroll
          <View style={styles.flatListContent}>
            {reviews.map((item) => {
            const isBusiness = item.reviewer_type === 'business';
            const reviewerName = item.reviewer_name || item.customer_name || 'Anonymous';
            const reviewerAvatar = item.reviewer_avatar || item.customer_avatar;

            return (
              <View key={item.id} style={[
                styles.reviewCard,
                isBusiness && styles.businessReviewCard
              ]}>
                <View style={styles.reviewHeader}>
                  <View style={styles.reviewerInfo}>
                    {/* Avatar with different styling for business vs customer */}
                    <View style={[
                      styles.reviewerAvatar,
                      isBusiness && styles.businessReviewerAvatar
                    ]}>
                      {reviewerAvatar ? (
                        <Image
                          source={{ uri: reviewerAvatar }}
                          style={styles.reviewerAvatarImage}
                        />
                      ) : (
                        <>
                          {isBusiness ? (
                            <Building2 size={16} color={isDark ? '#FFFFFF' : '#000000'} />
                          ) : (
                            <User size={16} color={isDark ? '#FFFFFF' : '#000000'} />
                          )}
                        </>
                      )}
                    </View>

                    <View style={styles.reviewerDetails}>
                      {/* Reviewer name - clickable for businesses */}
                      {isBusiness && item.reviewer_slug ? (
                        <TouchableOpacity
                          onPress={() => handleBusinessReviewerPress(item.reviewer_slug!)}
                          activeOpacity={0.7}
                        >
                          <View style={styles.businessReviewerNameContainer}>
                            <Text style={[styles.reviewerName, styles.businessReviewerName]}>
                              {reviewerName}
                            </Text>
                            <View style={styles.businessBadge}>
                              <Building2 size={10} color="#D4AF37" />
                              <Text style={styles.businessBadgeText}>Business</Text>
                            </View>
                          </View>
                        </TouchableOpacity>
                      ) : (
                        <View style={styles.customerReviewerNameContainer}>
                          <Text style={styles.reviewerName}>
                            {reviewerName}
                          </Text>
                          {!isBusiness && (
                            <View style={styles.customerBadge}>
                              <User size={10} color="#6B7280" />
                              <Text style={styles.customerBadgeText}>Customer</Text>
                            </View>
                          )}
                        </View>
                      )}

                      {/* Rating stars */}
                      <View style={styles.reviewStars}>
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            size={12}
                            color={star <= item.rating ? '#f39c12' : '#ddd'}
                            fill={star <= item.rating ? '#f39c12' : 'transparent'}
                          />
                        ))}
                      </View>
                    </View>
                  </View>

                  <Text style={styles.reviewDate}>
                    {new Date(item.created_at).toLocaleDateString()}
                  </Text>
                </View>

                {item.review_text && (
                  <Text style={styles.reviewText}>{item.review_text}</Text>
                )}
              </View>
            );
            })}
            {/* Infinite scroll trigger element */}
            {hasMore && (
              <View style={styles.infiniteScrollTrigger} />
            )}
            {loadingMore && hasMore && (
              <View style={styles.loadMoreContainer}>
                <AnimatedLoader size={24} color="#D4AF37" />
                <Text style={styles.loadMoreText}>Loading more reviews...</Text>
              </View>
            )}
          </View>
        ) : (
          // Use FlatList for standalone scrolling
          <FlatList
            data={reviews}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => {
              const isBusiness = item.reviewer_type === 'business';
              const reviewerName = item.reviewer_name || item.customer_name || 'Anonymous';
              const reviewerAvatar = item.reviewer_avatar || item.customer_avatar;

              return (
                <View style={[
                  styles.reviewCard,
                  isBusiness && styles.businessReviewCard
                ]}>
                  <View style={styles.reviewHeader}>
                    <View style={styles.reviewerInfo}>
                      {/* Avatar with different styling for business vs customer */}
                      <View style={[
                        styles.reviewerAvatar,
                        isBusiness && styles.businessReviewerAvatar
                      ]}>
                        {reviewerAvatar ? (
                          <Image
                            source={{ uri: reviewerAvatar }}
                            style={styles.reviewerAvatarImage}
                          />
                        ) : (
                          <>
                            {isBusiness ? (
                              <Building2 size={16} color={isDark ? '#FFFFFF' : '#000000'} />
                            ) : (
                              <User size={16} color={isDark ? '#FFFFFF' : '#000000'} />
                            )}
                          </>
                        )}
                      </View>

                      <View style={styles.reviewerDetails}>
                        {/* Reviewer name - clickable for businesses */}
                        {isBusiness && item.reviewer_slug ? (
                          <TouchableOpacity
                            onPress={() => handleBusinessReviewerPress(item.reviewer_slug!)}
                            activeOpacity={0.7}
                          >
                            <View style={styles.businessReviewerNameContainer}>
                              <Text style={[styles.reviewerName, styles.businessReviewerName]}>
                                {reviewerName}
                              </Text>
                              <View style={styles.businessBadge}>
                                <Building2 size={10} color="#D4AF37" />
                                <Text style={styles.businessBadgeText}>Business</Text>
                              </View>
                            </View>
                          </TouchableOpacity>
                        ) : (
                          <View style={styles.customerReviewerNameContainer}>
                            <Text style={styles.reviewerName}>
                              {reviewerName}
                            </Text>
                            {!isBusiness && (
                              <View style={styles.customerBadge}>
                                <User size={10} color="#6B7280" />
                                <Text style={styles.customerBadgeText}>Customer</Text>
                              </View>
                            )}
                          </View>
                        )}

                        {/* Rating stars */}
                        <View style={styles.reviewStars}>
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              size={12}
                              color={star <= item.rating ? '#f39c12' : '#ddd'}
                              fill={star <= item.rating ? '#f39c12' : 'transparent'}
                            />
                          ))}
                        </View>
                      </View>
                    </View>

                    <Text style={styles.reviewDate}>
                      {new Date(item.created_at).toLocaleDateString()}
                    </Text>
                  </View>

                  {item.review_text && (
                    <Text style={styles.reviewText}>{item.review_text}</Text>
                  )}
                </View>
              );
            }}
            onEndReached={() => {
              if (hasMore && !loadingMore) {
                onLoadMore();
              }
            }}
            onEndReachedThreshold={0.1}
            ListFooterComponent={() => {
              if (loadingMore && hasMore) {
                return (
                  <View style={styles.loadMoreContainer}>
                    <AnimatedLoader size={24} color="#D4AF37" />
                    <Text style={styles.loadMoreText}>Loading more reviews...</Text>
                  </View>
                );
              }
              return null;
            }}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.flatListContent}
          />
        )
      ) : (
        <Text style={styles.emptyText}>No reviews available</Text>
      )}

      {/* Sort Modal */}
      <Modal
        visible={showSortModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowSortModal(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowSortModal(false)}
        >
          <View style={styles.sortModal}>
            <Text style={styles.sortModalTitle}>Sort Reviews</Text>

            {/* Date Section */}
            <View style={styles.sortSection}>
              <Text style={styles.sortSectionTitle}>Date</Text>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'newest' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('newest')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'newest' && styles.sortOptionTextSelected]}>
                  Newest First
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'oldest' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('oldest')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'oldest' && styles.sortOptionTextSelected]}>
                  Oldest First
                </Text>
              </TouchableOpacity>
            </View>

            {/* Rating Section */}
            <View style={styles.sortSection}>
              <Text style={styles.sortSectionTitle}>Rating</Text>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'highest_rating' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('highest_rating')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'highest_rating' && styles.sortOptionTextSelected]}>
                  Highest Rating
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.sortOption, sortBy === 'lowest_rating' && styles.sortOptionSelected]}
                onPress={() => handleSortSelect('lowest_rating')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'lowest_rating' && styles.sortOptionTextSelected]}>
                  Lowest Rating
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}
