import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Linking,
  ActivityIndicator,
} from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { AdData, BusinessCustomAd } from '@/types/ad';
import { createEnhancedAdSectionStyles } from '@/styles/components/EnhancedAdSectionStyles';
import { SkeletonLoader } from '@/components/ui/SkeletonLoader';

interface EnhancedAdSectionProps {
  topAdData: AdData | null;
  businessCustomAd?: BusinessCustomAd;
  userPlan?: string;
  loading?: boolean;
}

export default function EnhancedAdSection({
  topAdData,
  businessCustomAd,
  userPlan,
  loading = false,
}: EnhancedAdSectionProps) {
  const colorScheme = useColorScheme();
  const styles = createEnhancedAdSectionStyles(colorScheme);
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  // Helper function to validate URLs (simplified version for React Native)
  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  // Check if business owner has Pro/Enterprise access for custom ads
  const hasProEnterpriseAccess = userPlan === 'pro' || userPlan === 'enterprise';

  // Validate custom ad data - must have Pro/Enterprise plan, enabled=true, and valid image URL
  const isCustomAdValid = businessCustomAd &&
    typeof businessCustomAd === 'object' &&
    businessCustomAd.enabled === true &&
    businessCustomAd.image_url &&
    typeof businessCustomAd.image_url === 'string' &&
    businessCustomAd.image_url.trim() !== '' &&
    isValidUrl(businessCustomAd.image_url);

  // Validate custom ad link URL if provided
  const hasValidLinkUrl = businessCustomAd?.link_url &&
    typeof businessCustomAd.link_url === 'string' &&
    businessCustomAd.link_url.trim() !== '' &&
    isValidUrl(businessCustomAd.link_url);

  // Determine which ad to show - business custom ad takes priority (only for Pro/Enterprise with valid data and enabled=true)
  const shouldShowBusinessAd = hasProEnterpriseAccess && isCustomAdValid;
  const shouldShowTopAd = !shouldShowBusinessAd && topAdData && topAdData.imageUrl;

  const handleAdPress = async (url: string) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        console.error('Cannot open URL:', url);
      }
    } catch (error) {
      console.error('Error opening URL:', error);
    }
  };

  const renderBusinessAd = () => {
    if (!businessCustomAd?.image_url) return null;

    const AdContent = (
      <View style={styles.adContainer}>
        <View style={styles.imageContainer}>
          {imageLoading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#D4AF37" />
            </View>
          )}
          <Image
            source={{ uri: businessCustomAd.image_url }}
            style={[styles.adImage, imageError && styles.hiddenImage]}
            onLoad={() => setImageLoading(false)}
            onError={() => {
              setImageLoading(false);
              setImageError(true);
            }}
            resizeMode="cover"
          />
          {/* Business ad indicator */}
          <View style={styles.sponsoredBadge}>
            <Text style={styles.sponsoredText}>Sponsored</Text>
          </View>
        </View>
      </View>
    );

    if (hasValidLinkUrl) {
      return (
        <TouchableOpacity
          onPress={() => handleAdPress(businessCustomAd.link_url!)}
          activeOpacity={0.8}
          style={styles.clickableAd}
        >
          {AdContent}
        </TouchableOpacity>
      );
    }

    return AdContent;
  };

  const renderCustomAd = () => {
    if (!topAdData?.imageUrl) return null;

    const AdContent = (
      <View style={styles.adContainer}>
        <View style={styles.imageContainer}>
          {imageLoading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#D4AF37" />
            </View>
          )}
          <Image
            source={{ uri: topAdData.imageUrl }}
            style={[styles.adImage, imageError && styles.hiddenImage]}
            onLoad={() => setImageLoading(false)}
            onError={() => {
              setImageLoading(false);
              setImageError(true);
            }}
            resizeMode="cover"
          />
          {/* Custom ad indicator */}
          <View style={styles.adBadge}>
            <Text style={styles.adText}>Ad</Text>
          </View>
        </View>
      </View>
    );

    if (topAdData.linkUrl) {
      return (
        <TouchableOpacity
          onPress={() => handleAdPress(topAdData.linkUrl!)}
          activeOpacity={0.8}
          style={styles.clickableAd}
        >
          {AdContent}
        </TouchableOpacity>
      );
    }

    return AdContent;
  };

  const renderAdSkeleton = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.adContainer}>
        <View style={styles.imageContainer}>
          <SkeletonLoader
            width="100%"
            height={180}
            borderRadius={0}
            style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}
          />
        </View>
      </View>
    </View>
  );

  const renderPlaceholderAd = () => (
    <View style={styles.placeholderContainer}>
      <View style={styles.placeholderContent}>
        <Text style={styles.placeholderTitle}>Advertise Here</Text>
        <Text style={styles.placeholderSubtitle}>
          Reach thousands of potential customers in your area
        </Text>
        <TouchableOpacity
          style={styles.advertiseButton}
          onPress={() => handleAdPress('https://dukancard.in/advertise')}
        >
          <Text style={styles.advertiseButtonText}>Learn More</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Show skeleton while loading
  if (loading) {
    return renderAdSkeleton();
  }

  return (
    <View style={styles.sectionContainer}>
      {shouldShowBusinessAd ? (
        // Business Custom Ad (Pro/Enterprise only with enabled=true)
        renderBusinessAd()
      ) : shouldShowTopAd ? (
        // Platform/Global Ad
        renderCustomAd()
      ) : (
        // Placeholder Ad
        renderPlaceholderAd()
      )}
    </View>
  );
}
