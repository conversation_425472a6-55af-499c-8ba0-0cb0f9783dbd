"use server";

// Import Razorpay subscription utilities
// These will be implemented with Razorpay-specific code
// For now, we'll use placeholder functions
const createSubscriptionAuthForUser = async (_userId: string, _subscriptionId: string, _sessionId: string, _paymentMethodDetails: Record<string, Record<string, string>>) => ({ success: false, error: "Not implemented with Razorpay yet" });
const createSubscriptionChargeForUser = async (_userId: string, _subscriptionId: string, _amount: number, _scheduleDate: string, _paymentMethodDetails: Record<string, Record<string, string>>, _remarks?: string) => ({ success: false, error: "Not implemented with <PERSON><PERSON><PERSON>y yet" });
const getSubscriptionPaymentDetails = async (_userId: string, _subscriptionId: string, _paymentId: string) => ({ success: false, error: "Not implemented with Razorpay yet", data: undefined });
const getSubscriptionPaymentsList = async (_userId: string, _subscriptionId: string) => ({ success: false, error: "Not implemented with Razorpay yet", data: undefined });
const manageSubscriptionPaymentForUser = async (_userId: string, _subscriptionId: string, _paymentId: string, _action: string, _actionDetails?: Record<string, unknown>) => ({ success: false, error: "Not implemented with Razorpay yet" });
import {
  PaymentMethod,
  PaymentType,
  PaymentOptions,
  PaymentManagementAction,
  PaymentManagementActionDetails,
  ActionResponse
} from "./types";
import {
  getUserAndProfile,
  revalidateSubscriptionPaths,
  createErrorResponse,
  doesSubscriptionBelongToUser
} from "./utils";



/**
 * Create a subscription payment (AUTH or CHARGE)
 * @param subscriptionId The subscription ID
 * @param paymentType The payment type
 * @param paymentMethod The payment method
 * @param options The payment options
 * @returns The payment result
 */
export async function createSubscriptionPayment(
  subscriptionId: string,
  paymentType: PaymentType,
  paymentMethod: PaymentMethod,
  options: PaymentOptions
): Promise<ActionResponse> {
  // Get user
  const { user, error } = await getUserAndProfile();

  if (error || !user) {
    return createErrorResponse(error || "User not authenticated");
  }

  // Check if the user has the subscription
  const belongsToUser = await doesSubscriptionBelongToUser(user.id, subscriptionId);
  if (!belongsToUser) {
    return createErrorResponse("User does not have this subscription");
  }

  // Prepare payment method details
  const paymentMethodDetails: Record<string, Record<string, string>> = {};
  paymentMethodDetails[paymentMethod.type] = paymentMethod.details;

  // Create the payment
  let result;

  if (paymentType === "AUTH") {
    if (!options.sessionId) {
      return createErrorResponse("sessionId is required for AUTH payment type");
    }

    result = await createSubscriptionAuthForUser(
      user.id,
      subscriptionId,
      options.sessionId,
      paymentMethodDetails
    );
  } else {
    if (!options.amount || !options.scheduleDate) {
      return createErrorResponse("amount and scheduleDate are required for CHARGE payment type");
    }

    result = await createSubscriptionChargeForUser(
      user.id,
      subscriptionId,
      options.amount,
      options.scheduleDate,
      paymentMethodDetails,
      options.remarks
    );
  }

  // Revalidate paths
  revalidateSubscriptionPaths();

  return result;
}

/**
 * Create a subscription authorization
 * @param subscriptionId The subscription ID
 * @param sessionId The session ID
 * @param paymentMethod The payment method
 * @returns The authorization result
 */
export async function createSubscriptionAuth(
  subscriptionId: string,
  sessionId: string,
  paymentMethod: PaymentMethod
): Promise<ActionResponse> {
  return createSubscriptionPayment(
    subscriptionId,
    "AUTH",
    paymentMethod,
    { sessionId }
  );
}

/**
 * Create a subscription charge
 * @param subscriptionId The subscription ID
 * @param amount The payment amount
 * @param scheduleDate The payment schedule date
 * @param paymentMethod The payment method
 * @param remarks Optional payment remarks
 * @returns The charge result
 */
export async function createSubscriptionCharge(
  subscriptionId: string,
  amount: number,
  scheduleDate: string,
  paymentMethod: PaymentMethod,
  remarks?: string
): Promise<ActionResponse> {
  return createSubscriptionPayment(
    subscriptionId,
    "CHARGE",
    paymentMethod,
    { amount, scheduleDate, remarks }
  );
}

/**
 * Get subscription payment details
 *
 * This server action fetches the details of a specific payment for a subscription.
 * It verifies that the user has access to the subscription before making the API call.
 *
 * @param subscriptionId The subscription ID
 * @param paymentId The payment ID
 * @returns The payment details
 *
 * Example response:
 * ```json
 * {
 *   "success": true,
 *   "data": {
 *     "payment_amount": 1,
 *     "payment_id": "test-payment-id",
 *     "payment_status": "SUCCESS",
 *     "payment_type": "AUTH",
 *     "subscription_id": "test-subscription-id"
 *   }
 * }
 * ```
 */
/**
 * Manage a subscription payment (CANCEL or RETRY)
 *
 * This server action manages a payment for a subscription by either cancelling it or scheduling a retry.
 * It first verifies that the user has access to the subscription before making the API call.
 *
 * @param subscriptionId The subscription ID
 * @param paymentId The payment ID
 * @param action The action to perform (CANCEL or RETRY)
 * @param actionDetails Optional action details (required for RETRY)
 * @returns The payment management result
 *
 * Example usage for cancelling a payment:
 * ```typescript
 * const result = await managePayment(subscriptionId, paymentId, "CANCEL");
 * ```
 *
 * Example usage for retrying a payment:
 * ```typescript
 * const nextScheduledTime = new Date();
 * nextScheduledTime.setDate(nextScheduledTime.getDate() + 1); // Schedule for tomorrow
 *
 * const result = await managePayment(
 *   subscriptionId,
 *   paymentId,
 *   "RETRY",
 *   { next_scheduled_time: nextScheduledTime.toISOString() }
 * );
 * ```
 */
export async function managePayment(
  subscriptionId: string,
  paymentId: string,
  action: PaymentManagementAction,
  actionDetails?: PaymentManagementActionDetails
): Promise<ActionResponse> {
  // Get user
  const { user, error } = await getUserAndProfile();

  if (error || !user) {
    return createErrorResponse(error || "User not authenticated");
  }

  // Check if the user has the subscription
  const belongsToUser = await doesSubscriptionBelongToUser(user.id, subscriptionId);
  if (!belongsToUser) {
    return createErrorResponse("User does not have this subscription");
  }

  // Validate action details for RETRY
  if (action === "RETRY" && !actionDetails?.next_scheduled_time) {
    return createErrorResponse("next_scheduled_time is required for RETRY action");
  }

  // Manage the payment
  const result = await manageSubscriptionPaymentForUser(
    user.id,
    subscriptionId,
    paymentId,
    action,
    actionDetails as Record<string, unknown>
  );

  // Revalidate paths
  revalidateSubscriptionPaths();

  return result;
}

/**
 * Retry a subscription payment
 *
 * This server action schedules a retry for a failed payment.
 * It requires the next scheduled time for the retry.
 *
 * @param subscriptionId The subscription ID
 * @param paymentId The payment ID
 * @param nextScheduledTime The next scheduled time for the payment (ISO string)
 * @returns The payment management result
 */
export async function retryPayment(
  subscriptionId: string,
  paymentId: string,
  nextScheduledTime: string
): Promise<ActionResponse> {
  return managePayment(
    subscriptionId,
    paymentId,
    "RETRY",
    { next_scheduled_time: nextScheduledTime }
  );
}

/**
 * Alias for retryPayment to match function name used in SubscriptionManager
 */
export const retrySubscriptionPayment = retryPayment;

/**
 * Cancel a subscription payment
 *
 * This server action cancels a pending or scheduled payment.
 *
 * @param subscriptionId The subscription ID
 * @param paymentId The payment ID
 * @returns The payment management result
 */
export async function cancelPayment(
  subscriptionId: string,
  paymentId: string
): Promise<ActionResponse> {
  return managePayment(
    subscriptionId,
    paymentId,
    "CANCEL"
  );
}

export async function getPaymentDetails(
  subscriptionId: string,
  paymentId: string
): Promise<ActionResponse> {
  // Get user
  const { user, error } = await getUserAndProfile();

  if (error || !user) {
    return createErrorResponse(error || "User not authenticated");
  }

  // Check if the user has the subscription
  const belongsToUser = await doesSubscriptionBelongToUser(user.id, subscriptionId);
  if (!belongsToUser) {
    return createErrorResponse("User does not have this subscription");
  }

  // Get the payment details
  const result = await getSubscriptionPaymentDetails(
    user.id,
    subscriptionId,
    paymentId
  );

  // Convert to ActionResponse type
  return {
    success: result.success,
    data: result.data as Record<string, unknown> | undefined,
    error: result.error
  };
}

/**
 * Get all payments for a subscription
 *
 * This server action fetches all payments for a subscription.
 * It verifies that the user has access to the subscription before making the API call.
 *
 * @param subscriptionId The subscription ID
 * @returns The list of payments
 *
 * Example response:
 * ```json
 * {
 *   "success": true,
 *   "data": [
 *     {
 *       "payment_amount": 1,
 *       "payment_id": "test-payment-id",
 *       "payment_status": "SUCCESS",
 *       "payment_type": "AUTH",
 *       "subscription_id": "test-subscription-id"
 *     },
 *     {
 *       "payment_amount": 499,
 *       "payment_id": "charge-payment-1",
 *       "payment_status": "SUCCESS",
 *       "payment_type": "CHARGE",
 *       "subscription_id": "test-subscription-id"
 *     }
 *   ]
 * }
 * ```
 */
export async function getPaymentsList(
  subscriptionId: string
): Promise<ActionResponse> {
  // Get user
  const { user, error } = await getUserAndProfile();

  if (error || !user) {
    return createErrorResponse(error || "User not authenticated");
  }

  // Check if the user has the subscription
  const belongsToUser = await doesSubscriptionBelongToUser(user.id, subscriptionId);
  if (!belongsToUser) {
    return createErrorResponse("User does not have this subscription");
  }

  // Get all payments for the subscription
  const result = await getSubscriptionPaymentsList(
    user.id,
    subscriptionId
  );

  // Convert to ActionResponse type
  return {
    success: result.success,
    data: result.data as Record<string, unknown> | undefined,
    error: result.error
  };
}

// Refund functionality has been removed
