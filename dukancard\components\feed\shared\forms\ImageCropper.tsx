"use client";

import { useState, useCallback } from "react";
import <PERSON><PERSON><PERSON> from "react-easy-crop";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { motion, AnimatePresence } from "framer-motion";
import { Crop, Loader2 } from "lucide-react";


export interface ImageCropperProps {
  isOpen: boolean;
  imageSrc: string;
  onCropComplete: (_croppedFile: File) => void;
  onCancel: () => void;
  originalFileName?: string;
}

// Fixed aspect ratio for posts (4:3 to match post card display)
const ASPECT_RATIO = 4 / 3;

// Helper function to create image from canvas
const createImage = (url: string): Promise<HTMLImageElement> =>
  new Promise((resolve, reject) => {
    const image = new Image();
    image.addEventListener('load', () => resolve(image));
    image.addEventListener('error', error => reject(error));
    image.setAttribute('crossOrigin', 'anonymous');
    image.src = url;
  });

// Helper function to get cropped image
const getCroppedImg = async (
  imageSrc: string,
  pixelCrop: { x: number; y: number; width: number; height: number }
): Promise<Blob> => {
  const image = await createImage(imageSrc);
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('No 2d context');
  }

  canvas.width = pixelCrop.width;
  canvas.height = pixelCrop.height;

  ctx.drawImage(
    image,
    pixelCrop.x,
    pixelCrop.y,
    pixelCrop.width,
    pixelCrop.height,
    0,
    0,
    pixelCrop.width,
    pixelCrop.height
  );

  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      if (blob) resolve(blob);
    }, 'image/webp', 0.9);
  });
};

export default function ImageCropper({
  isOpen,
  imageSrc,
  onCropComplete,
  onCancel,
  originalFileName = "cropped-image",
}: ImageCropperProps) {
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
  } | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Handle crop completion
  const onCropCompleteCallback = useCallback(
    (_croppedArea: unknown, croppedAreaPixels: { x: number; y: number; width: number; height: number }) => {
      setCroppedAreaPixels(croppedAreaPixels);
    },
    []
  );

  // Generate cropped image
  const generateCroppedImage = useCallback(async () => {
    if (!croppedAreaPixels) {
      return;
    }

    setIsProcessing(true);

    try {
      const croppedImageBlob = await getCroppedImg(imageSrc, croppedAreaPixels);

      // Create file from blob
      const fileName = originalFileName.replace(/\.[^/.]+$/, "") + "_cropped.webp";
      const croppedFile = new File([croppedImageBlob], fileName, {
        type: "image/webp",
        lastModified: Date.now(),
      });

      onCropComplete(croppedFile);
      setIsProcessing(false);
    } catch (error) {
      console.error("Error generating cropped image:", error);
      setIsProcessing(false);
    }
  }, [croppedAreaPixels, imageSrc, onCropComplete, originalFileName]);

  return (
    <Dialog open={isOpen} onOpenChange={() => !isProcessing && onCancel()}>
      <DialogContent className="w-[95vw] max-w-2xl max-h-[95vh] overflow-hidden p-4 sm:p-6">
        <DialogHeader className="text-center pb-2">
          <DialogTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Crop Image
          </DialogTitle>
          <DialogDescription className="text-gray-500 dark:text-gray-400 mt-2">
            Crop your image to 4:3 aspect ratio (landscape)
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-3 sm:space-y-4">
          {/* Crop area */}
          <div className="relative h-[40vh] sm:h-[50vh] md:h-[55vh] w-full bg-neutral-100 dark:bg-neutral-800 rounded-lg overflow-hidden border-2 border-blue-100 dark:border-blue-900/30">
            <Cropper
              image={imageSrc}
              crop={crop}
              zoom={zoom}
              aspect={ASPECT_RATIO}
              onCropChange={setCrop}
              onZoomChange={setZoom}
              onCropComplete={onCropCompleteCallback}
              showGrid={true}
              style={{
                containerStyle: {
                  borderRadius: '8px',
                  overflow: 'hidden',
                },
              }}
            />
          </div>

          {/* Zoom control */}
          <div className="flex items-center space-x-2 sm:space-x-4 px-3 py-2 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
            <span className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap font-medium">Zoom:</span>
            <input
              type="range"
              min={1}
              max={3}
              step={0.1}
              value={zoom}
              onChange={(e) => setZoom(Number(e.target.value))}
              className="flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
              style={{
                background: `linear-gradient(to right, rgb(59 130 246) 0%, rgb(59 130 246) ${((zoom - 1) / 2) * 100}%, rgb(229 231 235) ${((zoom - 1) / 2) * 100}%, rgb(229 231 235) 100%)`
              }}
            />
            <span className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap min-w-[3rem] font-medium">
              {Math.round(zoom * 100)}%
            </span>
          </div>
        </div>

        <DialogFooter className="flex flex-col-reverse sm:flex-row gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isProcessing}
            className="flex-1 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200"
          >
            Cancel
          </Button>

          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="flex-1"
          >
            <Button
              type="button"
              onClick={generateCroppedImage}
              disabled={!croppedAreaPixels || isProcessing}
              className={`
                w-full relative overflow-hidden
                bg-gradient-to-r from-blue-500 to-blue-600
                hover:from-blue-600 hover:to-blue-700
                text-white font-medium
                shadow-lg hover:shadow-xl
                transition-all duration-300
                before:absolute before:inset-0
                before:bg-gradient-to-r before:from-blue-400 before:to-blue-500
                before:opacity-0 hover:before:opacity-20
                before:transition-opacity before:duration-300
                ${isProcessing ? 'cursor-not-allowed opacity-80' : ''}
              `}
              style={{
                boxShadow: isProcessing
                  ? '0 4px 20px rgba(59, 130, 246, 0.3)'
                  : '0 4px 20px rgba(59, 130, 246, 0.4), 0 0 20px rgba(59, 130, 246, 0.2)'
              }}
            >
              <AnimatePresence mode="wait">
                {isProcessing ? (
                  <motion.div
                    key="processing"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className="flex items-center justify-center"
                  >
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </motion.div>
                ) : (
                  <motion.div
                    key="crop"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className="flex items-center justify-center"
                  >
                    <Crop className="h-4 w-4 mr-2" />
                    Apply Crop
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </motion.div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
