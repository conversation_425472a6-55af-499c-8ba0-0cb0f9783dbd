import crypto from 'crypto';

// Razorpay API URLs
export const RAZORPAY_API_URL = process.env.NODE_ENV === 'production'
  ? 'https://api.razorpay.com/v2'
  : 'https://api.razorpay.com/v2';

/**
 * Get Razorpay authentication credentials
 * @returns The Razorpay key ID and secret
 */
export const getRazorpayAuth = () => {
  // Use different keys for production and development environments
  let keyId: string | undefined;
  let keySecret: string | undefined;

  if (process.env.NODE_ENV === 'production') {
    // Use production keys
    keyId = process.env.RAZORPAY_LIVE_KEY_ID || '***********************';
    keySecret = process.env.RAZORPAY_LIVE_SECRET_KEY || 'ZE2AurACFXhx0b1sQaLG4YfQ';
  } else {
    // Use test keys
    keyId = process.env.RAZORPAY_KEY_ID || 'rzp_test_ksxy6FklIhV1xC';
    // Check for both possible environment variable names for the secret
    keySecret = process.env.RAZORPAY_KEY_SECRET || process.env.RAZORPAY_SECRET_KEY || 'ZE2AurACFXhx0b1sQaLG4YfQ';
  }

  if (!keyId || !keySecret) {
    console.error('[RAZORPAY] Missing credentials:', {
      keyId: !!keyId,
      keySecret: !!keySecret,
      env: process.env.NODE_ENV
    });
    throw new Error('Razorpay credentials not configured');
  }

  return { keyId, keySecret };
};

/**
 * Get Razorpay API headers with authentication
 * @returns The headers for Razorpay API requests
 */
export const getRazorpayApiHeaders = () => {
  const { keyId, keySecret } = getRazorpayAuth();

  // Create Basic Auth header
  const authString = Buffer.from(`${keyId}:${keySecret}`).toString('base64');

  return {
    'Authorization': `Basic ${authString}`,
    'Content-Type': 'application/json'
  };
};

/**
 * Verify Razorpay webhook signature
 *
 * Razorpay uses HMAC SHA256 for webhook signatures
 *
 * @param payload The webhook payload as a string
 * @param signature The webhook signature from the headers
 * @param secret The webhook secret from environment variables
 * @returns Whether the signature is valid
 */
export const verifyWebhookSignature = (
  payload: string,
  signature: string,
  secret: string
): boolean => {
  try {
    // Razorpay uses HMAC SHA256 for webhook signatures
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');

    // Use timing-safe comparison to prevent timing attacks
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  } catch (error) {
    console.error('[RAZORPAY_WEBHOOK] Error verifying webhook signature:', error);
    return false;
  }
};

/**
 * Verify Razorpay credentials by making a test API call
 * @returns Whether the credentials are valid
 */
export const verifyRazorpayCredentials = async (): Promise<boolean> => {
  try {
    const headers = getRazorpayApiHeaders();

    // Make a simple API call to verify credentials
    const response = await fetch(`${RAZORPAY_API_URL}/customers`, {
      method: 'GET',
      headers
    });

    return response.ok;
  } catch (error) {
    console.error('[RAZORPAY] Error verifying credentials:', error);
    return false;
  }
};
