ninja: Entering directory `C:\Users\<USER>\Desktop\Dukancard Web App\dukancard-app\android\app\.cxx\Debug\5i261l4z\armeabi-v7a'
[0/2] Re-checking globbed directories...
[1/90] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[2/90] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[3/90] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[4/90] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[5/90] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[6/90] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[7/90] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/29c6ac0a1dd6ebb9204efeee5b088e58/rnasyncstorageJSI-generated.cpp.o
[8/90] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[9/90] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o
[10/90] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o
[11/90] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/1d35ed449d7ed1132b7810290d98901f/ComponentDescriptors.cpp.o
[12/90] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o
[13/90] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o
[14/90] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o
[15/90] Building CXX object RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/EventEmitters.cpp.o
[16/90] Building CXX object RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o
[17/90] Building CXX object RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/ComponentDescriptors.cpp.o
[18/90] Building CXX object RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/RNCTabView-generated.cpp.o
[19/90] Building CXX object RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/Props.cpp.o
[20/90] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o
[21/90] Building CXX object RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/States.cpp.o
[22/90] Building CXX object RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/RNCTabViewJSI-generated.cpp.o
[23/90] Building CXX object RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/react/renderer/components/RNCTabView/ShadowNodes.cpp.o
[24/90] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o
[25/90] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o
[26/90] Building CXX object CMakeFiles/appmodules.dir/a9b7d3f3b37cff0b0133a74066882b5d/dukancard-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[27/90] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[28/90] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o
[29/90] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o
[30/90] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o
[31/90] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o
[32/90] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o
[33/90] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o
[34/90] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o
[35/90] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o
[36/90] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o
[37/90] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o
[38/90] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c51938d3e6a253a3009a2c56b716e8a9/RNCSafeAreaViewShadowNode.cpp.o
[39/90] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/43b809937c47d5f1950eff660045d62e/ComponentDescriptors.cpp.o
[40/90] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3e6ef1f046d08e2c3fe07b8fb087e6c8/safeareacontext/EventEmitters.cpp.o
[41/90] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c51938d3e6a253a3009a2c56b716e8a9/RNCSafeAreaViewState.cpp.o
[42/90] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3499c5ac0a53884d052b3bc82cdce709/jni/safeareacontext-generated.cpp.o
[43/90] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/5d636cbcd229f104b062b5d1e1270509/components/safeareacontext/Props.cpp.o
[44/90] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3e6ef1f046d08e2c3fe07b8fb087e6c8/safeareacontext/States.cpp.o
[45/90] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/43b809937c47d5f1950eff660045d62e/safeareacontextJSI-generated.cpp.o
[46/90] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3e6ef1f046d08e2c3fe07b8fb087e6c8/safeareacontext/ShadowNodes.cpp.o
[47/90] Linking CXX shared library "C:\Users\<USER>\Desktop\Dukancard Web App\dukancard-app\android\app\build\intermediates\cxx\Debug\5i261l4z\obj\armeabi-v7a\libreact_codegen_safeareacontext.so"
[48/90] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/80d27b9b47dc95aaffe5bad82ae8e0fb/RNSScreenStackHeaderConfigShadowNode.cpp.o
[49/90] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0bd7b61d274820429f2a575349562c5c/components/rnscreens/RNSScreenShadowNode.cpp.o
[50/90] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2156fb46c433072b3da64e4a3d42b035/rnscreens/RNSModalScreenShadowNode.cpp.o
[51/90] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2156fb46c433072b3da64e4a3d42b035/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[52/90] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[53/90] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2156fb46c433072b3da64e4a3d42b035/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[54/90] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/80d27b9b47dc95aaffe5bad82ae8e0fb/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[55/90] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2156fb46c433072b3da64e4a3d42b035/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[56/90] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/de9ef7ac22ab3b6b6d4803db570e9c12/renderer/components/rnscreens/RNSScreenState.cpp.o
[57/90] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/caaa5fce54ffafb439b043bcaf3fd825/components/rnscreens/rnscreensJSI-generated.cpp.o
[58/90] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2319e830721e0e0eafceebf67cce4793/react/renderer/components/rnscreens/States.cpp.o
[59/90] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6fa3974dcf3475fb474084dc201e48e8/renderer/components/rnscreens/EventEmitters.cpp.o
[60/90] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6fa3974dcf3475fb474084dc201e48e8/renderer/components/rnscreens/ShadowNodes.cpp.o
[61/90] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2319e830721e0e0eafceebf67cce4793/react/renderer/components/rnscreens/Props.cpp.o
[62/90] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/2f94934fd237e76f68d2bda5849339c7/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o
[63/90] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/caaa5fce54ffafb439b043bcaf3fd825/components/rnscreens/ComponentDescriptors.cpp.o
[64/90] Linking CXX shared library "C:\Users\<USER>\Desktop\Dukancard Web App\dukancard-app\android\app\build\intermediates\cxx\Debug\5i261l4z\obj\armeabi-v7a\libreact_codegen_rnscreens.so"
[65/90] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/9fcacfee94a56df95dee4d9c5d961fd6/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o
[66/90] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/2f94934fd237e76f68d2bda5849339c7/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o
[67/90] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/6180855d4482a4b3bef2030cd547cc59/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o
[68/90] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o
[69/90] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o
[70/90] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o
[71/90] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o
[72/90] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/1b3febb7467a3c0483c5fb5a3fdf6263/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o
C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:31:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   31 | void RNSVGImageEventEmitter::onLoad(OnLoad $event) const {
      |                                            ^
C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:32:26: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   32 |   dispatchEvent("load", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                          ^
C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:32:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   32 |   dispatchEvent("load", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                           ^
C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:33:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   33 |     auto $payload = jsi::Object(runtime);
      |          ^
C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:36:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   36 |   source.setProperty(runtime, "width", $event.source.width);
      |                                        ^
C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:37:41: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   37 |   source.setProperty(runtime, "height", $event.source.height);
      |                                         ^
C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:38:38: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   38 |   source.setProperty(runtime, "uri", $event.source.uri);
      |                                      ^
C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:39:3: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   39 |   $payload.setProperty(runtime, "source", source);
      |   ^
C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:41:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   41 |     return $payload;
      |            ^
9 warnings generated.
[73/90] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o
[74/90] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/1b3febb7467a3c0483c5fb5a3fdf6263/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o
[75/90] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/5a8cfdcf52865bd271d518bef9244a20/codegen/jni/react/renderer/components/rnsvg/States.cpp.o
[76/90] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o
[77/90] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/5a8cfdcf52865bd271d518bef9244a20/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o
[78/90] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/4901a1f2aa0c1c71fe85c62af5592e7d/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o
[79/90] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o
[80/90] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o
[81/90] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/4901a1f2aa0c1c71fe85c62af5592e7d/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o
[82/90] Linking CXX shared library "C:\Users\<USER>\Desktop\Dukancard Web App\dukancard-app\android\app\build\intermediates\cxx\Debug\5i261l4z\obj\armeabi-v7a\libreact_codegen_rnsvg.so"
[83/90] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o
[84/90] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o
[85/90] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o
[86/90] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/68c0b361f082d70d15ec3e1cd2e973b0/RNCWebViewSpecJSI-generated.cpp.o
[87/90] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o
[88/90] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o
[89/90] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o
[90/90] Linking CXX shared library "C:\Users\<USER>\Desktop\Dukancard Web App\dukancard-app\android\app\build\intermediates\cxx\Debug\5i261l4z\obj\armeabi-v7a\libappmodules.so"
