"use client";

import { useSearchParams } from "next/navigation";
import { useTransition } from "react";
import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { searchCategoryCombined, fetchMoreBusinessCardsByCategoryCombined } from "../actions";
import { CATEGORY_BUSINESSES_PER_PAGE } from "../constants/paginationConstants";
import { BusinessProfilePublicData } from "@/lib/actions/businessProfiles/types";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import {
  BUSINESS_NAME_PARAM,
  BUSINESS_SORT_PARAM,
  PINCODE_PARAM,
  LOCALITY_PARAM,
} from "@/app/(main)/discover/constants/urlParamConstants";
import { CategorySearchResult, ViewType, SerializableCategory } from "./types";

// Business context functions
export function useBusinessContextFunctions(
  category: SerializableCategory,
  viewType: ViewType,
  setIsSearching: (_value: boolean) => void,
  setSearchResult: (_value: CategorySearchResult | null) => void,
  setIsAuthenticated: (_value: boolean) => void,
  setBusinesses: (_value: (BusinessProfilePublicData | BusinessCardData)[]) => void,
  setHasMore: (_value: boolean) => void,
  setTotalCount: (_value: number) => void,
  setCurrentPage: (_value: number) => void,
  setSortBy: (_value: BusinessSortBy) => void,
  setSearchError: (_value: string | null) => void,
  businesses: (BusinessProfilePublicData | BusinessCardData)[],
  sortBy: BusinessSortBy,
  locationInfo?: { state?: string | null; city?: string | null; pincode?: string | null; locality?: string | null }
) {
  const searchParams = useSearchParams();
  const [isPending, startSearchTransition] = useTransition();

  // Handle business sort change
  const handleBusinessSortChange = (newSortBy: BusinessSortBy) => {
    if (newSortBy === sortBy) return;

    // Update URL params
    const url = new URL(window.location.href);
    url.searchParams.set(BUSINESS_SORT_PARAM, newSortBy);
    window.history.pushState({}, "", url.toString());

    // Update state
    setSortBy(newSortBy);

    // Only proceed if we're in the cards view
    if (viewType !== "cards") return;

    // Set loading state
    setIsSearching(true);

    // Get current search params
    const currentSearchTerm = searchParams.get(BUSINESS_NAME_PARAM);
    const pincode = searchParams.get(PINCODE_PARAM);
    const locality = searchParams.get(LOCALITY_PARAM);

    startSearchTransition(async () => {
      try {
        // Add a small delay to prevent race conditions
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Log the pincode value being used
        console.log(`handleBusinessSortChange using pincode: ${pincode}, from locationInfo: ${locationInfo?.pincode}`);

        // Use pincode from locationInfo if it's not in the URL params
        const effectivePincode = pincode || locationInfo?.pincode || null;

        const result = await searchCategoryCombined({
          categoryName: category.name,
          businessName: currentSearchTerm,
          state: locationInfo?.state || null, // Include state from locationInfo
          city: locationInfo?.city || null, // Include city from locationInfo
          pincode: effectivePincode, // Use the effective pincode
          locality,
          viewType,
          page: 1,
          limit: CATEGORY_BUSINESSES_PER_PAGE,
          businessSort: newSortBy,
        });

        if (result.data) {
          setSearchResult(result.data);
          setIsAuthenticated(result.data.isAuthenticated);
          setBusinesses(result.data.businesses || []);
          setHasMore(result.data.hasMore);
          setTotalCount(result.data.totalCount);
          setCurrentPage(result.data.nextPage ? 2 : 1);
        } else if (result.error) {
          setSearchError(result.error);
        }
      } catch (error) {
        setSearchError("An error occurred while sorting businesses.");
        console.error("Error sorting businesses:", error);
      } finally {
        setIsSearching(false);
      }
    });
  };

  // Handle business search
  const handleBusinessSearch = (searchTerm: string) => {
    // Only proceed if we're in the cards view
    if (viewType !== "cards") return;

    // Update URL params
    const url = new URL(window.location.href);
    if (searchTerm) url.searchParams.set(BUSINESS_NAME_PARAM, searchTerm);
    else url.searchParams.delete(BUSINESS_NAME_PARAM);
    window.history.pushState({}, "", url.toString());

    // Set loading state
    setIsSearching(true);

    // Get current search params
    const pincode = searchParams.get(PINCODE_PARAM);
    const locality = searchParams.get(LOCALITY_PARAM);

    startSearchTransition(async () => {
      // Add a small delay to prevent race conditions
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Log the pincode value being used
      console.log(`handleBusinessSearch using pincode: ${pincode}, from locationInfo: ${locationInfo?.pincode}`);

      // Use pincode from locationInfo if it's not in the URL params
      const effectivePincode = pincode || locationInfo?.pincode || null;

      const result = await searchCategoryCombined({
        categoryName: category.name,
        businessName: searchTerm,
        state: locationInfo?.state || null, // Include state from locationInfo
        city: locationInfo?.city || null, // Include city from locationInfo
        pincode: effectivePincode, // Use the effective pincode
        locality,
        viewType,
        page: 1,
        limit: CATEGORY_BUSINESSES_PER_PAGE,
        businessSort: sortBy,
      });

      if (result.data) {
        setSearchResult(result.data);
        setIsAuthenticated(result.data.isAuthenticated);

        if (result.data.businesses) {
          setBusinesses(result.data.businesses);
          setHasMore(result.data.hasMore);
          setTotalCount(result.data.totalCount);
          setCurrentPage(result.data.nextPage ? 2 : 1);
        }
      } else if (result.error) {
        setSearchError(result.error);
      }

      setIsSearching(false);
    });
  };

  // Load more businesses
  const loadMoreBusinesses = async () => {
    if (!businesses.length) return;

    const pincode = searchParams.get(PINCODE_PARAM);
    const locality = searchParams.get(LOCALITY_PARAM);
    const businessName = searchParams.get(BUSINESS_NAME_PARAM);

    try {
      // Use values from locationInfo if they're not in the URL params
      const effectivePincode = pincode || locationInfo?.pincode || undefined;
      const effectiveCity = locationInfo?.city || undefined;

      // Extract state, city, pincode, and locality slugs from the URL
      const urlParts = window.location.pathname.split('/').filter(part => part.trim() !== '');

      // Find the index of the category slug
      const categoryIndex = urlParts.findIndex(part => part === category.slug);

      // Get stateSlug, citySlug, and localitySlug from URL if available
      let stateSlugFromUrl = null;
      let citySlugFromUrl = null;
      let _pincodeFromUrl = null;
      let localitySlugFromUrl = null;

      if (categoryIndex >= 0 && categoryIndex + 1 < urlParts.length) {
          stateSlugFromUrl = urlParts[categoryIndex + 1];
      }

      if (categoryIndex >= 0 && categoryIndex + 2 < urlParts.length) {
          citySlugFromUrl = urlParts[categoryIndex + 2];
      }

      // Check for pincode (needed to extract locality slug)
      if (categoryIndex >= 0 && categoryIndex + 3 < urlParts.length) {
          _pincodeFromUrl = urlParts[categoryIndex + 3];
      }

      // Check for locality slug
      if (categoryIndex >= 0 && categoryIndex + 4 < urlParts.length) {
          localitySlugFromUrl = urlParts[categoryIndex + 4];
      }



      const result = await fetchMoreBusinessCardsByCategoryCombined({
        categoryName: category.name,
        page: businesses.length > 0 ? Math.ceil(businesses.length / CATEGORY_BUSINESSES_PER_PAGE) + 1 : 1,
        limit: CATEGORY_BUSINESSES_PER_PAGE,
        sortBy,
        state: locationInfo?.state || undefined, // Include state from locationInfo
        city: effectiveCity, // Include city from locationInfo
        pincode: effectivePincode, // Use the effective pincode
        locality: locality || undefined,
        stateSlug: stateSlugFromUrl, // Pass state slug from URL
        citySlug: citySlugFromUrl, // Pass city slug from URL
        localitySlug: localitySlugFromUrl, // Pass locality slug from URL
        businessName: businessName || undefined,
      });

      if (result.data) {
        setBusinesses([...businesses, ...(result.data.businesses || [])]);
        setHasMore(result.data.hasMore);
        setCurrentPage(result.data.nextPage || 1);
      } else if (result.error) {
        setSearchError(result.error);
      }
    } catch (error) {
      console.error("Error loading more businesses:", error);
      setSearchError("Failed to load more businesses.");
    }
  };

  return {
    isPending,
    handleBusinessSortChange,
    handleBusinessSearch,
    loadMoreBusinesses,
  };
}
