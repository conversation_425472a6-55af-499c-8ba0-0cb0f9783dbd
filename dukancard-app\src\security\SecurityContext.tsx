import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Alert, Platform } from 'react-native';
import <PERSON><PERSON><PERSON>onkey from 'jail-monkey';
import RNExitApp from 'react-native-exit-app';

interface SecurityContextType {
  isSecure: boolean;
  isInitialized: boolean;
  checkSecurity: () => Promise<boolean>;
}

const SecurityContext = createContext<SecurityContextType | undefined>(undefined);

interface SecurityProviderProps {
  children: ReactNode;
}

export const SecurityProvider: React.FC<SecurityProviderProps> = ({ children }) => {
  const [isSecure, setIsSecure] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    initializeSecurity();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const initializeSecurity = async () => {
    try {
      // Security initialization - API keys are now hardcoded in config
      
      // Perform security checks
      const securityResult = await performSecurityChecks();
      setIsSecure(securityResult);
      setIsInitialized(true);

      if (!securityResult && !__DEV__) {
        showSecurityAlert();
      }
    } catch (error) {
      console.warn('Security initialization failed:', error);
      setIsInitialized(true);
      if (!__DEV__) {
        showSecurityAlert();
      }
    }
  };

  const performSecurityChecks = async (): Promise<boolean> => {
    // Skip security checks in development mode
    if (__DEV__) {
      return true;
    }

    try {
      // Device security checks
      const isDebuggedMode = await JailMonkey.isDebuggedMode();
      const isJailBroken = 
        JailMonkey.isOnExternalStorage() ||
        JailMonkey.isJailBroken() ||
        JailMonkey.trustFall() ||
        isDebuggedMode ||
        JailMonkey.canMockLocation();

      // Runtime security checks
      const runtimeChecks = performRuntimeChecks();

      // Network security checks
      const networkChecks = performNetworkChecks();

      // App integrity checks
      const integrityChecks = performIntegrityChecks();

      return !isJailBroken && runtimeChecks.secure && networkChecks.secure && integrityChecks.secure;
    } catch (error) {
      console.warn('Security check failed:', error);
      return false;
    }
  };

  const performRuntimeChecks = () => {
    let secure = true;
    const issues: string[] = [];

    // Check for debugging tools
    if (typeof atob !== 'undefined' && !__DEV__) {
      secure = false;
      issues.push('Debug tools detected');
    }

    // Check for common debugging variables
    if (typeof window !== 'undefined' && (window as any).chrome && (window as any).chrome.runtime) {
      secure = false;
      issues.push('Browser debugging detected');
    }

    // Check for Flipper (React Native debugger)
    if (typeof global !== 'undefined' && (global as any).__FLIPPER__) {
      secure = false;
      issues.push('Flipper debugger detected');
    }

    // Check for Metro bundler in production
    if (!__DEV__ && typeof global !== 'undefined' && (global as any).__METRO__) {
      secure = false;
      issues.push('Metro bundler detected in production');
    }

    // Check for common reverse engineering tools
    if (typeof global !== 'undefined') {
      const suspiciousGlobals = ['__REACT_DEVTOOLS_GLOBAL_HOOK__', '__REDUX_DEVTOOLS_EXTENSION__'];
      for (const globalVar of suspiciousGlobals) {
        if ((global as any)[globalVar]) {
          secure = false;
          issues.push(`Development tool detected: ${globalVar}`);
        }
      }
    }

    return { secure, issues };
  };

  const performNetworkChecks = () => {
    let secure = true;
    const issues: string[] = [];

    // Check for proxy detection (basic)
    if (typeof navigator !== 'undefined' && (navigator as any).connection) {
      // This is a basic check - in production you might want more sophisticated detection
      const connection = (navigator as any).connection;
      if (connection.type === 'unknown' || connection.effectiveType === 'unknown') {
        // Could indicate proxy usage, but this is not definitive
      }
    }

    return { secure, issues };
  };

  const performIntegrityChecks = () => {
    let secure = true;
    const issues: string[] = [];

    // Check if running in expected environment
    if (Platform.OS !== 'ios' && Platform.OS !== 'android') {
      secure = false;
      issues.push('Unexpected platform detected');
    }

    // Check for expected React Native environment
    if (typeof global === 'undefined' || typeof require === 'undefined') {
      secure = false;
      issues.push('Unexpected JavaScript environment');
    }

    return { secure, issues };
  };

  const showSecurityAlert = () => {
    Alert.alert(
      'Security Warning',
      'This device or environment appears to be compromised or unsafe. The app will now close for security reasons.',
      [
        {
          text: 'OK',
          onPress: () => {
            // Exit the app (no sensitive data to clear since keys are hardcoded)
            RNExitApp.exitApp();
          }
        }
      ],
      { cancelable: false }
    );
  };

  const checkSecurity = async (): Promise<boolean> => {
    const result = await performSecurityChecks();
    setIsSecure(result);
    return result;
  };

  const contextValue: SecurityContextType = {
    isSecure,
    isInitialized,
    checkSecurity,
  };

  return (
    <SecurityContext.Provider value={contextValue}>
      {children}
    </SecurityContext.Provider>
  );
};

export const useSecurity = (): SecurityContextType => {
  const context = useContext(SecurityContext);
  if (context === undefined) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  return context;
};

export default SecurityContext;
