"use server";

import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { createClient } from "@/utils/supabase/server";
import { LocalitySearchResult } from "../context/types";
import { fetchBusinessesByLocalitySearch } from "./businessActions";
import { fetchProductsByLocality } from "./productActions";
import { searchLocalityData } from "./locationActions";

// Combined search action for locality pages
export async function searchLocalityCombined(params: {
  localityName: string;
  pincode: string;
  viewType: "cards" | "products";
  sortBy?: BusinessSortBy | "price_asc" | "price_desc";
  productType?: "physical" | "service" | null;
  businessName?: string;
  productName?: string;
}): Promise<{
  data?: LocalitySearchResult;
  error?: string;
}> {
  const {
    localityName,
    pincode,
    viewType,
    sortBy = "created_desc",
    productType = null,
    businessName,
    productName,
  } = params;

  // Check if locality name and pincode are provided
  if (!localityName || !pincode) {
    return { error: "Locality name and pincode are required." };
  }

  try {
    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();
    const isAuthenticated = !!session;

    // Define location object for the result
    const location = {
      type: "pincode" as const,
      value: pincode,
    };

    if (viewType === "cards") {
      // If business name is provided, use search
      if (businessName) {
        const result = await fetchBusinessesByLocalitySearch({
          localityName,
          pincode,
          businessName,
          sortBy,
        });

        if (result.error) {
          return { error: result.error };
        }

        return {
          data: {
            location,
            businesses: result.data?.businesses || [],
            isAuthenticated,
            totalCount: result.data?.businesses.length || 0,
            hasMore: result.data?.hasMore || false,
            nextPage: result.data?.nextPage || null,
          },
        };
      }

      // Otherwise, use the standard locality data search
      return searchLocalityData({
        localityName,
        pincode,
        viewType,
        sortBy,
      });
    } else {
      // viewType === "products"
      // If product name is provided, use search
      if (productName) {
        const result = await fetchProductsByLocality({
          localityName,
          pincode,
          productName,
          sortBy,
          productType,
        });

        if (result.error) {
          return { error: result.error };
        }

        return {
          data: {
            location,
            products: result.data?.products || [],
            isAuthenticated,
            totalCount: result.data?.totalCount || 0,
            hasMore: result.data?.hasMore || false,
            nextPage: result.data?.nextPage || null,
          },
        };
      }

      // Otherwise, use the standard locality data search
      return searchLocalityData({
        localityName,
        pincode,
        viewType,
        sortBy,
        productType,
      });
    }
  } catch (error) {
    console.error("Error in searchLocalityCombined:", error);
    return { error: "An unexpected error occurred. Please try again." };
  }
}
