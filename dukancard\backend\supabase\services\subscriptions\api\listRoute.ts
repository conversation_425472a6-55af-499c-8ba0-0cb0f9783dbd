import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { listSubscriptions } from "@/lib/razorpay/services/subscription";

/**
 * GET /api/subscriptions/list
 *
 * Fetches all subscriptions from Razorpay
 *
 * Query parameters:
 * - plan_id: Filter subscriptions by plan ID
 * - from: Unix timestamp from when subscriptions are to be fetched
 * - to: Unix timestamp till when subscriptions are to be fetched
 * - count: Number of subscriptions to fetch (default: 10, max: 100)
 * - skip: Number of subscriptions to skip (default: 0)
 *
 * Example response:
 * ```json
 * {
 *   "success": true,
 *   "data": {
 *     "entity": "collection",
 *     "count": 2,
 *     "items": [
 *       {
 *         "id": "sub_00000000000001",
 *         "entity": "subscription",
 *         "plan_id": "plan_00000000000001",
 *         "customer_id": "cust_D00000000000001",
 *         "status": "active",
 *         "current_start": 1577355871,
 *         "current_end": 1582655400,
 *         "ended_at": null,
 *         "quantity": 1,
 *         "notes": {
 *           "notes_key_1": "<PERSON>, <PERSON>, <PERSON>",
 *           "notes_key_2": "<PERSON>, <PERSON>… decaf."
 *         },
 *         "charge_at": 1577385991,
 *         "offer_id": "offer_JHD834hjbxzhd38d",
 *         "start_at": 1577385991,
 *         "end_at": 1603737000,
 *         "auth_attempts": 0,
 *         "total_count": 6,
 *         "paid_count": 1,
 *         "customer_notify": true,
 *         "created_at": 1577356081,
 *         "expire_by": 1577485991,
 *         "short_url": "https://rzp.io/i/z3b1R61A9",
 *         "has_scheduled_changes": false,
 *         "change_scheduled_at": null,
 *         "remaining_count": 5
 *       },
 *       // More subscriptions...
 *     ]
 *   }
 * }
 * ```
 */
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const planId = searchParams.get("plan_id");
    const from = searchParams.get("from");
    const to = searchParams.get("to");
    const count = searchParams.get("count") || "10";
    const skip = searchParams.get("skip") || "0";

    // Verify authentication using Supabase
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // For now, we don't have admin roles, so we'll just allow all authenticated users
    // to list subscriptions. In a production environment, you might want to
    // restrict this to admin users only.

    // In a production environment, you might want to check if the user
    // has admin privileges before allowing them to list all subscriptions.
    // For now, we'll just allow all authenticated users to access this endpoint.

    // Build query parameters
    const queryParams: Record<string, string | number> = {};

    if (planId) queryParams.plan_id = planId;
    if (from) queryParams.from = parseInt(from);
    if (to) queryParams.to = parseInt(to);

    // Ensure count is within limits (1-100)
    queryParams.count = Math.min(Math.max(parseInt(count), 1), 100);
    queryParams.skip = parseInt(skip);

    // Fetch subscriptions from Razorpay
    const result = await listSubscriptions(queryParams);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    // Return the subscriptions
    return NextResponse.json(
      { success: true, data: result.data },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error listing subscriptions:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 }
    );
  }
}
