"use client";

import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import BusinessCardPreview from "@/app/(dashboard)/dashboard/business/card/components/BusinessCardPreview";
import EnhancedPublicCardActions from "@/app/components/EnhancedPublicCardActions";
import FloatingInteractionButtons from "@/app/components/FloatingInteractionButtons";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import { cardVariants } from "./animations";

// Define the BusinessProfile type
type BusinessProfile = BusinessCardData & {
  total_reviews?: number;
  subscription_status?: string;
  has_active_subscription?: boolean;
  trial_end_date?: Date | string | null;
};

interface EnhancedBusinessCardSectionProps {
  businessProfile: BusinessProfile;
  isAuthenticated: boolean;
  userPlan?: "free" | "basic" | "growth" | "pro" | "enterprise" | "trial" | undefined;
  totalLikes: number;
  totalSubscriptions: number;
  averageRating: number;
  isSubscribed: boolean;
  hasLiked: boolean;
  isLoadingInteraction: boolean;
  onSubscribe: () => void;
  onUnsubscribe: () => void;
  onLike: () => void;
  onUnlike: () => void;
  onReviewClick?: () => void;
  isOwnBusiness?: boolean;
  isCurrentUserBusiness?: boolean;
}

export default function EnhancedBusinessCardSection({
  businessProfile,
  isAuthenticated,
  userPlan,
  totalLikes,
  totalSubscriptions,
  averageRating,
  isSubscribed,
  hasLiked,
  isLoadingInteraction,
  onSubscribe,
  onUnsubscribe,
  onLike,
  onUnlike,
  onReviewClick,
  isOwnBusiness = false,
  isCurrentUserBusiness = false,
}: EnhancedBusinessCardSectionProps) {
  const cardRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(cardRef, { once: false, amount: 0.2 });

  // Helper function to format the address from business profile data
  const formatAddress = (profile: BusinessProfile): string => {
    const addressParts = [
      profile.address_line,
      profile.locality,
      profile.city,
      profile.state,
      profile.pincode,
    ].filter(Boolean);

    return addressParts.join(", ");
  };

  return (
    <>
      <motion.div
        ref={cardRef}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        variants={cardVariants}
        className="transition-all duration-300"
      >
        {/* Card container with relative positioning for floating buttons */}
        <div className="max-w-[350px] w-full mx-auto relative">
          {/* Business Card Preview */}
          <div className="relative overflow-visible">
            <BusinessCardPreview
              data={businessProfile}
              isAuthenticated={isAuthenticated}
              userPlan={userPlan}
              totalLikes={totalLikes}
              totalSubscriptions={totalSubscriptions}
              averageRating={averageRating}
              isSubscribed={isSubscribed}
              hasLiked={hasLiked}
              isLoadingInteraction={isLoadingInteraction}
              isCurrentUserBusiness={isCurrentUserBusiness}
            />

            {/* Desktop/Tablet Floating Interaction Buttons - positioned relative to the card only */}
            <div className="hidden sm:block">
              <FloatingInteractionButtons
                hasLiked={hasLiked}
                isSubscribed={isSubscribed}
                isLoadingInteraction={isLoadingInteraction}
                onLike={onLike}
                onUnlike={onUnlike}
                onSubscribe={onSubscribe}
                onUnsubscribe={onUnsubscribe}
                isAuthenticated={isAuthenticated}
                isOwnBusiness={isOwnBusiness}
                isCurrentUserBusiness={isCurrentUserBusiness}
                themeColor={businessProfile.theme_color}
                onReviewClick={onReviewClick}
                businessSlug={businessProfile.business_slug}
                businessName={businessProfile.business_name}
              />
            </div>
          </div>

          {/* Mobile Interaction Buttons - positioned below the card */}
          <div className="block sm:hidden mt-4 mb-2 w-full">
            <FloatingInteractionButtons
              hasLiked={hasLiked}
              isSubscribed={isSubscribed}
              isLoadingInteraction={isLoadingInteraction}
              onLike={onLike}
              onUnlike={onUnlike}
              onSubscribe={onSubscribe}
              onUnsubscribe={onUnsubscribe}
              isAuthenticated={isAuthenticated}
              isOwnBusiness={isOwnBusiness}
              isCurrentUserBusiness={isCurrentUserBusiness}
              themeColor={businessProfile.theme_color}
              onReviewClick={onReviewClick}
              businessSlug={businessProfile.business_slug}
              businessName={businessProfile.business_name}
            />
          </div>

          {/* QR Code Download and Share Buttons - separate from card */}
          <div className="mt-6">
            <EnhancedPublicCardActions
              businessSlug={businessProfile.business_slug || ""}
              businessName={businessProfile.business_name || ""}
              ownerName={businessProfile.member_name || ""}
              businessAddress={formatAddress(businessProfile)}
              themeColor={businessProfile.theme_color || "#F59E0B"}
            />
          </div>
        </div>
      </motion.div>
    </>
  );
}
