import React from 'react';
import { View, Text, TouchableOpacity, Alert, Linking, Image } from 'react-native';
import QRCode from 'react-qr-code';
import { QrCode, User } from 'lucide-react-native';
import { generateDukancardUrl } from '../../lib/utils/qrCodeUtils';

interface QRCodeDisplayProps {
  businessSlug: string;
  businessName?: string;
  businessLogo?: string | null;
  isDark?: boolean;
  size?: number;
  showUrl?: boolean;
  containerStyle?: any;
}

export default function QRCodeDisplay({
  businessSlug,
  businessName = '',
  businessLogo,
  isDark = false,
  size = 60,
  showUrl = true,
  containerStyle,
}: QRCodeDisplayProps) {
  if (!businessSlug) {
    return (
      <View style={[{
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#F59E0B', // Yellow background
        padding: 20,
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
        elevation: 5,
      }, containerStyle]}>
        {/* Left side - Business Profile */}
        <View style={{ flex: 1, paddingRight: 16 }}>
          {/* Business Logo */}
          <View style={{
            width: 60,
            height: 60,
            borderRadius: 30,
            backgroundColor: '#fff',
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 12,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
          }}>
            {businessLogo ? (
              <Image
                source={{ uri: businessLogo }}
                style={{
                  width: 56,
                  height: 56,
                  borderRadius: 28,
                }}
                resizeMode="cover"
              />
            ) : (
              <User size={32} color="#6B7280" />
            )}
          </View>

          {/* Business Name */}
          <Text style={{
            fontSize: 18,
            fontWeight: 'bold',
            color: '#000',
            marginBottom: 4,
          }}>
            {businessName || 'Business Name'}
          </Text>

          {/* Status Message */}
          <Text style={{
            fontSize: 14,
            color: '#374151',
            opacity: 0.8,
          }}>
            Set Slug to activate QR
          </Text>
        </View>

        {/* Right side - QR Code */}
        <View style={{
          backgroundColor: '#fff',
          padding: 12,
          borderRadius: 12,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 3,
          opacity: 0.5,
        }}>
          <QrCode
            color="#374151"
            size={80}
            strokeWidth={1.5}
          />
        </View>
      </View>
    );
  }

  const qrValue = generateDukancardUrl(businessSlug);
  const displayUrl = `dukancard.in/${businessSlug}`;

  const handleUrlPress = () => {
    Alert.alert(
      'Open Profile',
      `Would you like to open ${businessName || 'this business'} profile in your browser?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Open',
          onPress: () => {
            Linking.openURL(qrValue).catch(() => {
              Alert.alert('Error', 'Unable to open the URL');
            });
          },
        },
      ]
    );
  };

  return (
    <View style={[{
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#F59E0B', // Yellow background
      padding: 20,
      borderRadius: 12,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 5,
    }, containerStyle]}>
      {/* Left side - Business Profile */}
      <View style={{ flex: 1, paddingRight: 16 }}>
        {/* Business Logo */}
        <View style={{
          width: 60,
          height: 60,
          borderRadius: 30,
          backgroundColor: '#fff',
          justifyContent: 'center',
          alignItems: 'center',
          marginBottom: 12,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 3,
        }}>
          {businessLogo ? (
            <Image
              source={{ uri: businessLogo }}
              style={{
                width: 56,
                height: 56,
                borderRadius: 28,
              }}
              resizeMode="cover"
            />
          ) : (
            <User size={32} color="#6B7280" />
          )}
        </View>

        {/* Business Name */}
        <Text style={{
          fontSize: 18,
          fontWeight: 'bold',
          color: '#000',
          marginBottom: 4,
        }}>
          {businessName || 'Business Name'}
        </Text>

        {/* URL below business name */}
        {showUrl && (
          <TouchableOpacity onPress={handleUrlPress} activeOpacity={0.7}>
            <Text style={{
              fontSize: 14,
              fontFamily: 'monospace',
              color: '#374151',
              fontWeight: '600',
            }}>
              {displayUrl}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Right side - QR Code */}
      <View style={{
        backgroundColor: '#fff',
        padding: 12,
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }}>
        <QRCode
          value={qrValue}
          size={100}
          style={{ height: "auto", maxWidth: "100%", width: "100%" }}
          viewBox="0 0 100 100"
          fgColor="#000000"
          bgColor="#FFFFFF"
        />
      </View>
    </View>
  );
}
