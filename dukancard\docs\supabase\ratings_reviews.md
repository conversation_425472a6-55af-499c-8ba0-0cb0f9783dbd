# Ratings and Reviews Table Documentation

## Table Overview

The `ratings_reviews` table in the Dukancard application stores user ratings and reviews for business profiles. It allows users to provide numerical ratings (1-5 stars) and optional text reviews, enabling a feedback system for businesses and helping other users make informed decisions.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key for the rating/review record |
| user_id | uuid | NO | | Foreign key to users.id, the user who submitted the rating/review |
| business_profile_id | uuid | NO | | Foreign key to business_profiles.id, the business being rated/reviewed |
| rating | smallint | NO | | Numerical rating from 1 to 5 |
| review_text | text | YES | | Optional text review accompanying the rating |
| created_at | timestamptz | NO | timezone('utc'::text, now()) | Timestamp when the rating/review was created |
| updated_at | timestamptz | NO | timezone('utc'::text, now()) | Timestamp when the rating/review was last updated |

## Constraints

### Primary Key
- `ratings_reviews_pkey` - Primary key constraint on the `id` column

### Foreign Keys
- `ratings_reviews_user_id_fkey` - Foreign key constraint linking `user_id` to `users.id`
- `ratings_reviews_business_profile_id_fkey` - Foreign key constraint linking `business_profile_id` to `business_profiles.id`

### Unique Constraints
- `ratings_reviews_user_business_profile_unique` - Ensures a user can only submit one rating/review per business
  - Columns: `user_id`, `business_profile_id`

### Check Constraints
- `ratings_reviews_rating_check` - Ensures rating values are between 1 and 5
  ```sql
  CHECK ((rating >= 1) AND (rating <= 5))
  ```

### Not Null Constraints
Multiple check constraints ensure that required fields are not null.

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| ratings_reviews_pkey | UNIQUE | id | Primary key index |
| ratings_reviews_user_business_profile_unique | UNIQUE | user_id, business_profile_id | Ensures unique combination of user and business |

## Triggers

### trigger_add_rating_activity
- **Events**: INSERT, UPDATE
- **Function**: add_rating_activity()
- **Description**: Creates a record in the business_activities table when a new rating is added or an existing rating is updated

### handle_new_review
- **Event**: INSERT
- **Function**: update_average_rating()
- **Description**: Updates the average_rating in the business_profiles table when a new rating is added

### handle_updated_review
- **Event**: UPDATE
- **Function**: update_average_rating()
- **Description**: Updates the average_rating in the business_profiles table when a rating is updated

### handle_deleted_review
- **Event**: DELETE
- **Function**: update_average_rating()
- **Description**: Updates the average_rating in the business_profiles table when a rating is deleted

### Trigger Function Definitions

#### add_rating_activity()

```sql
CREATE OR REPLACE FUNCTION public.add_rating_activity()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
BEGIN
  -- Check if this is an update or insert
  IF TG_OP = 'UPDATE' THEN
    -- For updates, only add activity if rating changed
    IF NEW.rating = OLD.rating THEN
      RETURN NEW;
    END IF;
  END IF;
  
  -- Insert a new activity record
  INSERT INTO business_activities (
    business_profile_id,
    user_id,
    activity_type,
    rating_value,
    created_at
  ) VALUES (
    NEW.business_profile_id,
    NEW.user_id,
    'rating',
    NEW.rating,
    NEW.updated_at
  );
  
  RETURN NEW;
END;
$function$
```

#### update_average_rating()

```sql
CREATE OR REPLACE FUNCTION public.update_average_rating()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  avg_rating numeric;
  profile_id uuid;
BEGIN
  -- Determine the business_profile_id based on the operation type
  IF (TG_OP = 'DELETE') THEN
    profile_id := OLD.business_profile_id;
  ELSE
    profile_id := NEW.business_profile_id;
  END IF;

  -- Calculate the new average rating for the specific business profile
  SELECT AVG(rating)::numeric(2,1) INTO avg_rating
  FROM public.ratings_reviews
  WHERE business_profile_id = profile_id;

  -- Update the business_profiles table
  UPDATE public.business_profiles
  SET average_rating = COALESCE(avg_rating, 0.0) -- Set to 0.0 if no ratings exist
  WHERE id = profile_id;

  RETURN NULL; -- Result is ignored since this is an AFTER trigger
END;
$function$
```

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| Allow public read access for ratings and reviews | SELECT | true | |
| Allow authenticated users to insert their own rating/review | INSERT | | |
| Allow users to update their own rating/review | UPDATE | (auth.role() = 'authenticated' AND user_id = auth.uid()) | |
| Allow users to delete their own rating/review | DELETE | (auth.role() = 'authenticated' AND user_id = auth.uid()) | |

These policies ensure that:
1. Anyone can view ratings and reviews (public read access)
2. Authenticated users can add ratings and reviews
3. Users can only update or delete their own ratings and reviews

## Related Tables

### users
The `users` table is referenced by the `user_id` foreign key and contains information about the user who submitted the rating/review.

### business_profiles
The `business_profiles` table is referenced by the `business_profile_id` foreign key and contains information about the business being rated/reviewed. The `average_rating` field in this table is automatically updated by triggers on the `ratings_reviews` table.

### business_activities
The `business_activities` table receives new records via the `add_rating_activity` trigger when a rating is created or updated, tracking this as a business activity.

## Usage Notes

1. **Rating System**:
   - The table implements a 5-star rating system (1-5)
   - Each record represents one user rating one business
   - The unique constraint ensures a user can only submit one rating per business
   - Users can update their ratings, which will recalculate the business's average rating

2. **Review Text**:
   - The `review_text` field is optional, allowing users to provide a rating without a text review
   - When provided, it offers qualitative feedback alongside the numerical rating

3. **Business Metrics**:
   - The `update_average_rating` trigger maintains an accurate average rating in the `business_profiles.average_rating` field
   - This enables efficient display of average ratings without needing to query and calculate from the ratings_reviews table

4. **Activity Tracking**:
   - The `add_rating_activity` trigger creates a record in the `business_activities` table
   - This allows business owners to see notifications about new or updated ratings
   - For updates, an activity is only created if the rating value changes

5. **Security**:
   - RLS policies ensure that while anyone can view ratings and reviews, only authenticated users can create them
   - Users can only update or delete their own ratings and reviews, not those created by others

6. **Performance Considerations**:
   - The composite index on `user_id` and `business_profile_id` supports efficient lookups
   - The triggers maintain denormalized average ratings for performance optimization

7. **User Experience Flow**:
   - When a user rates a business, a new record is created
   - The business owner receives a notification via the business_activities table
   - The business profile's average rating is automatically updated
   - If a user tries to rate the same business again, the application should update the existing record

8. **Data Relationships**:
   - Each rating/review is associated with exactly one user and one business profile
   - The relationship between users and business profiles through ratings is many-to-many, but constrained to one rating per user-business pair
