/**
 * Development Error Monitoring
 * Enhanced error tracking and debugging tools for development environment
 */

import Constants from 'expo-constants';
import { errorTracker } from './errorTracking';

interface DevError {
  message: string;
  stack?: string;
  component?: string;
  props?: any;
  timestamp: string;
}

class DevelopmentErrorMonitoring {
  private isDevelopment: boolean;
  private devErrors: DevError[] = [];
  private maxDevErrors = 100;

  constructor() {
    this.isDevelopment = __DEV__ || Constants.expoConfig?.extra?.appEnv === 'development';
    
    if (this.isDevelopment) {
      this.setupDevelopmentErrorHandlers();
      this.setupConsoleOverrides();
    }
  }

  /**
   * Set up development-specific error handlers
   */
  private setupDevelopmentErrorHandlers() {
    // Enhanced console logging for development
    const originalConsoleError = console.error;
    console.error = (...args: any[]) => {
      // Log to our development tracker
      this.logDevError({
        message: args.map(arg => 
          typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' '),
        timestamp: new Date().toISOString(),
      });
      
      // Call original console.error
      originalConsoleError.apply(console, args);
    };

    // Enhanced console warning for development
    const originalConsoleWarn = console.warn;
    console.warn = (...args: any[]) => {
      // Log warnings in development for debugging
      this.logDevError({
        message: `[WARNING] ${args.map(arg => 
          typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ')}`,
        timestamp: new Date().toISOString(),
      });
      
      // Call original console.warn
      originalConsoleWarn.apply(console, args);
    };
  }

  /**
   * Set up console overrides for better development debugging
   */
  private setupConsoleOverrides() {
    // Add development-specific console methods
    (console as any).devLog = (...args: any[]) => {
      if (this.isDevelopment) {
        console.log('🔧 [DEV]', ...args);
      }
    };

    (console as any).devError = (...args: any[]) => {
      if (this.isDevelopment) {
        console.error('🚨 [DEV ERROR]', ...args);
        this.logDevError({
          message: `[DEV ERROR] ${args.join(' ')}`,
          timestamp: new Date().toISOString(),
        });
      }
    };

    (console as any).devWarn = (...args: any[]) => {
      if (this.isDevelopment) {
        console.warn('⚠️ [DEV WARNING]', ...args);
      }
    };
  }

  /**
   * Log development-specific errors
   */
  private logDevError(error: DevError) {
    if (!this.isDevelopment) return;

    this.devErrors.push(error);
    
    // Keep only recent errors
    if (this.devErrors.length > this.maxDevErrors) {
      this.devErrors = this.devErrors.slice(-this.maxDevErrors);
    }
  }

  /**
   * Log React component errors with context
   */
  logComponentError(error: Error, errorInfo: any, componentName?: string) {
    if (!this.isDevelopment) return;

    const devError: DevError = {
      message: `Component Error in ${componentName || 'Unknown'}: ${error.message}`,
      stack: error.stack,
      component: componentName,
      props: errorInfo,
      timestamp: new Date().toISOString(),
    };

    this.logDevError(devError);
    
    // Also log to main error tracker
    errorTracker.logError(error, {
      context: 'React Component',
      componentName,
      ...errorInfo,
    });

    // Enhanced console output for development
    console.group(`🚨 Component Error: ${componentName || 'Unknown'}`);
    console.error('Error:', error);
    console.error('Component Stack:', errorInfo.componentStack);
    console.error('Props:', errorInfo);
    console.groupEnd();
  }

  /**
   * Log navigation errors
   */
  logNavigationError(error: Error, route?: string) {
    if (!this.isDevelopment) return;

    const devError: DevError = {
      message: `Navigation Error${route ? ` on route ${route}` : ''}: ${error.message}`,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    };

    this.logDevError(devError);
    console.error(`🧭 Navigation Error${route ? ` [${route}]` : ''}:`, error);
  }

  /**
   * Log API errors with request details
   */
  logApiError(error: Error, endpoint?: string, requestData?: any) {
    if (!this.isDevelopment) return;

    const devError: DevError = {
      message: `API Error${endpoint ? ` at ${endpoint}` : ''}: ${error.message}`,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    };

    this.logDevError(devError);
    
    console.group(`🌐 API Error${endpoint ? ` [${endpoint}]` : ''}`);
    console.error('Error:', error);
    if (requestData) console.error('Request Data:', requestData);
    console.groupEnd();
  }

  /**
   * Get all development errors for debugging
   */
  getDevErrors(): DevError[] {
    return [...this.devErrors];
  }

  /**
   * Clear development errors
   */
  clearDevErrors() {
    this.devErrors = [];
  }

  /**
   * Print development error summary
   */
  printErrorSummary() {
    if (!this.isDevelopment || this.devErrors.length === 0) return;

    console.group('📊 Development Error Summary');
    console.log(`Total errors: ${this.devErrors.length}`);
    
    // Group by error type
    const errorTypes = this.devErrors.reduce((acc, error) => {
      const type = error.component ? 'Component' : 
                   error.message.includes('API') ? 'API' : 
                   error.message.includes('Navigation') ? 'Navigation' : 'Other';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.table(errorTypes);
    console.groupEnd();
  }

  /**
   * Check if development mode is active
   */
  isDevelopmentMode(): boolean {
    return this.isDevelopment;
  }
}

// Create singleton instance
export const devErrorMonitor = new DevelopmentErrorMonitoring();

// Export types
export type { DevError };
