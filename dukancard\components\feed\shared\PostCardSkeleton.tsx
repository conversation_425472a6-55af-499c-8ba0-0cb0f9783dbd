'use client';

import { motion } from 'framer-motion';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface PostCardSkeletonProps {
  index?: number;
  showImage?: boolean;
  showProducts?: boolean;
}

export default function PostCardSkeleton({ 
  index = 0, 
  showImage = true, 
  showProducts = false 
}: PostCardSkeletonProps) {
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        delay: index * 0.1,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      className={cn(
        "bg-white dark:bg-black rounded-xl border border-neutral-200 dark:border-neutral-800",
        "shadow-sm overflow-hidden mb-4 md:mb-6"
      )}
    >
      {/* Header Skeleton */}
      <div className="p-4 pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 flex-1">
            {/* Avatar Skeleton */}
            <Skeleton className="h-12 w-12 rounded-full" />
            
            <div className="flex-1 space-y-2">
              {/* Business Name */}
              <Skeleton className="h-4 w-32" />
              
              {/* Location and Time */}
              <div className="flex items-center space-x-2">
                <Skeleton className="h-3 w-24" />
                <Skeleton className="h-3 w-1 rounded-full" />
                <Skeleton className="h-3 w-16" />
              </div>
            </div>
          </div>
          
          {/* More Button */}
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
      </div>

      {/* Content Skeleton */}
      <div className="px-4 pb-3 space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
      </div>

      {/* Image Skeleton */}
      {showImage && (
        <div className="relative w-full">
          <Skeleton className="w-full aspect-[4/3]" />
        </div>
      )}

      {/* Products Skeleton */}
      {showProducts && (
        <div className="px-4 pt-4 space-y-3">
          {/* Products Header */}
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-6 rounded-lg" />
            <Skeleton className="h-4 w-32" />
          </div>
          
          {/* Products Grid */}
          <div className="grid grid-cols-2 gap-3">
            {Array.from({ length: 2 }).map((_, i) => (
              <div key={i} className="bg-neutral-50 dark:bg-neutral-900 rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden">
                <Skeleton className="w-full aspect-square" />
                <div className="p-3 space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-16" />
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Actions Skeleton */}
      <div className="p-4 pt-3">
        <div className="flex gap-3">
          <Skeleton className="h-10 flex-1" />
          <Skeleton className="h-10 flex-1" />
        </div>
      </div>
    </motion.div>
  );
}
