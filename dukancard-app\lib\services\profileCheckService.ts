/**
 * Profile Check Service for React Native
 * 
 * Handles profile existence checks using direct Supabase client calls
 * with Row Level Security (RLS) policies for security.
 * 
 * This service replaces the need for Next.js proxy API routes by leveraging:
 * - RLS policies for security (users can only check their own profiles)
 * - Public read access for profile discovery
 * - Direct Supabase client calls for better performance
 */

import { supabase } from '@/lib/supabase';

// Types for profile check operations
export interface ProfileCheckResult {
  exists: boolean;
  data?: any;
}

export interface ServiceResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Check if the current user has a specific type of profile
 */
export async function checkProfileExists(
  type: 'customer' | 'business'
): Promise<ServiceResult<ProfileCheckResult>> {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return { success: false, error: 'Authentication required' };
    }

    const tableName = type === 'customer' ? 'customer_profiles' : 'business_profiles';

    // Check if profile exists for the authenticated user
    const { data: profile, error } = await supabase
      .from(tableName)
      .select('*')
      .eq('id', user.id)
      .maybeSingle();

    if (error) {
      console.error(`[PROFILE_CHECK_SERVICE] Error checking ${type} profile:`, error);
      return { success: false, error: `Failed to check ${type} profile` };
    }

    const result: ProfileCheckResult = {
      exists: !!profile,
      data: profile || undefined,
    };

    return { success: true, data: result };
  } catch (error) {
    console.error('[PROFILE_CHECK_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Check if the current user has a customer profile
 */
export async function checkCustomerProfileExists(): Promise<ServiceResult<ProfileCheckResult>> {
  return checkProfileExists('customer');
}

/**
 * Check if the current user has a business profile
 */
export async function checkBusinessProfileExists(): Promise<ServiceResult<ProfileCheckResult>> {
  return checkProfileExists('business');
}

/**
 * Check both customer and business profiles for the current user
 */
export async function checkAllProfilesExist(): Promise<ServiceResult<{
  customer: ProfileCheckResult;
  business: ProfileCheckResult;
}>> {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return { success: false, error: 'Authentication required' };
    }

    // Check both profiles in parallel
    const [customerResult, businessResult] = await Promise.all([
      supabase
        .from('customer_profiles')
        .select('*')
        .eq('id', user.id)
        .maybeSingle(),
      supabase
        .from('business_profiles')
        .select('*')
        .eq('id', user.id)
        .maybeSingle()
    ]);

    if (customerResult.error) {
      console.error('[PROFILE_CHECK_SERVICE] Error checking customer profile:', customerResult.error);
      return { success: false, error: 'Failed to check customer profile' };
    }

    if (businessResult.error) {
      console.error('[PROFILE_CHECK_SERVICE] Error checking business profile:', businessResult.error);
      return { success: false, error: 'Failed to check business profile' };
    }

    const result = {
      customer: {
        exists: !!customerResult.data,
        data: customerResult.data || undefined,
      },
      business: {
        exists: !!businessResult.data,
        data: businessResult.data || undefined,
      },
    };

    return { success: true, data: result };
  } catch (error) {
    console.error('[PROFILE_CHECK_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Check if a profile exists by user ID (public read access)
 * Uses public RLS policy for read access
 */
export async function checkProfileExistsByUserId(
  userId: string,
  type: 'customer' | 'business'
): Promise<ServiceResult<ProfileCheckResult>> {
  try {
    const tableName = type === 'customer' ? 'customer_profiles' : 'business_profiles';

    // Use public read access to check if profile exists
    const { data: profile, error } = await supabase
      .from(tableName)
      .select('*')
      .eq('id', userId)
      .maybeSingle();

    if (error) {
      console.error(`[PROFILE_CHECK_SERVICE] Error checking ${type} profile by ID:`, error);
      return { success: false, error: `Failed to check ${type} profile` };
    }

    const result: ProfileCheckResult = {
      exists: !!profile,
      data: profile || undefined,
    };

    return { success: true, data: result };
  } catch (error) {
    console.error('[PROFILE_CHECK_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Check multiple profiles by user IDs (public read access)
 * Uses public RLS policy for read access
 */
export async function checkMultipleProfilesExist(
  userIds: string[],
  type: 'customer' | 'business'
): Promise<ServiceResult<{ [userId: string]: ProfileCheckResult }>> {
  try {
    if (!Array.isArray(userIds) || userIds.length === 0) {
      return { success: true, data: {} };
    }

    const tableName = type === 'customer' ? 'customer_profiles' : 'business_profiles';

    // Use public read access to check if profiles exist
    const { data: profiles, error } = await supabase
      .from(tableName)
      .select('*')
      .in('id', userIds);

    if (error) {
      console.error(`[PROFILE_CHECK_SERVICE] Error checking multiple ${type} profiles:`, error);
      return { success: false, error: `Failed to check ${type} profiles` };
    }

    // Create a map of user ID to profile check result
    const result: { [userId: string]: ProfileCheckResult } = {};
    
    userIds.forEach(userId => {
      const profile = profiles?.find(p => p.id === userId);
      result[userId] = {
        exists: !!profile,
        data: profile || undefined,
      };
    });

    return { success: true, data: result };
  } catch (error) {
    console.error('[PROFILE_CHECK_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get user role status based on profile existence
 */
export async function getUserRoleStatus(): Promise<ServiceResult<{
  hasCustomerProfile: boolean;
  hasBusinessProfile: boolean;
  customerProfile?: any;
  businessProfile?: any;
  onboardingStatus: 'none' | 'customer' | 'business' | 'complete';
  businessOnboardingCompleted: boolean;
  customerProfileComplete: boolean;
  role: 'customer' | 'business' | null;
  needsRoleSelection: boolean;
  needsOnboarding: boolean;
  needsProfileCompletion: boolean;
}>> {
  try {
    const profilesResult = await checkAllProfilesExist();
    
    if (!profilesResult.success) {
      return { success: false, error: profilesResult.error };
    }

    const { customer, business } = profilesResult.data!;
    
    // Determine onboarding status
    let onboardingStatus: 'none' | 'customer' | 'business' | 'complete' = 'none';
    if (customer.exists && business.exists) {
      onboardingStatus = 'complete';
    } else if (business.exists) {
      onboardingStatus = 'business';
    } else if (customer.exists) {
      onboardingStatus = 'customer';
    }

    // Determine primary role
    let role: 'customer' | 'business' | null = null;
    if (business.exists) {
      role = 'business';
    } else if (customer.exists) {
      role = 'customer';
    }

    // Check if profiles are complete
    const customerProfileComplete = customer.exists && 
      customer.data?.name && 
      customer.data?.email;
    
    const businessOnboardingCompleted = business.exists && 
      business.data?.business_name && 
      business.data?.business_slug &&
      business.data?.contact_email;

    // Determine what the user needs to do next
    const needsRoleSelection = !customer.exists && !business.exists;
    const needsOnboarding = (customer.exists && !customerProfileComplete) || 
                           (business.exists && !businessOnboardingCompleted);
    const needsProfileCompletion = needsOnboarding;

    const result = {
      hasCustomerProfile: customer.exists,
      hasBusinessProfile: business.exists,
      customerProfile: customer.data,
      businessProfile: business.data,
      onboardingStatus,
      businessOnboardingCompleted,
      customerProfileComplete,
      role,
      needsRoleSelection,
      needsOnboarding,
      needsProfileCompletion,
    };

    return { success: true, data: result };
  } catch (error) {
    console.error('[PROFILE_CHECK_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}
