"use client";

import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { X, ZoomIn, ZoomOut } from "lucide-react";
import { motion, AnimatePresence, useMotionValue } from "framer-motion";
import { usePinchZoom } from "../hooks/usePinchZoom";

interface ImageZoomModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  altText: string;
}

export default function ImageZoomModal({
  isOpen,
  onClose,
  imageUrl,
  altText,
}: ImageZoomModalProps) {
  const [scale, setScale] = useState(1);
  const [loading, setLoading] = useState(true);
  const imageContainerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);
  const [dragConstraints, setDragConstraints] = useState({ top: 0, right: 0, bottom: 0, left: 0 });

  // For panning the image
  const x = useMotionValue(0);
  const y = useMotionValue(0);

  // Use our custom pinch zoom hook for mobile devices
  usePinchZoom(imageRef, setScale, scale, {
    minScale: 0.5,
    maxScale: 3,
    scaleStep: 0.1
  });

  // Update drag constraints when scale changes
  useEffect(() => {
    if (!isOpen) return;

    // Use requestAnimationFrame to ensure DOM is fully updated
    const updateConstraints = () => {
      if (scale > 1 && imageRef.current && imageContainerRef.current) {
        const containerRect = imageContainerRef.current.getBoundingClientRect();
        const imageRect = imageRef.current.getBoundingClientRect();

        // Calculate the actual scaled dimensions
        const scaledWidth = imageRect.width * scale;
        const scaledHeight = imageRect.height * scale;

        // Calculate how much the image can move in each direction
        // This is half the difference between the scaled size and container size
        const horizontalConstraint = Math.max(0, (scaledWidth - containerRect.width) / 2);
        const verticalConstraint = Math.max(0, (scaledHeight - containerRect.height) / 2);

        // Set constraints with some extra padding for better experience
        setDragConstraints({
          left: -horizontalConstraint - 50,
          right: horizontalConstraint + 50,
          top: -verticalConstraint - 50,
          bottom: verticalConstraint + 50
        });
      } else {
        // Reset constraints and position when not zoomed
        setDragConstraints({ top: 0, right: 0, bottom: 0, left: 0 });
        x.set(0);
        y.set(0);
      }
    };

    // Initial update
    updateConstraints();

    // Update again after a short delay to ensure all measurements are accurate
    const timeoutId = setTimeout(updateConstraints, 100);

    return () => clearTimeout(timeoutId);
  }, [scale, isOpen, x, y]);

  // Reset scale and position when modal opens
  useEffect(() => {
    if (isOpen) {
      setScale(1);
      setLoading(true);
      x.set(0);
      y.set(0);
    }
  }, [isOpen, x, y]);

  // Progressively recenter image as user zooms out
  useEffect(() => {
    // Only apply when scale is between 1 and 2
    if (scale > 1 && scale < 2) {
      // Calculate how much to recenter based on current scale
      // As scale approaches 1, centeringFactor approaches 1 (full centering)
      const centeringFactor = 1 - (scale - 1);

      // Get current position values
      const currentX = x.get();
      const currentY = y.get();

      // Calculate new positions with progressive centering
      // The closer to scale 1, the closer to center (0,0)
      const newX = currentX * (1 - centeringFactor);
      const newY = currentY * (1 - centeringFactor);

      // Apply the new positions with a small delay for smoother transition
      const applyProgressiveCentering = () => {
        x.set(newX);
        y.set(newY);
      };

      const timeoutId = setTimeout(applyProgressiveCentering, 10);
      return () => clearTimeout(timeoutId);
    } else if (scale <= 1) {
      // When fully zoomed out, ensure complete centering
      x.set(0);
      y.set(0);
    }
  }, [scale, x, y]);

  // Handle keyboard events (Escape to close, + to zoom in, - to zoom out)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      if (e.key === "Escape") {
        onClose();
      } else if (e.key === "+" || e.key === "=") {
        setScale((prev) => Math.min(prev + 0.25, 3));
      } else if (e.key === "-" || e.key === "_") {
        setScale((prev) => Math.max(prev - 0.25, 0.5));
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [isOpen, onClose]);

  // Handle mouse wheel for zooming
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (!isOpen || !imageContainerRef.current) return;

      e.preventDefault();

      // Get current scale before update
      const prevScale = scale;

      // Calculate new scale based on wheel direction
      let newScale;
      if (e.deltaY < 0) {
        // Scroll up - zoom in
        newScale = Math.min(prevScale + 0.1, 3);
      } else {
        // Scroll down - zoom out
        newScale = Math.max(prevScale - 0.1, 0.5);
      }

      // Update scale - the progressive centering effect will handle recentering
      setScale(newScale);
    };

    // Handle mouse down for better drag experience
    const handleMouseDown = () => {
      if (scale > 1) {
        document.body.style.cursor = 'grabbing';
      }
    };

    // Handle mouse up to reset cursor
    const handleMouseUp = () => {
      document.body.style.cursor = '';
    };

    const currentRef = imageContainerRef.current;
    if (currentRef) {
      currentRef.addEventListener('wheel', handleWheel, { passive: false });
      currentRef.addEventListener('mousedown', handleMouseDown);
      window.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      if (currentRef) {
        currentRef.removeEventListener('wheel', handleWheel);
        currentRef.removeEventListener('mousedown', handleMouseDown);
        window.removeEventListener('mouseup', handleMouseUp);
      }
      document.body.style.cursor = '';
    };
  }, [isOpen, scale]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  const zoomIn = () => setScale((prev) => Math.min(prev + 0.25, 3));
  const zoomOut = () => setScale((prev) => Math.max(prev - 0.25, 0.5));

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black p-4"
          onClick={onClose}
        >
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
            aria-label="Close"
          >
            <X className="w-6 h-6" />
          </button>

          {/* Zoom controls */}
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-10 flex items-center gap-4 bg-black/50 rounded-full p-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                zoomOut();
              }}
              className="p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
              aria-label="Zoom out"
              disabled={scale <= 0.5}
            >
              <ZoomOut className="w-5 h-5" />
            </button>
            <span className="text-white text-sm font-medium">{Math.round(scale * 100)}%</span>
            <button
              onClick={(e) => {
                e.stopPropagation();
                zoomIn();
              }}
              className="p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
              aria-label="Zoom in"
              disabled={scale >= 3}
            >
              <ZoomIn className="w-5 h-5" />
            </button>
          </div>

          {/* Image container - stop propagation to prevent closing when clicking on image */}
          <motion.div
            ref={imageContainerRef}
            className="relative w-full max-w-4xl h-[80vh] overflow-hidden touch-none"
            onClick={(e) => e.stopPropagation()}
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.9 }}
            style={{ touchAction: "none" }}
            onTouchStart={(e) => {
              // Prevent default touch behavior to avoid interference
              if (scale > 1) {
                e.stopPropagation();
              }
            }}
          >
            {loading && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-10 h-10 border-4 border-neutral-300 border-t-[var(--brand-gold)] rounded-full animate-spin"></div>
              </div>
            )}
            <div className="w-full h-full flex items-center justify-center overflow-hidden">
              <motion.div
                ref={imageRef}
                drag={scale > 1} // Enable dragging only when zoomed in
                dragConstraints={dragConstraints} // Use dynamic constraints based on scale
                dragElastic={0} // No elasticity for more direct control
                dragMomentum={false} // Disable momentum for more precise control
                dragTransition={{ power: 0.1, timeConstant: 200 }} // Smoother dragging
                style={{
                  x,
                  y,
                  scale,
                  touchAction: "none", // Disable browser's touch actions for better control
                  cursor: scale > 1 ? "grab" : "default"
                }}
                className="relative touch-none will-change-transform"
                whileDrag={{ cursor: "grabbing" }}
                // Handle double click to zoom in/out
                onDoubleClick={(e) => {
                  if (scale > 1) {
                    // Zoom out to 1 - progressive centering will handle recentering
                    setScale(1);
                  } else {
                    // Zoom in to 2x at the point that was clicked
                    const rect = e.currentTarget.getBoundingClientRect();
                    const offsetX = e.clientX - rect.left;
                    const offsetY = e.clientY - rect.top;

                    // Calculate center point offset
                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;

                    // Calculate the point to zoom to (relative to center)
                    const targetX = (offsetX - centerX) * 0.5;
                    const targetY = (offsetY - centerY) * 0.5;

                    // Set scale and position
                    setScale(2);
                    x.set(-targetX); // Negative because we're moving the image in the opposite direction
                    y.set(-targetY);
                  }
                }}
              >
                <div className="relative" style={{ pointerEvents: 'none' }}>
                  <Image
                    src={imageUrl}
                    alt={altText}
                    width={1200}
                    height={1800}
                    className="max-w-none object-contain select-none"
                    onLoad={() => setLoading(false)}
                    priority
                    draggable={false}
                    unoptimized={true} // Use unoptimized for better quality when zooming
                    style={{
                      userSelect: 'none',
                      WebkitUserSelect: 'none'
                    }}
                  />
                </div>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
