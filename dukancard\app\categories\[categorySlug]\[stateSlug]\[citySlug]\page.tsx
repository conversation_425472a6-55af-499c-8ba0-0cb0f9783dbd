import { <PERSON>ada<PERSON> } from "next";
import { Suspense } from "react";
import { getCategoryBySlug } from "@/lib/config/categories";
import { notFound } from "next/navigation";
import ModernCategoryClient from "../../ModernCategoryClient";
import ModernResultsSkeleton from "@/app/(main)/discover/ModernResultsSkeleton";
import { INDIAN_STATE_DATA } from "@/lib/config/states";
import { createSitemapClient } from "@/utils/supabase/sitemap";
import { unstable_cache } from "next/cache";
import { fetchBusinessesByLocation } from "@/lib/actions/categories/locationBasedFetching";

// Force dynamic rendering to prevent build-time generation
export const dynamic = 'force-dynamic';

interface CategoryCityPageProps {
  params: Promise<{
    categorySlug: string;
    stateSlug: string;
    citySlug: string;
  }>;
}

export async function generateMetadata({ params }: CategoryCityPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const { categorySlug, stateSlug, citySlug } = resolvedParams;

  // Get the category by slug
  const category = getCategoryBySlug(categorySlug);
  if (!category) {
    return {
      title: "Category Not Found",
      description: "The requested category could not be found."
    };
  }

  // Find the matching state using the state_slug directly
  const stateData = INDIAN_STATE_DATA.find(state => state.slug === stateSlug);
  const matchingState = stateData?.name;

  if (!matchingState) {
    return {
      title: "State Not Found",
      description: "The requested state could not be found."
    };
  }

  // State names are already in title case format in INDIAN_STATES
  const formattedState = matchingState;

  // Define a function to find a matching city with caching
  const findMatchingCity = unstable_cache(
    async (stateName: string, citySlugParam: string) => {
      console.log(`Looking up city with slug "${citySlugParam}" in state "${stateName}" (this will be cached)`);

      const supabaseClient = createSitemapClient();

      // Get city name directly using the city_slug
      const { data, error } = await supabaseClient
        .from("pincodes")
        .select("DivisionName")
        .eq("StateName", stateName)
        .eq("city_slug", citySlugParam)
        .limit(1);

      if (error) {
        console.error("Error fetching city by slug:", error);
        return "";
      }

      if (!data || data.length === 0) {
        console.warn(`No city found with slug "${citySlugParam}" in state "${stateName}"`);
        return "";
      }

      return data[0].DivisionName;
    },
    ["city-by-slug"], // Cache key prefix
    {
      revalidate: 60 * 60 * 24 * 30, // Revalidate every 30 days (in seconds)
      tags: [`state-city-${matchingState}-${citySlug}`], // Cache tag for this specific lookup
    }
  );

  // Use the cached function to find the matching city
  const matchingCity = await findMatchingCity(matchingState, citySlug);

  if (!matchingCity) {
    return {
      title: "City Not Found",
      description: "The requested city could not be found."
    };
  }

  // Format city name for display (Title Case)
  const formattedCity = matchingCity
    .toLowerCase()
    .replace(/\b\w/g, (char: string) => char.toUpperCase());

  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const pageUrl = `${siteUrl}/categories/${categorySlug}/${stateSlug}/${citySlug}`;

  return {
    title: `${category.name} in ${formattedCity}, ${formattedState} | Best ${category.name} in ${formattedCity}`,
    description: `Find ${category.name.toLowerCase()} in ${formattedCity}, ${formattedState}. Browse local ${category.name.toLowerCase()} businesses, products, and services in ${formattedCity} with Dukancard.`,
    openGraph: {
      title: `${category.name} in ${formattedCity}, ${formattedState} | Best ${category.name} in ${formattedCity}`,
      description: `Find ${category.name.toLowerCase()} in ${formattedCity}, ${formattedState}. Browse local ${category.name.toLowerCase()} businesses, products, and services in ${formattedCity} with Dukancard.`,
      url: pageUrl,
      siteName: "Dukancard",
      locale: "en_IN",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: `${category.name} in ${formattedCity}, ${formattedState} | Best ${category.name} in ${formattedCity}`,
      description: `Find ${category.name.toLowerCase()} in ${formattedCity}, ${formattedState}. Browse local ${category.name.toLowerCase()} businesses, products, and services in ${formattedCity} with Dukancard.`,
    },
    alternates: {
      canonical: pageUrl,
    },
  };
}

// Server Component - Handles initial rendering
async function CategoryCityPageContent({ params }: CategoryCityPageProps) {
  const resolvedParams = await params;
  const { categorySlug, stateSlug, citySlug } = resolvedParams;

  // Get the category by slug
  const category = getCategoryBySlug(categorySlug);
  if (!category) {
    notFound();
  }

  // Find the matching state using the state_slug directly
  const stateData = INDIAN_STATE_DATA.find(state => state.slug === stateSlug);
  const matchingState = stateData?.name;

  if (!matchingState) {
    notFound();
  }

  // Define a function to find a matching city with caching
  const findMatchingCity = unstable_cache(
    async (stateName: string, citySlugParam: string) => {
      console.log(`Looking up city with slug "${citySlugParam}" in state "${stateName}" (this will be cached)`);

      const supabaseClient = createSitemapClient();

      // Get city name directly using the city_slug
      const { data, error } = await supabaseClient
        .from("pincodes")
        .select("DivisionName")
        .eq("StateName", stateName)
        .eq("city_slug", citySlugParam)
        .limit(1);

      if (error) {
        console.error("Error fetching city by slug:", error);
        return "";
      }

      if (!data || data.length === 0) {
        console.warn(`No city found with slug "${citySlugParam}" in state "${stateName}"`);
        return "";
      }

      return data[0].DivisionName;
    },
    ["city-by-slug"], // Cache key prefix
    {
      revalidate: 60 * 60 * 24 * 30, // Revalidate every 30 days (in seconds)
      tags: [`state-city-${matchingState}-${citySlug}`], // Cache tag for this specific lookup
    }
  );

  // Use the cached function to find the matching city
  const matchingCity = await findMatchingCity(matchingState, citySlug);

  if (!matchingCity) {
    notFound();
  }

  // Format city name for display (Title Case)
  const formattedCity = matchingCity
    .toLowerCase()
    .replace(/\b\w/g, (char: string) => char.toUpperCase());

  // Create a serializable version of the category without methods
  const serializableCategory = {
    name: category.name,
    slug: category.slug,
    description: category.description,
    isPopular: category.isPopular,
    iconName: category.icon.name // Include the icon name for rendering on the client
  };

  // Fetch businesses in this category filtered by state and city slugs
  const { data: businesses, count } = await fetchBusinessesByLocation({
    categoryName: category.name,
    state: matchingState,
    city: matchingCity, // Keep for backward compatibility
    stateSlug: stateSlug, // Use the slug directly from the URL
    citySlug: citySlug, // Use the slug directly from the URL
    page: 1,
    limit: 20
  });

  return (
    <ModernCategoryClient
      category={serializableCategory}
      initialBusinesses={businesses || []}
      totalCount={count || 0}
      locationInfo={{
        state: matchingState,
        city: formattedCity
      }}
    />
  );
}

// Main Page Component using Suspense for streaming
export default async function CategoryCityPage({ params }: CategoryCityPageProps) {
  return (
    <div className="min-h-screen bg-white dark:bg-black">
      <Suspense fallback={<ModernResultsSkeleton />}>
        <CategoryCityPageContent params={params} />
      </Suspense>
    </div>
  );
}

// Note: We don't need generateStaticParams here as the cities are too numerous
// and will be generated on-demand instead
