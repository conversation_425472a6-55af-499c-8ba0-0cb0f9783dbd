import { ProductServiceData, ProductSortBy } from "@/backend/supabase/services/products/types";
import { ProductWithVariantInfo } from "@/types/products";

// Define types for component props and state
export type SaveResult = {
  success: boolean;
  error?: string;
  data?: ProductServiceData;
};

export interface ProductsContextType {
  // Data
  products: ProductWithVariantInfo[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  planLimit: number;
  canAddMore: boolean;

  // UI State
  viewType: "table" | "grid";
  setViewType: (_type: "table" | "grid") => void;
  isLoading: boolean;
  isPending: boolean;
  isInitialLoading: boolean;

  // Filter State
  searchTerm: string;
  setSearchTerm: (_term: string) => void;
  filterAvailable: boolean | undefined;
  setFilterAvailable: (_available: boolean | undefined) => void;
  sortBy: ProductSortBy;
  setSortBy: (_sortBy: ProductSortBy) => void;

  // Modal State
  deletingProductId: string | null;
  setDeletingProductId: (_id: string | null) => void;

  // Actions
  loadMoreProducts: () => void;
  handleAddNew: () => void;
  handleDeleteConfirm: () => Promise<void>;
  handleSave: () => Promise<void>;
  getFilterStatusText: () => string;
  getProductStatusBadge: (_isAvailable: boolean) => React.ReactElement;
}

// Animation variants
export const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05
    }
  }
};

export const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24
    }
  }
};

export const cardHoverVariants = {
  initial: {
    boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)",
    borderColor: "rgba(229, 231, 235, 0.7)"
  },
  hover: {
    boxShadow: "0 10px 25px -5px rgba(var(--brand-gold-rgb), 0.2), 0 8px 10px -6px rgba(var(--brand-gold-rgb), 0.1)",
    borderColor: "rgba(var(--brand-gold-rgb), 0.5)",
    scale: 1.01,
    transition: { duration: 0.3 }
  }
};

export const viewTransitionVariants = {
  initial: { opacity: 0, scale: 0.95 },
  animate: {
    opacity: 1,
    scale: 1,
    transition: { duration: 0.4, ease: "easeOut" }
  },
  exit: {
    opacity: 0,
    scale: 0.95,
    transition: { duration: 0.3, ease: "easeIn" }
  }
};

// Constants
export const ITEMS_PER_PAGE = 10;
