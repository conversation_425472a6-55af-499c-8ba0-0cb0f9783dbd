"use client";

import { useState, useRef, useCallback } from "react";
import { UseFormReturn } from "react-hook-form";
import { Upload, Image, Link, Eye, EyeOff, X, Loader2 } from "lucide-react";
import NextImage from "next/image";
import { BusinessCardData } from "../../schema";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "sonner";
import { uploadCustomAdImage, deleteCustomAd } from "@/backend/supabase/services/business/card/customAdUpload";
import { compressImageUltraAggressiveClient } from "@/lib/utils/client-image-compression";
import CustomAdCropDialog from "../CustomAdCropDialog";

interface CustomAdUploadProps {
  form: UseFormReturn<BusinessCardData>;
  currentUserPlan: "basic" | "growth" | "pro" | "enterprise" | "trial" | null;
}

export default function CustomAdUpload({
  form,
  currentUserPlan,
}: CustomAdUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [imageToCrop, setImageToCrop] = useState<string | null>(null);
  const [originalFile, setOriginalFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Check if user has access to custom ads
  const hasCustomAdAccess = currentUserPlan === "pro" || currentUserPlan === "enterprise";

  // Handle file selection
  const handleFileSelect = useCallback((file: File | null) => {
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error("Please select a valid image file");
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("Image file size must be less than 5MB. Please choose a smaller file.");
      return;
    }

    setOriginalFile(file);
    const reader = new FileReader();
    reader.onloadend = () => {
      setImageToCrop(reader.result as string);
    };
    reader.readAsDataURL(file);
  }, []);

  // Handle drag and drop
  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (!hasCustomAdAccess || isUploading) return;

    const droppedFile = e.dataTransfer.files?.[0];
    if (droppedFile) {
      handleFileSelect(droppedFile);
    }
  }, [hasCustomAdAccess, isUploading, handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (hasCustomAdAccess && !isUploading) {
      setIsDragging(true);
    }
  }, [hasCustomAdAccess, isUploading]);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  // Handle crop completion and upload
  const handleCropComplete = async (croppedImageBlob: Blob | null) => {
    if (!originalFile || !croppedImageBlob) {
      // If cropping failed, close dialog immediately
      setImageToCrop(null);
      setOriginalFile(null);
      return;
    }

    setIsUploading(true);
    try {
      // Convert blob to file for compression
      const croppedFile = new File([croppedImageBlob], originalFile.name, {
        type: 'image/png', // Canvas outputs PNG
        lastModified: Date.now()
      });

      // Compress image on client-side first
      const compressionResult = await compressImageUltraAggressiveClient(croppedFile, {
        maxDimension: 1200, // 16:9 aspect ratio - max 1200x675 for ads
        targetSizeKB: 100   // Target 100KB max
      });

      // Convert compressed blob back to file
      const compressedFile = new File([compressionResult.blob], originalFile.name, {
        type: compressionResult.blob.type
      });

      // Create FormData with the compressed image
      const formData = new FormData();
      formData.append('image', compressedFile);

      // Upload the image
      const result = await uploadCustomAdImage(formData);

      if (result.success && result.url) {
        // Update form data
        form.setValue("custom_ads.image_url", result.url);
        form.setValue("custom_ads.enabled", true);
        form.setValue("custom_ads.uploaded_at", new Date().toISOString());

        toast.success("Custom ad uploaded successfully!");
      } else {
        toast.error(result.error || "Failed to upload custom ad");
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Failed to upload custom ad");
    } finally {
      setIsUploading(false);
      // Close dialog after upload is complete (success or failure)
      setImageToCrop(null);
      setOriginalFile(null);
    }
  };

  // Handle ad toggle - only update form state, save on main form submission
  const handleAdToggle = (enabled: boolean) => {
    form.setValue("custom_ads.enabled", enabled, { shouldDirty: true });
  };

  // Handle ad deletion
  const handleDeleteAd = async () => {
    setIsDeleting(true);
    try {
      const result = await deleteCustomAd();
      if (result.success) {
        form.setValue("custom_ads.enabled", false);
        form.setValue("custom_ads.image_url", "");
        form.setValue("custom_ads.link_url", "");
        form.setValue("custom_ads.uploaded_at", "");
        toast.success("Custom ad deleted successfully");
      } else {
        toast.error(result.error || "Failed to delete custom ad");
      }
    } catch (error) {
      console.error("Delete error:", error);
      toast.error("Failed to delete custom ad");
    } finally {
      setIsDeleting(false);
    }
  };

  if (!hasCustomAdAccess) {
    return (
      <div className="space-y-4 p-4 border border-amber-200 dark:border-amber-800 rounded-lg bg-amber-50 dark:bg-amber-950/20">
        <div className="flex items-center gap-2">
          <Image className="h-5 w-5 text-amber-600 dark:text-amber-400" aria-label="Custom ads feature" />
          <h3 className="text-lg font-semibold text-amber-800 dark:text-amber-200">
            Custom Ads
          </h3>
          <Badge variant="secondary" className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200">
            Pro Feature
          </Badge>
        </div>
        <p className="text-sm text-amber-700 dark:text-amber-300">
          Upgrade to Pro or Enterprise plan to upload custom advertisement images that will be displayed on your public business card page.
        </p>
        <Button variant="outline" className="border-amber-300 text-amber-700 hover:bg-amber-100 dark:border-amber-700 dark:text-amber-300 dark:hover:bg-amber-900/20">
          Upgrade to Pro
        </Button>
      </div>
    );
  }

  const currentAdImage = form.watch("custom_ads.image_url");
  const isAdEnabled = form.watch("custom_ads.enabled");

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Image className="h-5 w-5 text-primary" aria-label="Custom advertisement" />
        <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-200">
          Custom Advertisement
        </h3>
        <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
          {currentUserPlan?.toUpperCase()}
        </Badge>
      </div>

      <div className="space-y-6">
        {/* Enable/Disable Toggle */}
        <FormField
          control={form.control}
          name="custom_ads.enabled"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base font-medium flex items-center gap-2">
                  {field.value ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                  Show Custom Ad
                </FormLabel>
                <p className="text-sm text-muted-foreground">
                  Display your custom advertisement on the public business card page
                </p>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={(checked) => {
                    field.onChange(checked);
                    handleAdToggle(checked);
                  }}
                  disabled={!currentAdImage}
                />
              </FormControl>
            </FormItem>
          )}
        />

        {/* Image Upload */}
        <div className="space-y-3">
          <FormLabel className="text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
            <Upload className="h-4 w-4 text-primary" />
            Advertisement Image
          </FormLabel>

          <Card
            className={`border-dashed border-2 transition-colors cursor-pointer ${
              isDragging
                ? "border-primary bg-primary/5"
                : "border-muted-foreground/25 hover:border-primary/50"
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={() => !isUploading && !isDeleting && fileInputRef.current?.click()}
          >
            <CardContent className="p-6">
              {isUploading ? (
                <div className="text-center">
                  <div className="w-16 h-16 rounded-lg bg-primary/10 flex items-center justify-center mx-auto mb-4 border-2 border-primary/20">
                    <Loader2 className="h-8 w-8 text-primary animate-spin" />
                  </div>
                  <p className="text-sm font-medium mb-2 text-primary">Uploading Custom Ad...</p>
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">
                      Compressing and optimizing image
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Uploading to secure storage
                    </p>
                  </div>
                  <div className="mt-3 w-full bg-muted rounded-full h-1.5">
                    <div className="bg-primary h-1.5 rounded-full animate-pulse" style={{ width: '70%' }}></div>
                  </div>
                </div>
              ) : currentAdImage ? (
                <div className="space-y-4">
                  <div className="relative">
                    <div className="w-full aspect-[16/9] overflow-hidden rounded-lg border-2 border-green-200 dark:border-green-800">
                      <NextImage
                        src={currentAdImage}
                        alt="Custom advertisement"
                        width={400}
                        height={225}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteAd();
                      }}
                      disabled={isDeleting}
                      className="absolute top-2 right-2"
                    >
                      {isDeleting ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <X className="h-4 w-4" />
                      )}
                    </Button>
                    {/* Ad status indicator */}
                    <div className="absolute bottom-2 left-2 bg-green-600 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
                      <Eye className="h-3 w-3" />
                      {isAdEnabled ? "Live" : "Draft"}
                    </div>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium text-green-700 dark:text-green-300">
                      Custom ad uploaded successfully
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {isAdEnabled ? "Visible on your public card" : "Enable to show on public card"} • Click to replace
                    </p>
                  </div>
                </div>
              ) : (
                <div className="text-center">
                  <div className="w-12 h-12 rounded-lg bg-muted flex items-center justify-center mx-auto mb-3">
                    <Upload className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <p className="text-sm font-medium mb-1">Upload Custom Ad</p>
                  <p className="text-xs text-muted-foreground mb-2">
                    Drag & drop or click to select
                  </p>
                  <p className="text-xs text-muted-foreground">
                    <strong>Aspect Ratio:</strong> 16:9 (recommended)
                  </p>
                </div>
              )}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={(e) => handleFileSelect(e.target.files?.[0] || null)}
                className="hidden"
                disabled={isUploading || isDeleting}
              />
            </CardContent>
          </Card>
        </div>

        {/* Link URL */}
        <FormField
          control={form.control}
          name="custom_ads.link_url"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <FormLabel className="text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5">
                <Link className="h-4 w-4 text-primary" />
                Advertisement Link (Optional)
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  value={field.value || ""}
                  onChange={(e) => {
                    field.onChange(e.target.value);
                  }}
                  placeholder="https://example.com"
                />
              </FormControl>
              <p className="text-xs text-muted-foreground">
                Make your ad clickable by adding a website URL. Changes will be saved when you click &quot;Save Changes&quot;.
              </p>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Custom Ad Crop Dialog */}
      <CustomAdCropDialog
        isOpen={!!imageToCrop}
        imgSrc={imageToCrop}
        onCropComplete={handleCropComplete}
        onClose={() => {
          // Only allow closing if not uploading
          if (!isUploading) {
            setImageToCrop(null);
            setOriginalFile(null);
          }
        }}
        isUploading={isUploading}
      />
    </div>
  );
}
