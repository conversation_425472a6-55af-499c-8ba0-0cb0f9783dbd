# Card Visits Table Documentation

## Table Overview

The `card_visits` table in the Dukancard application tracks visits to business profile cards. It records each unique visitor to a business profile on a daily basis and is used to calculate various visit metrics for business analytics.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key for the visit record |
| business_profile_id | uuid | NO | | Foreign key to business_profiles.id |
| visited_at | timestamptz | NO | now() | Timestamp when the visit occurred |
| visitor_identifier | text | NO | | Unique identifier for the visitor (typically a hashed IP or browser fingerprint) |
| visit_date | date | NO | CURRENT_DATE | Date of the visit (used for daily unique visit tracking) |

## Constraints

### Primary Key
- `card_visits_pkey` - Primary key constraint on the `id` column

### Foreign Keys
- `fk_business_profile` - Foreign key constraint linking `business_profile_id` to `business_profiles.id`

### Unique Constraints
- `unique_daily_visitor` - Ensures that a visitor can only be counted once per business profile per day
  - Columns: `business_profile_id`, `visitor_identifier`, `visit_date`

### Check Constraints
Multiple check constraints ensure that required fields are not null.

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| card_visits_pkey | UNIQUE | id | Primary key index |
| unique_daily_visitor | UNIQUE | business_profile_id, visitor_identifier, visit_date | Enforces unique daily visits per visitor |
| idx_card_visits_business_profile_id | BTREE | business_profile_id | Index for faster business profile lookups |
| idx_card_visits_visited_at | BTREE | visited_at | Index for faster timestamp-based queries |
| idx_card_visits_visit_date | BTREE | visit_date | Index for faster date-based queries |
| idx_card_visits_visitor_identifier | BTREE | visitor_identifier | Index for faster visitor lookups |
| card_visits_business_visitor_idx | BTREE | business_profile_id, visitor_identifier | Composite index for business and visitor queries |
| card_visits_business_visitor_date_idx | BTREE | business_profile_id, visitor_identifier, visit_date | Composite index for business, visitor, and date queries |

## Triggers

### handle_new_visit
- **Event**: INSERT
- **Function**: update_visit_counts()
- **Description**: Updates visit count metrics in the business_profiles table when a new visit is recorded

### handle_new_visit_monthly
- **Event**: INSERT
- **Function**: update_monthly_visit_counts()
- **Description**: Updates monthly visit metrics in the monthly_visit_metrics table when a new visit is recorded

### Trigger Function Definitions

#### update_visit_counts()

```sql
CREATE OR REPLACE FUNCTION public.update_visit_counts()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  profile_id uuid;
  ist_latest_date date;
  ist_today date;
  ist_yesterday date;
  ist_7_days_ago date;
  ist_30_days_ago date;
  total_count integer;
  today_count integer;
  yesterday_count integer;
  days_7_count integer;
  days_30_count integer;
BEGIN
  -- Get the business profile ID
  profile_id := NEW.business_profile_id;
  
  -- Find the most recent visit date in IST
  SELECT MAX(date(visited_at AT TIME ZONE 'Asia/Kolkata'))
  INTO ist_latest_date
  FROM public.card_visits
  WHERE business_profile_id = profile_id;
  
  -- If no visits, use current date
  IF ist_latest_date IS NULL THEN
    ist_latest_date := date(NOW() AT TIME ZONE 'Asia/Kolkata');
  END IF;
  
  -- Set today as the latest visit date
  ist_today := ist_latest_date;
  ist_yesterday := ist_today - interval '1 day';
  ist_7_days_ago := ist_today - interval '7 days';
  ist_30_days_ago := ist_today - interval '30 days';
  
  -- Count total unique visits
  SELECT COUNT(DISTINCT visitor_identifier) INTO total_count
  FROM public.card_visits
  WHERE business_profile_id = profile_id;
  
  -- Count today's unique visits (based on IST)
  SELECT COUNT(DISTINCT visitor_identifier) INTO today_count
  FROM public.card_visits
  WHERE business_profile_id = profile_id
    AND date(visited_at AT TIME ZONE 'Asia/Kolkata') = ist_today;
  
  -- Count yesterday's unique visits (based on IST)
  SELECT COUNT(DISTINCT visitor_identifier) INTO yesterday_count
  FROM public.card_visits
  WHERE business_profile_id = profile_id
    AND date(visited_at AT TIME ZONE 'Asia/Kolkata') = ist_yesterday;
  
  -- Count last 7 days unique visits (based on IST)
  SELECT COUNT(DISTINCT visitor_identifier) INTO days_7_count
  FROM public.card_visits
  WHERE business_profile_id = profile_id
    AND date(visited_at AT TIME ZONE 'Asia/Kolkata') >= ist_7_days_ago
    AND date(visited_at AT TIME ZONE 'Asia/Kolkata') <= ist_today;
  
  -- Count last 30 days unique visits (based on IST)
  SELECT COUNT(DISTINCT visitor_identifier) INTO days_30_count
  FROM public.card_visits
  WHERE business_profile_id = profile_id
    AND date(visited_at AT TIME ZONE 'Asia/Kolkata') >= ist_30_days_ago
    AND date(visited_at AT TIME ZONE 'Asia/Kolkata') <= ist_today;
  
  -- Update the business_profiles table
  UPDATE public.business_profiles
  SET 
    total_visits = total_count,
    today_visits = today_count,
    yesterday_visits = yesterday_count,
    visits_7_days = days_7_count,
    visits_30_days = days_30_count
  WHERE id = profile_id;
  
  RETURN NULL; -- Result is ignored since this is an AFTER trigger
END;
$function$
```

#### update_monthly_visit_counts()

```sql
CREATE OR REPLACE FUNCTION public.update_monthly_visit_counts()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  ist_date DATE;
  ist_year INTEGER;
  ist_month INTEGER;
BEGIN
  -- Calculate the date in IST (UTC+5:30)
  ist_date := (NEW.visited_at AT TIME ZONE 'Asia/Kolkata')::DATE;
  ist_year := EXTRACT(YEAR FROM ist_date);
  ist_month := EXTRACT(MONTH FROM ist_date);

  -- Insert or update the monthly visit metrics
  INSERT INTO monthly_visit_metrics (
    business_profile_id,
    year,
    month,
    unique_visits
  )
  VALUES (
    NEW.business_profile_id,
    ist_year,
    ist_month,
    1
  )
  ON CONFLICT (business_profile_id, year, month)
  DO UPDATE SET
    unique_visits = (
      SELECT COUNT(DISTINCT visitor_identifier)
      FROM card_visits
      WHERE business_profile_id = NEW.business_profile_id
      AND EXTRACT(YEAR FROM (visited_at AT TIME ZONE 'Asia/Kolkata')::DATE) = ist_year
      AND EXTRACT(MONTH FROM (visited_at AT TIME ZONE 'Asia/Kolkata')::DATE) = ist_month
    ),
    updated_at = now();

  RETURN NEW;
END;
$function$
```

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| Allow business owner read access | SELECT | EXISTS (SELECT 1 FROM business_profiles bp WHERE bp.id = card_visits.business_profile_id AND bp.id = auth.uid()) | |
| Allow public insert access | INSERT | | |

These policies ensure that:
1. Only business owners can view visit data for their own business profiles
2. Anyone can insert new visit records (necessary for tracking visits from non-authenticated users)

## Related Tables

### monthly_visit_metrics

The `monthly_visit_metrics` table is populated by the `update_monthly_visit_counts()` trigger function and stores aggregated monthly visit data:

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key |
| business_profile_id | uuid | NO | | Foreign key to business_profiles.id |
| year | integer | NO | | Year of the visit metrics |
| month | integer | NO | | Month of the visit metrics (1-12) |
| unique_visits | integer | NO | 0 | Count of unique visitors for the month |
| created_at | timestamptz | NO | now() | Timestamp when the record was created |
| updated_at | timestamptz | NO | now() | Timestamp when the record was last updated |

### business_profiles

The `business_profiles` table is updated by the `update_visit_counts()` trigger function with the following visit-related fields:

- `total_visits`: Total unique visitors to the business profile
- `today_visits`: Unique visitors today (based on IST timezone)
- `yesterday_visits`: Unique visitors yesterday (based on IST timezone)
- `visits_7_days`: Unique visitors in the last 7 days
- `visits_30_days`: Unique visitors in the last 30 days

## Usage Notes

1. The `card_visits` table is designed to track unique daily visitors to business profiles.
2. The `unique_daily_visitor` constraint ensures that a visitor is only counted once per day per business profile.
3. Visit timestamps are stored in UTC but converted to IST (UTC+5:30) for date-based calculations.
4. The table uses triggers to automatically update visit metrics in both the `business_profiles` and `monthly_visit_metrics` tables.
5. RLS policies ensure that only business owners can view their own visit data, while allowing public insertion of visit records.
6. The `visitor_identifier` should be a hashed value to protect visitor privacy while still allowing unique visitor tracking.
7. The table is optimized with multiple indexes to support efficient querying of visit data by business, visitor, date, and combinations thereof.
