#!/usr/bin/env node

/**
 * Security Testing Script for DukanCard App
 * Tests all implemented security measures
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🛡️  Testing DukanCard App Security Implementation...\n');

// Test results
const testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

// Helper function to run tests
function runTest(testName, testFunction) {
  try {
    console.log(`🧪 Testing: ${testName}...`);
    const result = testFunction();
    
    if (result.success) {
      console.log(`✅ PASSED: ${testName}`);
      if (result.message) console.log(`   📝 ${result.message}`);
      testResults.passed++;
    } else {
      console.log(`❌ FAILED: ${testName}`);
      console.log(`   📝 ${result.message}`);
      testResults.failed++;
    }
    
    if (result.warnings && result.warnings.length > 0) {
      result.warnings.forEach(warning => {
        console.log(`⚠️  WARNING: ${warning}`);
        testResults.warnings++;
      });
    }
    
    testResults.tests.push({
      name: testName,
      success: result.success,
      message: result.message,
      warnings: result.warnings || []
    });
    
    console.log('');
  } catch (error) {
    console.log(`❌ ERROR: ${testName} - ${error.message}\n`);
    testResults.failed++;
    testResults.tests.push({
      name: testName,
      success: false,
      message: error.message,
      warnings: []
    });
  }
}

// Test 1: Check if security dependencies are installed
function testSecurityDependencies() {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  const requiredDeps = [
    'jail-monkey',
    'react-native-exit-app',
    'react-native-keychain',
    '@react-native-async-storage/async-storage'
  ];
  
  const requiredDevDeps = [
    'obfuscator-io-metro-plugin'
  ];
  
  const missing = [];
  const warnings = [];
  
  // Check dependencies
  requiredDeps.forEach(dep => {
    if (!packageJson.dependencies || !packageJson.dependencies[dep]) {
      missing.push(dep);
    }
  });
  
  // Check dev dependencies
  requiredDevDeps.forEach(dep => {
    if (!packageJson.devDependencies || !packageJson.devDependencies[dep]) {
      missing.push(dep);
    }
  });
  
  if (missing.length > 0) {
    return {
      success: false,
      message: `Missing security dependencies: ${missing.join(', ')}`,
      warnings
    };
  }
  
  return {
    success: true,
    message: 'All security dependencies are installed',
    warnings
  };
}

// Test 2: Check if security files exist
function testSecurityFiles() {
  const requiredFiles = [
    'src/security/SecurityContext.tsx',
    'src/security/ApiKeyManager.ts',
    'src/security/SecureSupabaseClient.ts',
    'src/security/EnvironmentProtection.ts',
    'android/app/proguard-rules.pro'
  ];
  
  const missing = [];
  const warnings = [];
  
  requiredFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (!fs.existsSync(filePath)) {
      missing.push(file);
    }
  });
  
  if (missing.length > 0) {
    return {
      success: false,
      message: `Missing security files: ${missing.join(', ')}`,
      warnings
    };
  }
  
  return {
    success: true,
    message: 'All security files are present',
    warnings
  };
}

// Test 3: Check ProGuard configuration
function testProGuardConfig() {
  const buildGradlePath = path.join(process.cwd(), 'android/app/build.gradle');
  
  if (!fs.existsSync(buildGradlePath)) {
    return {
      success: false,
      message: 'Android build.gradle not found',
      warnings: []
    };
  }
  
  const buildGradleContent = fs.readFileSync(buildGradlePath, 'utf8');
  const warnings = [];
  
  // Check if ProGuard is enabled
  if (!buildGradleContent.includes('enableProguardInReleaseBuilds = true')) {
    return {
      success: false,
      message: 'ProGuard is not enabled in build.gradle',
      warnings
    };
  }
  
  // Check if ProGuard rules file exists
  const proguardRulesPath = path.join(process.cwd(), 'android/app/proguard-rules.pro');
  if (!fs.existsSync(proguardRulesPath)) {
    warnings.push('ProGuard rules file not found');
  }
  
  return {
    success: true,
    message: 'ProGuard is properly configured',
    warnings
  };
}

// Test 4: Check Metro obfuscation configuration
function testMetroObfuscation() {
  const metroConfigPath = path.join(process.cwd(), 'metro.config.js');
  
  if (!fs.existsSync(metroConfigPath)) {
    return {
      success: false,
      message: 'Metro config not found',
      warnings: []
    };
  }
  
  const metroConfigContent = fs.readFileSync(metroConfigPath, 'utf8');
  const warnings = [];
  
  // Check if obfuscation is configured
  if (!metroConfigContent.includes('obfuscator-io-metro-plugin')) {
    return {
      success: false,
      message: 'JavaScript obfuscation is not configured in metro.config.js',
      warnings
    };
  }
  
  // Check if production check is present
  if (!metroConfigContent.includes('NODE_ENV === \'production\'')) {
    warnings.push('Production environment check not found in metro config');
  }
  
  return {
    success: true,
    message: 'Metro obfuscation is properly configured',
    warnings
  };
}

// Test 5: Check environment variable protection
function testEnvironmentProtection() {
  const envProtectionPath = path.join(process.cwd(), 'src/security/EnvironmentProtection.ts');
  
  if (!fs.existsSync(envProtectionPath)) {
    return {
      success: false,
      message: 'Environment protection file not found',
      warnings: []
    };
  }
  
  const envProtectionContent = fs.readFileSync(envProtectionPath, 'utf8');
  const warnings = [];
  
  // Check if key obfuscation is implemented
  if (!envProtectionContent.includes('obfuscate') || !envProtectionContent.includes('deobfuscate')) {
    warnings.push('Environment variable obfuscation functions not found');
  }
  
  // Check if validation is implemented
  if (!envProtectionContent.includes('validateEnvironmentConfig')) {
    warnings.push('Environment validation function not found');
  }
  
  return {
    success: true,
    message: 'Environment protection is implemented',
    warnings
  };
}

// Test 6: Check app integration
function testAppIntegration() {
  const layoutPath = path.join(process.cwd(), 'app/_layout.tsx');
  
  if (!fs.existsSync(layoutPath)) {
    return {
      success: false,
      message: 'App layout file not found',
      warnings: []
    };
  }
  
  const layoutContent = fs.readFileSync(layoutPath, 'utf8');
  const warnings = [];
  
  // Check if SecurityProvider is imported and used
  if (!layoutContent.includes('SecurityProvider')) {
    return {
      success: false,
      message: 'SecurityProvider is not integrated in app layout',
      warnings
    };
  }
  
  return {
    success: true,
    message: 'Security is properly integrated in app',
    warnings
  };
}

// Run all tests
console.log('Starting security tests...\n');

runTest('Security Dependencies', testSecurityDependencies);
runTest('Security Files', testSecurityFiles);
runTest('ProGuard Configuration', testProGuardConfig);
runTest('Metro Obfuscation', testMetroObfuscation);
runTest('Environment Protection', testEnvironmentProtection);
runTest('App Integration', testAppIntegration);

// Print summary
console.log('🏁 Security Test Summary');
console.log('========================');
console.log(`✅ Passed: ${testResults.passed}`);
console.log(`❌ Failed: ${testResults.failed}`);
console.log(`⚠️  Warnings: ${testResults.warnings}`);
console.log(`📊 Total Tests: ${testResults.tests.length}\n`);

if (testResults.failed === 0) {
  console.log('🎉 All security tests passed! Your app is ready for secure deployment.');
} else {
  console.log('🚨 Some security tests failed. Please address the issues before deployment.');
  process.exit(1);
}

if (testResults.warnings > 0) {
  console.log('⚠️  Please review the warnings to ensure optimal security.');
}
