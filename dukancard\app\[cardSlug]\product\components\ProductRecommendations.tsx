"use client";

import { ProductServiceData } from "@/app/(dashboard)/dashboard/business/products/actions";
import ProductListItem from "@/app/components/ProductListItem";
import Link from "next/link";
import { motion } from "framer-motion";

interface ProductRecommendationsProps {
  businessProducts: ProductServiceData[];
  otherBusinessProducts: Array<ProductServiceData & { business_slug: string }>;
  businessSlug: string;
}

export default function ProductRecommendations({
  businessProducts,
  otherBusinessProducts,
  businessSlug,
}: ProductRecommendationsProps) {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  return (
    <div className="mt-8 sm:mt-12 md:mt-16 space-y-8 sm:space-y-12 md:space-y-16 md:px-4">
      {/* More from this business */}
      {businessProducts.length > 0 && (
        <motion.section
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-6"
        >
          <motion.div
            variants={itemVariants}
            className="flex items-center justify-between"
          >
            <h2 className="text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-50 relative">
              More from this business
              <span className="absolute -bottom-2 left-0 w-16 h-1 bg-primary rounded-full"></span>
            </h2>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6"
          >
            {businessProducts.map((product) => (
              <Link
                key={product.id}
                href={`/${businessSlug}/product/${product.slug || product.id}`}
                className="block h-full transform transition-transform duration-300 hover:-translate-y-1"
              >
                <ProductListItem product={product} isLink={false} />
              </Link>
            ))}
          </motion.div>
        </motion.section>
      )}

      {/* Products from other businesses */}
      {otherBusinessProducts.length > 0 && (
        <motion.section
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-6"
        >
          <motion.div
            variants={itemVariants}
            className="flex items-center justify-between"
          >
            <h2 className="text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-50 relative">
              You might also like
              <span className="absolute -bottom-2 left-0 w-16 h-1 bg-primary rounded-full"></span>
            </h2>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6"
          >
            {otherBusinessProducts.map((product) => (
              <Link
                key={product.id}
                href={`/${product.business_slug}/product/${product.slug || product.id}`}
                className="block h-full transform transition-transform duration-300 hover:-translate-y-1"
              >
                <ProductListItem product={product} isLink={false} />
              </Link>
            ))}
          </motion.div>
        </motion.section>
      )}
    </div>
  );
}
