/**
 * Free Error Tracking Service
 * Provides crash reporting and error logging without external paid services
 */

import Constants from 'expo-constants';
import { productionErrorLogger } from './productionErrorLogging';

interface ErrorInfo {
  error: Error;
  errorInfo?: any;
  context?: string;
  userId?: string;
  timestamp: string;
  appVersion: string;
  platform: string;
  buildType: string;
}

class ErrorTrackingService {
  private isEnabled: boolean;
  private errors: ErrorInfo[] = [];
  private maxStoredErrors = 50;

  constructor() {
    this.isEnabled = Constants.expoConfig?.extra?.enableCrashReporting === true;
    this.setupGlobalErrorHandlers();
  }

  /**
   * Set up global error handlers for unhandled errors
   */
  private setupGlobalErrorHandlers() {
    if (!this.isEnabled) return;

    // Handle unhandled JavaScript errors
    const originalHandler = ErrorUtils.getGlobalHandler();
    ErrorUtils.setGlobalHandler((error, isFatal) => {
      this.logError(error, {
        context: 'Global Error Handler',
        isFatal,
      });
      
      // Call original handler to maintain default behavior
      if (originalHandler) {
        originalHandler(error, isFatal);
      }
    });

    // Handle unhandled promise rejections
    // Note: This is a React Native specific implementation
    try {
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const ExceptionsManager = require('react-native/Libraries/Core/ExceptionsManager');
      const originalRejectionHandler = ExceptionsManager.handleException;
      ExceptionsManager.handleException = (error: any, isFatal: boolean) => {
        this.logError(error, {
          context: 'Unhandled Promise Rejection',
          isFatal,
        });

        // Call original handler
        originalRejectionHandler(error, isFatal);
      };
    } catch (e) {
      // ExceptionsManager not available (e.g., in web environment)
      console.warn('ExceptionsManager not available for error tracking');
    }
  }

  /**
   * Log an error with context information
   */
  logError(error: Error, context?: any) {
    if (!this.isEnabled) {
      // In development, just log to console
      console.error('Error:', error);
      if (context) console.error('Context:', context);
      return;
    }

    const errorInfo: ErrorInfo = {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } as Error,
      errorInfo: context,
      context: context?.context || 'Unknown',
      timestamp: new Date().toISOString(),
      appVersion: Constants.expoConfig?.version || 'Unknown',
      platform: Constants.platform?.ios ? 'ios' : 'android',
      buildType: Constants.expoConfig?.extra?.appEnv || 'development',
    };

    // Store error locally
    this.storeError(errorInfo);

    // Log to console for debugging
    console.error('Error tracked:', errorInfo);

    // Log to production error logger if enabled
    if (this.isEnabled) {
      productionErrorLogger.logProductionError(error, {
        screen: context?.context,
        action: context?.action,
        additionalData: context,
      }).catch(() => {
        // Silently fail if production logging fails
      });
    }
  }

  /**
   * Store error locally (for debugging and potential later upload)
   */
  private storeError(errorInfo: ErrorInfo) {
    this.errors.push(errorInfo);
    
    // Keep only the most recent errors
    if (this.errors.length > this.maxStoredErrors) {
      this.errors = this.errors.slice(-this.maxStoredErrors);
    }
  }

  /**
   * Get stored errors (useful for debugging)
   */
  getStoredErrors(): ErrorInfo[] {
    return [...this.errors];
  }

  /**
   * Clear stored errors
   */
  clearStoredErrors() {
    this.errors = [];
  }

  /**
   * Log a custom event or error
   */
  logEvent(message: string, data?: any) {
    if (!this.isEnabled) {
      console.log('Event:', message, data);
      return;
    }

    const error = new Error(message);
    this.logError(error, {
      context: 'Custom Event',
      data,
    });
  }

  /**
   * Log user actions for debugging
   */
  logUserAction(action: string, screen: string, data?: any) {
    if (!this.isEnabled) {
      console.log(`User Action [${screen}]:`, action, data);
      return;
    }

    console.log(`User Action [${screen}]:`, action, data);
    
    // Store user action for context in case of errors
    // This helps understand what the user was doing when an error occurred
  }

  /**
   * Enable or disable error tracking
   */
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }

  /**
   * Check if error tracking is enabled
   */
  isTrackingEnabled(): boolean {
    return this.isEnabled;
  }
}

// Create singleton instance
export const errorTracker = new ErrorTrackingService();

// Export types for use in components
export type { ErrorInfo };
