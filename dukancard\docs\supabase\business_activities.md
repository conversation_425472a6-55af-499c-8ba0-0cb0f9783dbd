# Business Activities Table Documentation

## Table Overview

The `business_activities` table in the Dukancard application tracks user interactions with business profiles. It records activities such as likes, subscriptions, and ratings, providing a history of user engagement with businesses and enabling notification functionality.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key for the activity record |
| business_profile_id | uuid | NO | | Foreign key to business_profiles.id |
| user_id | uuid | NO | | Foreign key to users.id |
| activity_type | text | NO | | Type of activity ('like', 'subscribe', or 'rating') |
| rating_value | integer | YES | | Rating value (1-5), only used when activity_type is 'rating' |
| created_at | timestamptz | NO | now() | Timestamp when the activity occurred |
| is_read | boolean | NO | false | Flag indicating whether the activity notification has been read |

## Constraints

### Primary Key
- `business_activities_pkey` - Primary key constraint on the `id` column

### Foreign Keys
- `business_activities_business_profile_id_fkey` - Foreign key constraint linking `business_profile_id` to `business_profiles.id`
- `business_activities_user_id_fkey` - Foreign key constraint linking `user_id` to `users.id`

### Check Constraints
- `business_activities_activity_type_check` - Ensures activity_type is one of: 'like', 'subscribe', or 'rating'
  ```sql
  CHECK (activity_type = ANY (ARRAY['like', 'subscribe', 'rating']))
  ```
- `rating_value_only_for_ratings` - Ensures rating_value is only provided for 'rating' activities and is required for them
  ```sql
  CHECK (
    (activity_type = 'rating' AND rating_value IS NOT NULL) OR 
    (activity_type <> 'rating' AND rating_value IS NULL)
  )
  ```

### Not Null Constraints
Multiple check constraints ensure that required fields are not null.

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| business_activities_pkey | UNIQUE | id | Primary key index |
| idx_business_activities_business_profile_id | BTREE | business_profile_id | Index for faster business profile lookups |
| idx_business_activities_user_id | BTREE | user_id | Index for faster user lookups |
| idx_business_activities_activity_type | BTREE | activity_type | Index for filtering by activity type |
| idx_business_activities_is_read | BTREE | is_read | Index for filtering read/unread activities |
| idx_business_activities_created_at | BTREE | created_at | Index for sorting by creation time |

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| business_activities_select_policy | SELECT | (auth.uid() = business_profile_id) | |
| business_activities_update_policy | UPDATE | (auth.uid() = business_profile_id) | |

These policies ensure that:
1. Business owners can only view activities related to their own business profiles
2. Business owners can only update activities related to their own business profiles (e.g., marking notifications as read)

## Related Tables

### business_profiles
The `business_profiles` table is referenced by the `business_profile_id` foreign key and contains information about the business that received the activity.

### users
The `users` table is referenced by the `user_id` foreign key and contains information about the user who performed the activity.

## Usage Notes

1. **Activity Types**:
   - `like`: Represents a user liking a business profile
   - `subscribe`: Represents a user subscribing to or following a business profile
   - `rating`: Represents a user rating a business profile (includes a rating value)

2. **Notification System**:
   - The `is_read` flag is used to track whether a business owner has seen the activity notification
   - Activities with `is_read = false` can be displayed as new notifications in the business dashboard
   - The `created_at` timestamp allows for sorting activities chronologically

3. **Rating System**:
   - When `activity_type = 'rating'`, the `rating_value` field stores the numerical rating
   - The check constraint ensures rating values are only provided for rating activities
   - Rating activities contribute to the business profile's average rating calculation

4. **Security**:
   - RLS policies ensure that business owners can only view and update activities related to their own business profiles
   - There is no explicit INSERT policy, suggesting that activities are created through application logic or database functions

5. **Performance Considerations**:
   - Multiple indexes are defined to optimize common query patterns
   - The `created_at` index supports efficient chronological sorting
   - The `is_read` index supports efficient filtering of unread notifications

6. **Business Metrics**:
   - Activities can be aggregated to calculate engagement metrics for business profiles
   - Like and subscription counts can be derived from this table
   - Rating averages can be calculated from rating activities

7. **Data Relationships**:
   - Each activity is associated with exactly one business profile and one user
   - A user can have multiple activities for the same business profile (e.g., like, subscribe, and rate)
   - Activities serve as a historical record of user engagement with businesses
