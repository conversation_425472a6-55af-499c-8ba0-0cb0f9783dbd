# Payment Subscriptions Table Documentation

## Table Overview

The `payment_subscriptions` table in the Dukancard application manages business subscription plans and payment information. It tracks the subscription status, plan details, payment history, and lifecycle events for business profiles, enabling the subscription-based business model of the platform.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | uuid_generate_v4() | Primary key for the subscription record |
| business_profile_id | uuid | NO | | Foreign key to business_profiles.id (unique) |
| razorpay_subscription_id | text | YES | | External ID from Razorpay payment gateway |
| razorpay_customer_id | text | YES | | External customer ID from Razorpay |
| plan_id | text | NO | | Subscription plan identifier (e.g., 'free', 'basic', 'growth', 'pro', 'enterprise') |
| plan_cycle | text | NO | | Billing cycle ('monthly' or 'yearly') |
| subscription_status | text | NO | 'pending' | Current status of the subscription |
| subscription_start_date | timestamptz | YES | | Date when the subscription started |
| subscription_expiry_time | timestamptz | YES | | Date when the subscription expires |
| subscription_charge_time | timestamptz | YES | | Date when the next charge will occur |
| last_payment_id | text | YES | | ID of the most recent payment |
| last_payment_date | timestamptz | YES | | Date of the most recent payment |
| last_payment_method | text | YES | | Method used for the most recent payment |
| cancellation_requested_at | timestamptz | YES | | Date when cancellation was requested |
| cancellation_reason | text | YES | | Reason provided for cancellation |
| subscription_paused_at | timestamptz | YES | | Date when the subscription was paused/halted |
| original_plan_id | text | YES | | Original plan ID before pause/halt (for restoration on resume) |
| original_plan_cycle | text | YES | | Original plan cycle before pause/halt (for restoration on resume) |
| created_at | timestamptz | NO | now() | Timestamp when the record was created |
| updated_at | timestamptz | NO | now() | Timestamp when the record was last updated |
| cancelled_at | timestamptz | YES | | Date when the subscription was cancelled |
| last_webhook_timestamp | timestamptz | YES | | Timestamp of the last processed webhook for this subscription. Used for webhook sequence validation to prevent out-of-order processing |

## Constraints

### Primary Key
- `payment_subscriptions_pkey` - Primary key constraint on the `id` column

### Foreign Keys
- `payment_subscriptions_business_profile_id_fkey` - Foreign key constraint linking `business_profile_id` to `business_profiles.id`

### Unique Constraints
- `payment_subscriptions_business_profile_id_key` - Ensures each business profile has at most one subscription record

### Check Constraints
Multiple check constraints ensure that required fields are not null.

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| payment_subscriptions_pkey | UNIQUE | id | Primary key index |
| payment_subscriptions_business_profile_id_key | UNIQUE | business_profile_id | Unique constraint index for business_profile_id |

## Triggers

| Trigger Name | Event | Function | Description |
|-------------|-------|----------|-------------|
| enforce_product_limits_on_plan_change | UPDATE | enforce_product_limits_on_plan_change() | Automatically enforces product limits when plan_id changes |

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| Users can read their own subscriptions | SELECT | (business_profile_id IN (SELECT business_profiles.id FROM business_profiles WHERE business_profiles.id = payment_subscriptions.business_profile_id)) | |

This policy ensures that users can only view subscription information for their own business profiles.

## Related Tables

### business_profiles
The `business_profiles` table is referenced by the `business_profile_id` foreign key and contains information about the business that owns the subscription.

## Usage Notes

1. **Subscription Plans**:
   - The table supports multiple subscription plans through the `plan_id` field
   - Common values include 'free', 'basic', 'growth', 'pro', and 'enterprise'
   - Each plan has different features and limits (e.g., product limits as seen in the `check_product_limit` function)

2. **Subscription Status**:
   - The `subscription_status` field tracks the current state of the subscription
   - Common values include:
     - 'pending': Initial state before payment confirmation
     - 'active': Subscription is currently active
     - 'halted': Subscription is temporarily paused
     - 'cancelled': Subscription has been cancelled but may still be active until expiry
     - 'expired': Subscription has ended and is no longer active

3. **Payment Integration**:
   - The table integrates with Razorpay payment gateway
   - External IDs from Razorpay are stored for reference and API calls
   - Payment history is tracked through the last_payment_* fields

4. **Subscription Lifecycle**:
   - The table tracks important dates in the subscription lifecycle:
     - Creation (created_at)
     - Start (subscription_start_date)
     - Next charge (subscription_charge_time)
     - Expiry (subscription_expiry_time)
     - Cancellation request (cancellation_requested_at)
     - Actual cancellation (cancelled_at)
     - Pause (subscription_paused_at)

5. **Billing Cycles**:
   - The `plan_cycle` field indicates whether the subscription is billed monthly or yearly
   - This affects pricing and renewal dates

6. **Free Plan Handling**:
   - Based on project memory, free plan users have:
     - subscription_status set to 'active'
     - plan_id set to 'free'
     - has_active_subscription in business_profiles set to false

7. **Subscription Changes**:
   - When a subscription is cancelled, expired, or completed, the business is downgraded to the free plan
   - For UPI/emandate payments, subscription updates require creating a new subscription then cancelling the old one

8. **Business Status Impact**:
   - When a business has paused subscription (status='halted'), the business profile is set to 'offline'
   - The business cannot go online until the subscription is resumed

9. **Pause/Resume Functionality**:
   - When a subscription is paused (halted), the original plan details are stored in `original_plan_id` and `original_plan_cycle`
   - The subscription is downgraded to 'free' plan during the pause period
   - When resumed (activated), the original plan is restored from the stored values
   - This ensures seamless restoration of the user's original subscription level

10. **Security**:
    - RLS policies ensure that users can only view their own subscription information
    - The table does not have INSERT, UPDATE, or DELETE policies, suggesting these operations are handled through server-side logic or functions

11. **Product Limit Enforcement**:
    - The table is used by the `check_product_limit` function to determine subscription plan limits
    - This enables automatic enforcement of product/service limits based on the user's current plan
    - The `enforce_product_limits_on_plan_change` trigger automatically adjusts product availability when plan_id changes
    - Plan limits: Free (5), Basic (15), Growth (50), Pro/Enterprise (unlimited)

12. **Webhook Integration**:
    - Based on project memory, subscription status changes are handled exclusively through Razorpay webhooks
    - This ensures payment status is properly synchronized with the payment provider

13. **Webhook Sequence Validation**:
    - The `last_webhook_timestamp` field enables webhook sequence validation to prevent out-of-order processing
    - Each webhook event timestamp is compared against the last processed timestamp
    - Webhooks arriving significantly out of order (>5 seconds older) are rejected to maintain data consistency
    - This prevents race conditions and ensures subscription state transitions occur in the correct sequence
    - The system allows a 5-second tolerance for near-simultaneous webhook events to handle network delays
