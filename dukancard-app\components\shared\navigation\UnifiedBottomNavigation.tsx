import React, { useState } from 'react';
import { useRouter } from 'expo-router';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from '@/contexts/AuthContext';
import { Home, Search, Store, User, DukancardBottomTabs } from './BottomNavigation';
import QRScannerModal from '../../qr/QRScannerModal';

interface UnifiedBottomNavigationProps {
  activeTab?: string;
  onTabChange?: (tab: string) => void;
  showQRScanner?: boolean;
}

export default function UnifiedBottomNavigation({
  activeTab,
  onTabChange,
  showQRScanner = true
}: UnifiedBottomNavigationProps) {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const { profileStatus } = useAuth();
  const [qrScannerVisible, setQrScannerVisible] = useState(false);

  // Determine user type
  const isBusinessUser = profileStatus.roleStatus?.role === 'business';
  const isCustomerUser = profileStatus.roleStatus?.role === 'customer';

  // Handle navigation
  const handleHomePress = () => {
    // If onTabChange is available, we're within a dashboard layout, so use tab change
    if (onTabChange) {
      onTabChange('home');
    } else {
      // Otherwise, navigate to appropriate route
      if (isBusinessUser) {
        router.push('/(dashboard)/business');
      } else if (isCustomerUser) {
        router.push('/(dashboard)/customer');
      } else {
        router.push('/');
      }
    }
  };

  const handleDiscoverPress = () => {
    // If onTabChange is available, we're within a dashboard layout, so use tab change
    if (onTabChange) {
      onTabChange('discover');
    } else {
      // Otherwise, navigate to appropriate dashboard with discover tab
      if (isBusinessUser) {
        router.push('/(dashboard)/business?tab=discover');
      } else if (isCustomerUser) {
        router.push('/(dashboard)/customer?tab=discover');
      } else {
        router.push('/(auth)/login');
      }
    }
  };

  const handleDukanAIPress = () => {
    console.log('Dukan AI is coming soon!');
    // TODO: Show coming soon modal or toast
  };

  const handleAccountPress = () => {
    // If onTabChange is available, we're within a dashboard layout, so use tab change
    if (onTabChange) {
      onTabChange('profile');
    } else {
      // Otherwise, navigate to appropriate dashboard route
      if (isBusinessUser) {
        router.push('/(dashboard)/business?tab=profile');
      } else if (isCustomerUser) {
        router.push('/(dashboard)/customer?tab=profile');
      } else {
        router.push('/(auth)/login');
      }
    }
  };

  const handleQRScanPress = () => {
    setQrScannerVisible(true);
  };

  const handleQRScanSuccess = (businessSlug: string) => {
    router.push(`/business/${businessSlug}`);
    setQrScannerVisible(false);
  };

  const handleQRScannerClose = () => {
    setQrScannerVisible(false);
  };

  // Determine account label based on user type
  const getAccountLabel = () => {
    if (isBusinessUser) {
      return 'Dashboard';
    } else if (isCustomerUser) {
      return 'Profile';
    }
    return 'Account';
  };

  const tabs = [
    {
      key: 'home',
      icon: Home,
      label: 'Home',
      onPress: handleHomePress,
      isActive: activeTab === 'home',
    },
    {
      key: 'discover',
      icon: Search,
      label: 'Discover',
      onPress: handleDiscoverPress,
      isActive: activeTab === 'discover',
    },
    {
      key: 'dukan-ai',
      icon: Store,
      label: 'Dukan AI',
      onPress: handleDukanAIPress,
      isActive: false,
      disabled: true,
    },
    {
      key: 'profile',
      icon: User,
      label: getAccountLabel(),
      onPress: handleAccountPress,
      isActive: activeTab === 'profile',
    },
  ];

  return (
    <>
      <DukancardBottomTabs
        tabs={tabs}
        onQRScanPress={showQRScanner ? handleQRScanPress : undefined}
        colorScheme={colorScheme}
      />

      {/* QR Scanner Modal */}
      {showQRScanner && qrScannerVisible && (
        <QRScannerModal
          visible={qrScannerVisible}
          onClose={handleQRScannerClose}
          onScanSuccess={handleQRScanSuccess}
        />
      )}
    </>
  );
}
