import React from 'react';
import { DashboardLayout } from '@/components/shared/layout/DashboardLayout';
import { ComingSoon } from '@/components/ui/ComingSoon';

interface DiscoverScreenProps {
  userName: string;
  showNotifications?: boolean;
}

export default function DiscoverScreen({
  userName,
  showNotifications = true
}: DiscoverScreenProps) {
  return (
    <DashboardLayout
      userName={userName}
      showNotifications={showNotifications}
    >
      <ComingSoon
        title="Discovery Coming Soon"
        description="We're building an amazing discovery experience to help you find local businesses and products. Stay tuned!"
        icon="rocket"
      />
    </DashboardLayout>
  );
}
