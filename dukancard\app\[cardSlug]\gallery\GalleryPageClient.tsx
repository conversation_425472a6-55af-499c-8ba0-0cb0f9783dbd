"use client";

import { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import { X, Maximize, ArrowLeft, Loader2 } from "lucide-react";
import { GalleryImage } from "@/lib/actions/gallery";
import { Button } from "@/components/ui/button";
import { getBusinessGalleryImagesPaginated } from "@/lib/actions/gallery";
import { useInView } from "react-intersection-observer";

interface GalleryPageClientProps {
  businessProfile: {
    business_slug: string;
    business_name: string;
    id?: string;
  };
  galleryImages: GalleryImage[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  userPlan: "free" | "basic" | "growth" | "pro" | "enterprise" | "trial";
  isAuthenticated: boolean;
  currentUserId: string | null;
}

export default function GalleryPageClient({
  businessProfile,
  galleryImages: initialGalleryImages,
  totalCount: _totalCount,
  totalPages: _totalPages,
  currentPage,
  hasNextPage,
  hasPrevPage: _hasPrevPage,
  userPlan: _userPlan,
  isAuthenticated: _isAuthenticated,
  currentUserId: _currentUserId,
}: GalleryPageClientProps) {
  const [lightboxImage, setLightboxImage] = useState<string | null>(null);
  const [lightboxIndex, setLightboxIndex] = useState<number>(0);
  const [_isClient, setIsClient] = useState(false);

  // Infinite scroll state
  const [allImages, setAllImages] = useState<GalleryImage[]>(initialGalleryImages);
  const [currentPageState, setCurrentPageState] = useState(currentPage);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(hasNextPage);

  // For intersection observer to enhance performance
  const { ref: galleryRef, inView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  // Intersection observer for infinite scroll
  const { ref: loadMoreRef, inView: loadMoreInView } = useInView({
    threshold: 0.1,
  });

  // Load more images function
  const loadMoreImages = useCallback(async () => {
    if (isLoading || !hasMore) return;

    setIsLoading(true);
    try {
      const nextPage = currentPageState + 1;
      const result = await getBusinessGalleryImagesPaginated(
        businessProfile.business_slug,
        nextPage,
        20
      );

      if (result.images && result.images.length > 0) {
        setAllImages(prev => [...prev, ...result.images]);
        setCurrentPageState(nextPage);
        setHasMore(result.hasNextPage);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error loading more images:', error);
      setHasMore(false);
    } finally {
      setIsLoading(false);
    }
  }, [businessProfile.business_slug, currentPageState, isLoading, hasMore]);

  // Set client-side state
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Trigger load more when intersection observer is triggered
  useEffect(() => {
    if (loadMoreInView && hasMore && !isLoading) {
      loadMoreImages();
    }
  }, [loadMoreInView, hasMore, isLoading, loadMoreImages]);

  // Prevent body scroll when lightbox is open
  useEffect(() => {
    if (lightboxImage) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [lightboxImage]);

  // Note: We don't need to limit the images here as they are already limited at the database level
  // in the getBusinessGalleryImagesBySlug function

  // Handle opening the lightbox
  const openLightbox = (imageUrl: string, index: number) => {
    setLightboxImage(imageUrl);
    setLightboxIndex(index);
  };

  // Handle closing the lightbox
  const closeLightbox = () => {
    setLightboxImage(null);
  };



  // Handle keyboard navigation (only Escape to close)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!lightboxImage) return;

      if (e.key === "Escape") {
        closeLightbox();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [lightboxImage]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { opacity: 1, scale: 1 },
  };

  return (
    <div className="w-full max-w-screen-xl mx-auto px-4 sm:px-6 py-8">
      {/* Header with back button */}
      <div className="flex items-center mb-6">
        <Link href={`/${businessProfile.business_slug}`} passHref>
          <Button variant="ghost" size="sm" className="gap-1">
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Business Card</span>
          </Button>
        </Link>
      </div>

      {/* Gallery Header */}
      <div className="mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-2">
          {businessProfile.business_name} Gallery
        </h1>
        <p className="text-muted-foreground">
          Browse all photos
        </p>
      </div>

      {/* Gallery Grid */}
      <motion.div
        ref={galleryRef}
        className="grid grid-cols-2 sm:grid-cols-4 gap-4"
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
      >
        {allImages.map((image, index) => (
          <motion.div
            key={image.id}
            className="aspect-square relative overflow-hidden rounded-xl cursor-pointer group"
            onClick={() => openLightbox(image.url, index)}
            variants={itemVariants}
            whileHover={{ scale: 1.03 }}
            transition={{ duration: 0.3 }}
          >
            <Image
              src={image.url}
              alt={`${businessProfile.business_name} gallery image ${index + 1}`}
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-105"
              sizes="(max-width: 640px) 50vw, 25vw"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="absolute bottom-2 right-2 bg-black/50 text-white p-1.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Maximize className="w-4 h-4" />
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Load more trigger and loading indicator */}
      {hasMore && (
        <div ref={loadMoreRef} className="flex justify-center py-8">
          {isLoading ? (
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>Loading more images...</span>
            </div>
          ) : (
            <div className="text-muted-foreground text-sm">
              Scroll down to load more images
            </div>
          )}
        </div>
      )}

      {/* End of gallery indicator */}
      {!hasMore && allImages.length > 0 && (
        <div className="flex justify-center py-8">
          <div className="text-muted-foreground text-sm">
            You&apos;ve reached the end of the gallery
          </div>
        </div>
      )}

      {/* Lightbox */}
      <AnimatePresence>
        {lightboxImage && (
          <motion.div
            className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={closeLightbox}
          >
            {/* Close button */}
            <button
              className="absolute top-4 right-4 z-10 p-2 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                closeLightbox();
              }}
            >
              <X className="w-6 h-6" />
            </button>



            {/* Image */}
            <div
              className="relative w-full h-full max-w-4xl max-h-[80vh] mx-auto p-4 flex items-center justify-center"
              onClick={(e) => e.stopPropagation()}
            >
              <Image
                src={lightboxImage}
                alt={`${businessProfile.business_name} gallery image ${lightboxIndex + 1}`}
                fill
                className="object-contain"
                sizes="100vw"
                priority
              />
            </div>


          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
