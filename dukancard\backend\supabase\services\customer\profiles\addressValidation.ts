"use server";

import { createClient } from "@/utils/supabase/server";
import { isCustomerAddressComplete, getMissingAddressFields, getAddressValidationMessage, type CustomerAddressData } from "@/lib/utils/addressValidation";
import { redirect } from "next/navigation";

/**
 * Checks if customer has complete address information
 * Returns validation result and redirect URL if needed
 */
export async function validateCustomerAddress(userId: string): Promise<{
  isValid: boolean;
  missingFields?: string[];
  message?: string;
  redirectUrl?: string;
}> {
  const supabase = await createClient();
  
  try {
    // Fetch customer address data
    const { data: profile, error } = await supabase
      .from('customer_profiles')
      .select('pincode, state, city, locality, address')
      .eq('id', userId)
      .single();
    
    if (error) {
      console.error('Error fetching customer profile for address validation:', error);
      // If we can't fetch the profile, assume invalid and redirect
      return {
        isValid: false,
        message: 'Unable to verify your address information. Please update your profile.',
        redirectUrl: '/dashboard/customer/profile?message=Please update your address information'
      };
    }
    
    const addressData: CustomerAddressData = {
      pincode: profile?.pincode,
      state: profile?.state,
      city: profile?.city,
      locality: profile?.locality,
      address: profile?.address
    };
    
    const isValid = isCustomerAddressComplete(addressData);
    
    if (!isValid) {
      const missingFields = getMissingAddressFields(addressData);
      const message = getAddressValidationMessage(missingFields);
      const redirectUrl = `/dashboard/customer/profile?message=${encodeURIComponent(message)}`;
      
      return {
        isValid: false,
        missingFields,
        message,
        redirectUrl
      };
    }
    
    return { isValid: true };
    
  } catch (error) {
    console.error('Unexpected error during address validation:', error);
    return {
      isValid: false,
      message: 'An error occurred while validating your address. Please update your profile.',
      redirectUrl: '/dashboard/customer/profile?message=Please update your address information'
    };
  }
}

/**
 * Middleware function to check address and redirect if incomplete
 * Use this in customer dashboard pages
 */
export async function requireCompleteAddress(userId: string): Promise<void> {
  const validation = await validateCustomerAddress(userId);

  if (!validation.isValid && validation.redirectUrl) {
    redirect(validation.redirectUrl);
  }
}

/**
 * Checks if customer has complete name information
 * Returns validation result and redirect URL if needed
 */
export async function validateCustomerName(userId: string): Promise<{
  isValid: boolean;
  message?: string;
  redirectUrl?: string;
}> {
  const supabase = await createClient();

  try {
    // Fetch customer name data
    const { data: profile, error } = await supabase
      .from('customer_profiles')
      .select('name')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching customer profile for name validation:', error);
      // If we can't fetch the profile, assume invalid and redirect
      return {
        isValid: false,
        message: 'Unable to verify your profile information. Please update your profile.',
        redirectUrl: '/dashboard/customer/profile?message=Please update your profile information'
      };
    }

    // Check if name is present and not empty
    const isValid = !!(profile?.name && profile.name.trim() !== '');

    if (!isValid) {
      const message = 'Please complete your name in your profile to access the dashboard.';
      const redirectUrl = `/dashboard/customer/profile?message=${encodeURIComponent(message)}`;

      return {
        isValid: false,
        message,
        redirectUrl
      };
    }

    return { isValid: true };

  } catch (error) {
    console.error('Unexpected error during name validation:', error);
    return {
      isValid: false,
      message: 'An error occurred while validating your profile. Please update your profile.',
      redirectUrl: '/dashboard/customer/profile?message=Please update your profile information'
    };
  }
}

/**
 * Middleware function to check name and redirect if incomplete
 * Use this in customer dashboard pages
 */
export async function requireCompleteName(userId: string): Promise<void> {
  const validation = await validateCustomerName(userId);

  if (!validation.isValid && validation.redirectUrl) {
    redirect(validation.redirectUrl);
  }
}

/**
 * Middleware function to check both address and name, redirect if incomplete
 * Use this in customer dashboard pages (except settings page)
 * Settings page is exempt from address validation
 */
export async function requireCompleteProfile(userId: string, exemptFromAddressValidation: boolean = false): Promise<void> {
  // Always check name (required for all dashboard access)
  await requireCompleteName(userId);

  // Only check address if not exempt (settings page is exempt)
  if (!exemptFromAddressValidation) {
    await requireCompleteAddress(userId);
  }
}

/**
 * Get customer address data for forms
 */
export async function getCustomerAddressData(userId: string): Promise<{
  data?: CustomerAddressData;
  error?: string;
}> {
  const supabase = await createClient();
  
  try {
    const { data: profile, error } = await supabase
      .from('customer_profiles')
      .select('pincode, state, city, locality, address')
      .eq('id', userId)
      .single();
    
    if (error) {
      console.error('Error fetching customer address data:', error);
      return { error: 'Failed to fetch address data' };
    }
    
    return {
      data: {
        pincode: profile?.pincode,
        state: profile?.state,
        city: profile?.city,
        locality: profile?.locality,
        address: profile?.address
      }
    };
    
  } catch (error) {
    console.error('Unexpected error fetching address data:', error);
    return { error: 'An unexpected error occurred' };
  }
}
