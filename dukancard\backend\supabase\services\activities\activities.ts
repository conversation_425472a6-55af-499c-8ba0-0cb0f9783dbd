"use server";

import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { revalidatePath } from "next/cache";

/**
 * Database Triggers Documentation
 *
 * The following triggers are set up in Supabase to automatically track activities:
 *
 * 1. add_like_activity() - Trigger function for likes
 * ```sql
 * CREATE OR REPLACE FUNCTION add_like_activity()
 * RETURNS TRIGGER AS $$
 * BEGIN
 *   -- Insert a new activity record
 *   INSERT INTO business_activities (
 *     business_profile_id,
 *     user_id,
 *     activity_type,
 *     created_at
 *   ) VALUES (
 *     NEW.business_profile_id,
 *     NEW.user_id,
 *     'like',
 *     NEW.created_at
 *   );
 *
 *   RETURN NEW;
 * END;
 * $$ LANGUAGE plpgsql;
 *
 * -- Trigger to call the function when a like is added
 * CREATE TRIGGER trigger_add_like_activity
 * AFTER INSERT ON likes
 * FOR EACH ROW
 * EXECUTE FUNCTION add_like_activity();
 * ```
 *
 * 1a. delete_like_activity() - Trigger function for removing like activities
 * ```sql
 * CREATE OR REPLACE FUNCTION delete_like_activity()
 * RETURNS TRIGGER AS $$
 * BEGIN
 *   -- Delete the activity record
 *   DELETE FROM business_activities
 *   WHERE business_profile_id = OLD.business_profile_id
 *   AND user_id = OLD.user_id
 *   AND activity_type = 'like';
 *
 *   RETURN OLD;
 * END;
 * $$ LANGUAGE plpgsql;
 *
 * -- Trigger to call the function when a like is deleted
 * CREATE TRIGGER trigger_delete_like_activity
 * AFTER DELETE ON likes
 * FOR EACH ROW
 * EXECUTE FUNCTION delete_like_activity();
 * ```
 *
 * 2. add_subscription_activity() - Trigger function for subscriptions
 * ```sql
 * CREATE OR REPLACE FUNCTION add_subscription_activity()
 * RETURNS TRIGGER AS $$
 * BEGIN
 *   -- Insert a new activity record
 *   INSERT INTO business_activities (
 *     business_profile_id,
 *     user_id,
 *     activity_type,
 *     created_at
 *   ) VALUES (
 *     NEW.business_profile_id,
 *     NEW.user_id,
 *     'subscribe',
 *     NEW.created_at
 *   );
 *
 *   RETURN NEW;
 * END;
 * $$ LANGUAGE plpgsql;
 *
 * -- Trigger to call the function when a subscription is added
 * CREATE TRIGGER trigger_add_subscription_activity
 * AFTER INSERT ON subscriptions
 * FOR EACH ROW
 * EXECUTE FUNCTION add_subscription_activity();
 * ```
 *
 * 2a. delete_subscription_activity() - Trigger function for removing subscription activities
 * ```sql
 * CREATE OR REPLACE FUNCTION delete_subscription_activity()
 * RETURNS TRIGGER AS $$
 * BEGIN
 *   -- Delete the activity record
 *   DELETE FROM business_activities
 *   WHERE business_profile_id = OLD.business_profile_id
 *   AND user_id = OLD.user_id
 *   AND activity_type = 'subscribe';
 *
 *   RETURN OLD;
 * END;
 * $$ LANGUAGE plpgsql;
 *
 * -- Trigger to call the function when a subscription is deleted
 * CREATE TRIGGER trigger_delete_subscription_activity
 * AFTER DELETE ON subscriptions
 * FOR EACH ROW
 * EXECUTE FUNCTION delete_subscription_activity();
 * ```
 *
 * 3. add_rating_activity() - Trigger function for ratings
 * ```sql
 * CREATE OR REPLACE FUNCTION add_rating_activity()
 * RETURNS TRIGGER AS $$
 * BEGIN
 *   -- Check if this is an update or insert
 *   IF TG_OP = 'UPDATE' THEN
 *     -- For updates, only add activity if rating changed
 *     IF NEW.rating = OLD.rating THEN
 *       RETURN NEW;
 *     END IF;
 *   END IF;
 *
 *   -- Insert a new activity record
 *   INSERT INTO business_activities (
 *     business_profile_id,
 *     user_id,
 *     activity_type,
 *     rating_value,
 *     created_at
 *   ) VALUES (
 *     NEW.business_profile_id,
 *     NEW.user_id,
 *     'rating',
 *     NEW.rating,
 *     NEW.updated_at
 *   );
 *
 *   RETURN NEW;
 * END;
 * $$ LANGUAGE plpgsql;
 *
 * -- Trigger to call the function when a rating is added or updated
 * CREATE TRIGGER trigger_add_rating_activity
 * AFTER INSERT OR UPDATE OF rating ON ratings_reviews
 * FOR EACH ROW
 * EXECUTE FUNCTION add_rating_activity();
 * ```
 *
 * 3a. delete_rating_activity() - Trigger function for removing rating activities
 * ```sql
 * CREATE OR REPLACE FUNCTION delete_rating_activity()
 * RETURNS TRIGGER AS $$
 * BEGIN
 *   -- Delete the activity record
 *   DELETE FROM business_activities
 *   WHERE business_profile_id = OLD.business_profile_id
 *   AND user_id = OLD.user_id
 *   AND activity_type = 'rating';
 *
 *   RETURN OLD;
 * END;
 * $$ LANGUAGE plpgsql;
 *
 * -- Trigger to call the function when a rating is deleted
 * CREATE TRIGGER trigger_delete_rating_activity
 * AFTER DELETE ON ratings_reviews
 * FOR EACH ROW
 * EXECUTE FUNCTION delete_rating_activity();
 * ```
 */

/**
 * Table Structure
 *
 * The business_activities table is structured as follows:
 * ```sql
 * CREATE TABLE business_activities (
 *   id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
 *   business_profile_id UUID NOT NULL REFERENCES business_profiles(id) ON DELETE CASCADE,
 *   user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
 *   activity_type TEXT NOT NULL CHECK (activity_type IN ('like', 'subscribe', 'rating')),
 *   rating_value INTEGER,
 *   created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
 *   is_read BOOLEAN NOT NULL DEFAULT false,
 *
 *   -- Add constraint to ensure rating_value is only set for rating activities
 *   CONSTRAINT rating_value_only_for_ratings CHECK (
 *     (activity_type = 'rating' AND rating_value IS NOT NULL) OR
 *     (activity_type != 'rating' AND rating_value IS NULL)
 *   )
 * );
 *
 * -- Indexes for better performance
 * CREATE INDEX idx_business_activities_business_profile_id ON business_activities(business_profile_id);
 * CREATE INDEX idx_business_activities_user_id ON business_activities(user_id);
 * CREATE INDEX idx_business_activities_is_read ON business_activities(is_read);
 * CREATE INDEX idx_business_activities_activity_type ON business_activities(activity_type);
 * CREATE INDEX idx_business_activities_created_at ON business_activities(created_at);
 * ```
 */

/**
 * Row Level Security (RLS) Policies
 *
 * The following RLS policies are set up in Supabase to secure the business_activities table:
 *
 * 1. Select Policy - Allows business owners to read their own activities
 * ```sql
 * CREATE POLICY business_activities_select_policy ON business_activities
 *   FOR SELECT
 *   USING (auth.uid() = business_profile_id);
 * ```
 *
 * 2. Update Policy - Allows business owners to update their own activities (for marking as read)
 * ```sql
 * CREATE POLICY business_activities_update_policy ON business_activities
 *   FOR UPDATE
 *   USING (auth.uid() = business_profile_id);
 * ```
 */

// Define types for activities
export interface BusinessActivity {
  id: string;
  business_profile_id: string;
  user_id: string;
  activity_type: "like" | "subscribe" | "rating";
  rating_value: number | null;
  created_at: string;
  is_read: boolean;
  user_profile?: {
    name?: string | null;
    avatar_url?: string | null;
    email?: string | null;
    is_business?: boolean;
    business_name?: string | null;
    business_slug?: string | null;
    logo_url?: string | null;
  };
}

export type ActivitySortBy = "newest" | "oldest" | "unread_first";

/**
 * Fetches activities for a business with pagination and sorting
 * Optionally marks fetched activities as read automatically
 */
export async function getBusinessActivities({
  businessProfileId,
  page = 1,
  pageSize = 15,
  sortBy = "newest",
  filterBy = "all",
  autoMarkAsRead = true, // New parameter to control auto-marking as read
}: {
  businessProfileId: string;
  page?: number;
  pageSize?: number;
  sortBy?: ActivitySortBy;
  filterBy?: "all" | "like" | "subscribe" | "rating" | "unread";
  autoMarkAsRead?: boolean;
}) {
  const supabase = await createClient();
  const supabaseAdmin = createAdminClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return { activities: [], count: 0, error: "Not authenticated" };
  }

  // Verify the user is the owner of the business
  if (user.id !== businessProfileId) {
    return { activities: [], count: 0, error: "Unauthorized" };
  }

  try {
    // Calculate pagination
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    // Build the query
    let query = supabase
      .from("business_activities")
      .select("*", { count: "exact" })
      .eq("business_profile_id", businessProfileId);

    // Apply filter
    if (filterBy === "like") {
      query = query.eq("activity_type", "like");
    } else if (filterBy === "subscribe") {
      query = query.eq("activity_type", "subscribe");
    } else if (filterBy === "rating") {
      query = query.eq("activity_type", "rating");
    } else if (filterBy === "unread") {
      query = query.eq("is_read", false);
    }

    // Apply sorting
    switch (sortBy) {
      case "oldest":
        query = query.order("created_at", { ascending: true });
        break;
      case "unread_first":
        query = query.order("is_read", { ascending: true }).order("created_at", { ascending: false });
        break;
      case "newest":
      default:
        query = query.order("created_at", { ascending: false });
        break;
    }

    // Apply pagination
    query = query.range(from, to);

    // Execute the query
    const { data: activities, error, count } = await query;

    if (error) {
      console.error("Error fetching business activities:", error);
      return { activities: [], count: 0, error: error.message };
    }

    // Get user profiles for the activities
    const userIds = activities.map((activity) => activity.user_id);

    // Fetch both customer and business profiles
    const [customerProfiles, businessProfiles] = await Promise.all([
      supabaseAdmin
        .from("customer_profiles")
        .select("id, name, avatar_url, email")
        .in("id", userIds),
      supabaseAdmin
        .from("business_profiles")
        .select("id, business_name, business_slug, logo_url")
        .in("id", userIds),
    ]);

    // Combine the profiles
    const userProfiles = new Map();

    // Add customer profiles to the map
    customerProfiles.data?.forEach((profile) => {
      userProfiles.set(profile.id, {
        name: profile.name,
        avatar_url: profile.avatar_url,
        email: profile.email,
        is_business: false,
      });
    });

    // Add business profiles to the map, overriding customer profiles if both exist
    businessProfiles.data?.forEach((profile) => {
      const existingProfile = userProfiles.get(profile.id) || {};
      userProfiles.set(profile.id, {
        ...existingProfile,
        business_name: profile.business_name,
        business_slug: profile.business_slug,
        logo_url: profile.logo_url,
        is_business: true,
      });
    });

    // Attach user profiles to activities
    const activitiesWithProfiles = activities.map((activity) => ({
      ...activity,
      user_profile: userProfiles.get(activity.user_id) || {},
    }));

    // Auto-mark fetched activities as read if enabled
    if (autoMarkAsRead && activities.length > 0) {
      // Get IDs of unread activities
      const unreadActivityIds = activities
        .filter(activity => !activity.is_read)
        .map(activity => activity.id);

      // Only proceed if there are unread activities
      if (unreadActivityIds.length > 0) {
        // Mark these activities as read
        const { error: markError } = await supabase
          .from("business_activities")
          .update({ is_read: true })
          .eq("business_profile_id", businessProfileId)
          .in("id", unreadActivityIds);

        if (markError) {
          console.error("Error auto-marking activities as read:", markError);
        } else {
          // Update the activities in our result to reflect they're now read
          activitiesWithProfiles.forEach(activity => {
            if (unreadActivityIds.includes(activity.id)) {
              activity.is_read = true;
            }
          });
        }
      }
    }

    return {
      activities: activitiesWithProfiles,
      count: count || 0,
      error: null,
    };
  } catch (error) {
    console.error("Unexpected error fetching business activities:", error);
    return {
      activities: [],
      count: 0,
      error: "An unexpected error occurred",
    };
  }
}

/**
 * Marks activities as read
 * Handles pagination for large numbers of activities to work around Supabase's 1000 row limit
 */
export async function markActivitiesAsRead({
  businessProfileId,
  activityIds,
}: {
  businessProfileId: string;
  activityIds: string[] | "all";
}) {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return { success: false, error: "Not authenticated" };
  }

  // Verify the user is the owner of the business
  if (user.id !== businessProfileId) {
    return { success: false, error: "Unauthorized" };
  }

  try {
    // If marking specific activities as read
    if (activityIds !== "all") {
      // Handle case where we have specific activity IDs
      const { error } = await supabase
        .from("business_activities")
        .update({ is_read: true })
        .eq("business_profile_id", businessProfileId)
        .in("id", activityIds);

      if (error) {
        console.error("Error marking specific activities as read:", error);
        return { success: false, error: error.message };
      }
    } else {
      // Handle "mark all as read" with pagination to work around Supabase's 1000 row limit
      const BATCH_SIZE = 1000; // Maximum number of rows to update at once
      let hasMore = true;
      let processedCount = 0;

      while (hasMore) {
        // Get a batch of unread activity IDs
        const { data: unreadActivities, error: fetchError } = await supabase
          .from("business_activities")
          .select("id")
          .eq("business_profile_id", businessProfileId)
          .eq("is_read", false)
          .limit(BATCH_SIZE);

        if (fetchError) {
          console.error("Error fetching unread activities:", fetchError);
          return { success: false, error: fetchError.message };
        }

        // If no more unread activities, we're done
        if (!unreadActivities || unreadActivities.length === 0) {
          hasMore = false;
          break;
        }

        // Extract IDs from the batch
        const batchIds = unreadActivities.map(activity => activity.id);

        // Mark this batch as read
        const { error: updateError } = await supabase
          .from("business_activities")
          .update({ is_read: true })
          .eq("business_profile_id", businessProfileId)
          .in("id", batchIds);

        if (updateError) {
          console.error("Error marking batch as read:", updateError);
          return { success: false, error: updateError.message };
        }

        // Update processed count and check if we need to continue
        processedCount += batchIds.length;
        hasMore = batchIds.length === BATCH_SIZE; // If we got a full batch, there might be more
      }

      console.log(`Marked ${processedCount} activities as read`);
    }

    // Revalidate the activities page
    revalidatePath("/dashboard/business/activities");

    return { success: true, error: null };
  } catch (error) {
    console.error("Unexpected error marking activities as read:", error);
    return {
      success: false,
      error: "An unexpected error occurred",
    };
  }
}

/**
 * Gets the count of unread activities
 */
export async function getUnreadActivitiesCount(businessProfileId: string) {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return { count: 0, error: "Not authenticated" };
  }

  // Verify the user is the owner of the business
  if (user.id !== businessProfileId) {
    return { count: 0, error: "Unauthorized" };
  }

  try {
    const { count, error } = await supabase
      .from("business_activities")
      .select("*", { count: "exact", head: true })
      .eq("business_profile_id", businessProfileId)
      .eq("is_read", false);

    if (error) {
      console.error("Error getting unread activities count:", error);
      return { count: 0, error: error.message };
    }

    return { count: count || 0, error: null };
  } catch (error) {
    console.error("Unexpected error getting unread activities count:", error);
    return { count: 0, error: "An unexpected error occurred" };
  }
}
