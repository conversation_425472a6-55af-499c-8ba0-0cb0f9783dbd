"use server";

import { createAdminClient } from "@/utils/supabase/admin";
import { SitemapProfileData } from "./types";

/**
 * Securely fetch business profiles for sitemap using the service role key
 */
export async function getSecureBusinessProfilesForSitemap(): Promise<{
  data?: SitemapProfileData[];
  error?: string;
}> {
  try {
    // Use the admin client with service role key to bypass RLS
    const supabaseAdmin = createAdminClient();

    // Fetch all business profiles with status "online"
    const { data: profiles, error: profilesError } = await supabaseAdmin
      .from("business_profiles")
      .select("business_slug, updated_at")
      .eq("status", "online") // Only fetch online profiles
      .not("business_slug", "is", null); // Ensure business_slug is not null

    if (profilesError) {
      return { error: "Database error fetching profiles." };
    }

    // If there are no profiles, return empty array
    if (!profiles || profiles.length === 0) {
      return { data: [] };
    }

    // Create a map to deduplicate by business_slug
    const uniqueProfiles = new Map<string, { business_slug: string, updated_at: string }>();

    // Add all profiles to the map (this automatically deduplicates by business_slug)
    profiles.forEach(profile => {
      if (profile.business_slug) {
        uniqueProfiles.set(profile.business_slug, {
          business_slug: profile.business_slug,
          updated_at: profile.updated_at
        });
      }
    });

    // Convert map values to array
    const combinedProfiles = Array.from(uniqueProfiles.values());

    // Return the deduplicated profiles
    return { data: combinedProfiles };
  } catch (_e) {
    return { error: "An unexpected error occurred." };
  }
}
