"use client";

import { useTransition } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { toast } from "sonner";
import { ShoppingBag, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import StandaloneProductForm, { ProductFormValues } from "../components/StandaloneProductForm";
import { addProductService } from "@/backend/supabase/services/products/addProduct";
import { ProductVariant } from "@/types/variants";
import { addProductVariant } from "../actions/addVariant";

interface AddProductClientProps {
  planLimit: number;
  currentCount: number;
  currentAvailableCount: number;
}

export default function AddProductClient({ planLimit, currentCount, currentAvailableCount }: AddProductClientProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.4
      }
    }
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        delay: 0.1,
        duration: 0.4,
        type: "spring",
        stiffness: 200,
        damping: 20
      }
    }
  };

  const handleSubmit = async (
    values: ProductFormValues & { variants?: ProductVariant[] },
    imageFiles?: (File | null)[],
    featuredImageIndex?: number,
    _removedImageIndices?: number[]
  ) => {
    startTransition(async () => {
      const formData = new FormData();

      // Add form values
      Object.entries(values).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          if (typeof value === "boolean") {
            formData.append(key, value.toString());
          } else {
            formData.append(key, String(value));
          }
        }
      });

      // Add featured image index
      if (featuredImageIndex !== undefined) {
        formData.append("featuredImageIndex", String(featuredImageIndex));
      }

      // Add image files
      if (imageFiles && imageFiles.length > 0) {
        imageFiles.forEach((file, index) => {
          if (file) {
            formData.append(`productImage_${index}`, file);
          }
        });
      }

      try {
        const result = await addProductService(formData);

        if (result.success && result.data) {
          // If variants were provided, create them after product creation
          if (values.variants && values.variants.length > 0) {
            toast.success("Product created! Adding variants...");

            let variantSuccessCount = 0;
            let variantErrorCount = 0;

            // Create each variant
            for (const variant of values.variants) {
              try {
                const variantFormData = new FormData();
                variantFormData.append('product_id', result.data.id!);
                variantFormData.append('variant_name', variant.variant_name);
                variantFormData.append('variant_values', JSON.stringify(variant.variant_values));
                // Handle pricing - ensure proper format
                if (variant.base_price !== undefined && variant.base_price !== null && variant.base_price > 0) {
                  variantFormData.append('base_price', variant.base_price.toString());
                }
                if (variant.discounted_price !== undefined && variant.discounted_price !== null && variant.discounted_price > 0) {
                  variantFormData.append('discounted_price', variant.discounted_price.toString());
                }
                variantFormData.append('is_available', variant.is_available ? 'true' : 'false');
                variantFormData.append('featured_image_index', variant.featured_image_index.toString());

                // Add variant images if they exist
                if (variant._imageFiles && variant._imageFiles.length > 0) {
                  variant._imageFiles.forEach((file, index) => {
                    if (file) {
                      variantFormData.append(`images[${index}]`, file);
                    }
                  });
                }

                const variantResult = await addProductVariant(variantFormData);
                if (variantResult.success) {
                  variantSuccessCount++;
                } else {
                  variantErrorCount++;
                  console.error('Failed to create variant:', variant.variant_name, variantResult.error);
                }
              } catch (variantError) {
                variantErrorCount++;
                console.error('Error creating variant:', variant.variant_name, variantError);
              }
            }

            if (variantSuccessCount > 0) {
              toast.success(`Product and ${variantSuccessCount} variant${variantSuccessCount > 1 ? 's' : ''} created successfully!`);
            }
            if (variantErrorCount > 0) {
              toast.warning(`Product created, but ${variantErrorCount} variant${variantErrorCount > 1 ? 's' : ''} failed to create. You can add them later by editing the product.`);
            }
          } else {
            toast.success("Product added successfully!");
          }

          router.push("/dashboard/business/products");
        } else {
          const errorMessage = result.error || "Failed to add product";

          // Show user-friendly error message
          if (errorMessage.includes("Image exceeds 15MB limit")) {
            toast.error("Image too large", {
              description: "Please select images smaller than 15MB each"
            });
          } else if (errorMessage.includes("Invalid file type")) {
            toast.error("Invalid file type", {
              description: "Please select JPG, PNG, WebP, or GIF images"
            });
          } else if (errorMessage.includes("Body exceeded")) {
            toast.error("Upload size limit exceeded", {
              description: "Please try uploading fewer images or smaller file sizes"
            });
          } else {
            toast.error("Failed to add product", {
              description: errorMessage
            });
          }
        }
      } catch (error) {
        console.error("Error adding product:", error);
        toast.error("An unexpected error occurred");
      }
    });
  };

  return (
    <motion.div
      className="w-full max-w-5xl mx-auto px-4 sm:px-6 py-6 sm:py-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Back button */}
      <Button
        variant="ghost"
        size="sm"
        className="mb-4 text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-200"
        onClick={() => router.push('/dashboard/business/products')}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Products
      </Button>

      {/* Header */}
      <motion.div
        className="mb-6 sm:mb-8"
        variants={headerVariants}
      >
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center gap-3">
            <div className="p-3 rounded-xl bg-gradient-to-br from-amber-100 to-amber-50 dark:from-amber-900/20 dark:to-amber-800/10 text-amber-600 dark:text-amber-400">
              <ShoppingBag className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-neutral-800 dark:text-neutral-100">
                Add New Product
              </h1>
              <p className="text-sm text-neutral-500 dark:text-neutral-400 mt-1">
                Create a new product or service to showcase in your business profile
              </p>
            </div>
          </div>
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-center px-4 py-2 bg-neutral-100 dark:bg-neutral-800 rounded-lg">
              <div className="text-sm font-medium">
                <span className="text-neutral-500 dark:text-neutral-400">Total Products: </span>
                <span className="text-neutral-800 dark:text-neutral-200">{currentCount}</span>
              </div>
            </div>
            <div className="flex items-center justify-center px-4 py-2 bg-neutral-100 dark:bg-neutral-800 rounded-lg">
              <div className="text-sm font-medium">
                <span className="text-neutral-500 dark:text-neutral-400">Available: </span>
                <span className="text-neutral-800 dark:text-neutral-200">{currentAvailableCount}</span>
                <span className="text-neutral-500 dark:text-neutral-400"> of </span>
                <span className={`${currentAvailableCount >= planLimit ? "text-red-500 dark:text-red-400 font-bold" : "text-neutral-800 dark:text-neutral-200"}`}>
                  {planLimit === Infinity ? "Unlimited" : planLimit}
                </span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Form */}
      <StandaloneProductForm
        onSubmit={handleSubmit}
        isSubmitting={isPending}
        isEditing={false}
        planLimit={planLimit}
        currentAvailableCount={currentAvailableCount}
      />
    </motion.div>
  );
}
