"use client";

import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useCategoryContext } from "../context/CategoryContext";

export default function ErrorSection() {
  const { searchError } = useCategoryContext();

  if (!searchError) return null;

  return (
    <Alert variant="destructive">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>{searchError}</AlertDescription>
    </Alert>
  );
}
