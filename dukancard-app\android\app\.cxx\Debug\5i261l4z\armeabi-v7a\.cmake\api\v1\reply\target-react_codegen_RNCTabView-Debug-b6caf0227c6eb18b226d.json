{"artifacts": [{"path": "RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/./RNCTabView-generated.cpp.o"}, {"path": "RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/./react/renderer/components/RNCTabView/ComponentDescriptors.cpp.o"}, {"path": "RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/./react/renderer/components/RNCTabView/EventEmitters.cpp.o"}, {"path": "RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/./react/renderer/components/RNCTabView/Props.cpp.o"}, {"path": "RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/./react/renderer/components/RNCTabView/RNCTabViewJSI-generated.cpp.o"}, {"path": "RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/./react/renderer/components/RNCTabView/ShadowNodes.cpp.o"}, {"path": "RNCTabView_autolinked_build/CMakeFiles/react_codegen_RNCTabView.dir/./react/renderer/components/RNCTabView/States.cpp.o"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_compile_options", "target_include_directories", "target_link_libraries"], "files": ["C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-bottom-tabs/android/build/generated/source/codegen/jni/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 11, "parent": 0}, {"command": 1, "file": 0, "line": 28, "parent": 0}, {"command": 2, "file": 0, "line": 17, "parent": 0}, {"command": 3, "file": 0, "line": 19, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 2, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 2, "fragment": "-fexceptions"}, {"backtrace": 2, "fragment": "-frtti"}, {"backtrace": 2, "fragment": "-std=c++20"}, {"backtrace": 2, "fragment": "-Wall"}, {"backtrace": 0, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 0, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 0, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 0, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "includes": [{"backtrace": 3, "path": "C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-bottom-tabs/android/build/generated/source/codegen/jni/."}, {"backtrace": 3, "path": "C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-bottom-tabs/android/build/generated/source/codegen/jni/react/renderer/components/RNCTabView"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/6e9cabc4b841ecb8bc48e2ce54799a1d/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "react_codegen_RNCTabView::@54948b52a0aeebf4e5a8", "name": "react_codegen_RNCTabView", "paths": {"build": "RNCTabView_autolinked_build", "source": "C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-bottom-tabs/android/build/generated/source/codegen/jni"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-bottom-tabs/android/build/generated/source/codegen/jni/RNCTabView-generated.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-bottom-tabs/android/build/generated/source/codegen/jni/react/renderer/components/RNCTabView/ComponentDescriptors.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-bottom-tabs/android/build/generated/source/codegen/jni/react/renderer/components/RNCTabView/EventEmitters.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-bottom-tabs/android/build/generated/source/codegen/jni/react/renderer/components/RNCTabView/Props.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-bottom-tabs/android/build/generated/source/codegen/jni/react/renderer/components/RNCTabView/RNCTabViewJSI-generated.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-bottom-tabs/android/build/generated/source/codegen/jni/react/renderer/components/RNCTabView/ShadowNodes.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-bottom-tabs/android/build/generated/source/codegen/jni/react/renderer/components/RNCTabView/States.cpp", "sourceGroupIndex": 0}], "type": "OBJECT_LIBRARY"}