"use server";

import { z } from "zod";
import { createClient } from "@/utils/supabase/server"; // Import the helper
import { PasswordComplexitySchema } from "@/lib/schemas/authSchemas"; // Import schemas

// Import IndianMobileSchema for mobile validation
import { IndianMobileSchema } from "@/lib/schemas/authSchemas";

// Mobile-only registration schema
const MobileRegistrationSchema = z
  .object({
    mobile: IndianMobileSchema,
    name: z.string().min(2, { message: "Name must be at least 2 characters" }),
    password: PasswordComplexitySchema,
    confirmPassword: z.string(),
    acceptTerms: z.boolean().refine((val) => val === true, {
      message: "You must accept the terms and conditions",
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

// Form schema for server-side validation
const formSchema = MobileRegistrationSchema;



// Define the return type for the action
interface ActionResult {
  success: boolean;
  message?: string; // Add optional message property
  error?: string;
  fieldErrors?: { [key: string]: string[] | undefined }; // For Zod errors
}

export async function registerUser(
  formData: unknown // Receive raw form data
): Promise<ActionResult> {
  // 1. Validate the form data on the server
  const validatedFields = formSchema.safeParse(formData);

  if (!validatedFields.success) {
    // Validation error occurred
    return {
      success: false,
      error: "Invalid form data provided.",
      fieldErrors: validatedFields.error.flatten().fieldErrors,
    };
  }

  // Extract mobile, password, name from validated data
  const { mobile, password, name } = validatedFields.data;

  // Use the centralized helper function to create an authenticated client
  const supabase = await createClient();

  try {
    // Ensure mobile number is exactly 10 digits
    if (!/^\d{10}$/.test(mobile)) {
      return {
        success: false,
        error: "Please enter a valid 10-digit mobile number",
      };
    }

    const phoneNumber = `+91${mobile}`;

    // Use phone authentication for mobile registration
    // Supabase auth will automatically handle duplicate phone numbers
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      phone: phoneNumber,
      password: password,
      options: {
        data: {
          name: name,
          full_name: name,
          auth_type: "phone", // Use "phone" for consistency
        },
      },
    });

    if (signUpError) {
      // Supabase SignUp Error occurred
      // Check for the specific error message first
      if (
        signUpError.message.toLowerCase().includes("user already registered")
      ) {
        return {
          success: false,
          error: "An account with this mobile number already exists. Please log in instead.",
        };
      } else {
        // Handle other sign-up errors
        return {
          success: false,
          error: `Registration failed: ${signUpError.message}`,
        };
      }
    }

    // Check if user object exists (it should on success)
    if (!signUpData.user) {
      // SignUp successful but no user object returned
      return {
        success: false,
        error: "User account could not be created.",
      };
    }

    // CRITICAL CHECK for existing verified users:
    // If signUp doesn't error, but the user already exists (e.g., identities might be empty or specific),
    // treat it as "Account Exists". A truly new user usually has an identity immediately.
    // This check might need refinement based on exact Supabase behavior.
    // Checking if identities array is empty is a common indicator.
    if (signUpData.user.identities && signUpData.user.identities.length === 0) {
      // SignUp succeeded but user might already exist (empty identities)
      return {
        success: false,
        error: "An account with this mobile number already exists. Please log in instead.",
      };
    }
    // If we passed the error checks and the existing user check, proceed.

    // Update the auth.users full_name for mobile registration
    // This ensures the Display name appears correctly in Supabase Auth dashboard
    try {
      const { error: updateError } = await supabase.auth.updateUser({
        data: {
          full_name: name,
        }
      });

      if (updateError) {
        console.warn('Failed to update user full_name:', updateError.message);
        // Don't fail the registration for this, just log the warning
      }
    } catch (updateErr) {
      console.warn('Unexpected error updating user full_name:', updateErr);
      // Don't fail the registration for this
    }

    // 3. Success (Auth user created)
    return {
      success: true,
      message: "Registration successful! You can now log in with your mobile number.",
    };
  } catch (error) {
    console.error("Unexpected error in registerUser:", error);
    // Catch any unexpected errors during the process
    // Unexpected Server Action Error occurred
    return {
      success: false,
      error: "An unexpected error occurred during registration.",
    };
  }
}


