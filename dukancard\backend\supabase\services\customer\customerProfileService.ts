/**
 * Customer Profile Service for Next Js
 * 
 * Handles all customer profile operations using direct Supabase client calls
 * with Row Level Security (RLS) policies for security.
 * 
 * This service replaces the need for API routes by leveraging:
 * - RLS policies for security (users can only access their own profiles)
 * - Public read access for profile discovery
 * - Direct Supabase client calls for better performance
 */

import { createClient } from '@/utils/supabase/server';

// Types for customer profile operations
export interface CustomerProfile {
  id: string;
  name: string;
  email: string;
  phone?: string;
  avatar_url?: string;
  address_line?: string;
  pincode?: string;
  city?: string;
  state?: string;
  locality?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateCustomerProfileData {
  name: string;
  email: string;
  phone?: string;
  avatar_url?: string;
  address_line?: string;
  pincode?: string;
  city?: string;
  state?: string;
  locality?: string;
}

export interface UpdateCustomerProfileData {
  name?: string;
  email?: string;
  phone?: string;
  avatar_url?: string;
  address_line?: string;
  pincode?: string;
  city?: string;
  state?: string;
  locality?: string;
}

export interface ServiceResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Get the current user's customer profile
 */
export async function getCustomerProfile(): Promise<ServiceResult<CustomerProfile | null>> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return { success: false, error: 'Authentication required' };
    }

    const { data: profile, error } = await supabase
      .from('customer_profiles')
      .select('*')
      .eq('id', user.id)
      .maybeSingle();

    if (error) {
      console.error('[CUSTOMER_PROFILE_SERVICE] Error fetching profile:', error);
      return { success: false, error: 'Failed to fetch customer profile' };
    }

    return { success: true, data: profile };
  } catch (error) {
    console.error('[CUSTOMER_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Check if the current user has a customer profile
 */
export async function checkCustomerProfileExists(): Promise<ServiceResult<boolean>> {
  try {
    const result = await getCustomerProfile();
    if (!result.success) {
      return { success: false, error: result.error };
    }
    
    return { success: true, data: !!result.data };
  } catch (error) {
    console.error('[CUSTOMER_PROFILE_SERVICE] Error checking profile existence:', error);
    return { success: false, error: 'Failed to check profile existence' };
  }
}

/**
 * Create a new customer profile
 * RLS policy ensures users can only create their own profile
 */
export async function createCustomerProfile(
  profileData: CreateCustomerProfileData
): Promise<ServiceResult<CustomerProfile>> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return { success: false, error: 'Authentication required' };
    }

    // RLS policy will ensure user can only create their own profile
    const { data: profile, error } = await supabase
      .from('customer_profiles')
      .insert({
        id: user.id, // Use authenticated user's ID
        ...profileData,
      })
      .select()
      .single();

    if (error) {
      console.error('[CUSTOMER_PROFILE_SERVICE] Error creating profile:', error);
      
      // Handle specific error cases
      if (error.code === '23505') {
        return { success: false, error: 'Customer profile already exists' };
      }
      
      return { success: false, error: 'Failed to create customer profile' };
    }

    return { success: true, data: profile };
  } catch (error) {
    console.error('[CUSTOMER_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Update the current user's customer profile
 * RLS policy ensures users can only update their own profile
 */
export async function updateCustomerProfile(
  updates: UpdateCustomerProfileData
): Promise<ServiceResult<CustomerProfile>> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return { success: false, error: 'Authentication required' };
    }

    // RLS policy will ensure user can only update their own profile
    const { data: profile, error } = await supabase
      .from('customer_profiles')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', user.id)
      .select()
      .single();

    if (error) {
      console.error('[CUSTOMER_PROFILE_SERVICE] Error updating profile:', error);
      return { success: false, error: 'Failed to update customer profile' };
    }

    return { success: true, data: profile };
  } catch (error) {
    console.error('[CUSTOMER_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get a customer profile by ID (public read access)
 * Uses public RLS policy for read access
 */
export async function getCustomerProfileById(
  customerId: string
): Promise<ServiceResult<CustomerProfile | null>> {
  try {
    const supabase = await createClient(); const { data: profile, error } = await supabase
      .from('customer_profiles')
      .select('*')
      .eq('id', customerId)
      .maybeSingle();

    if (error) {
      console.error('[CUSTOMER_PROFILE_SERVICE] Error fetching profile by ID:', error);
      return { success: false, error: 'Failed to fetch customer profile' };
    }

    return { success: true, data: profile };
  } catch (error) {
    console.error('[CUSTOMER_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get multiple customer profiles by IDs (public read access)
 * Uses public RLS policy for read access
 */
export async function getCustomerProfilesByIds(
  customerIds: string[]
): Promise<ServiceResult<CustomerProfile[]>> {
  try {
    if (!Array.isArray(customerIds) || customerIds.length === 0) {
      return { success: true, data: [] };
    }

    const supabase = await createClient();
    const { data: profiles, error } = await supabase
      .from('customer_profiles')
      .select('*')
      .in('id', customerIds);

    if (error) {
      console.error('[CUSTOMER_PROFILE_SERVICE] Error fetching profiles by IDs:', error);
      return { success: false, error: 'Failed to fetch customer profiles' };
    }

    return { success: true, data: profiles || [] };
  } catch (error) {
    console.error('[CUSTOMER_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Delete the current user's customer profile
 * RLS policy ensures users can only delete their own profile
 */
export async function deleteCustomerProfile(): Promise<ServiceResult<void>> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return { success: false, error: 'Authentication required' };
    }

    // RLS policy will ensure user can only delete their own profile
    const { error } = await supabase
      .from('customer_profiles')
      .delete()
      .eq('id', user.id);

    if (error) {
      console.error('[CUSTOMER_PROFILE_SERVICE] Error deleting profile:', error);
      return { success: false, error: 'Failed to delete customer profile' };
    }

    return { success: true };
  } catch (error) {
    console.error('[CUSTOMER_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get comprehensive user profile data (both customer and business if they exist)
 */
export async function getComprehensiveUserProfile(): Promise<ServiceResult<{
  customerProfile: CustomerProfile | null;
  businessProfile: any | null; // Import from businessProfileService if needed
  hasCustomerProfile: boolean;
  hasBusinessProfile: boolean;
}>> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return { success: false, error: 'Authentication required' };
    }

    // Fetch both profiles in parallel
    const [customerResult, businessResult] = await Promise.all([
      supabase
        .from('customer_profiles')
        .select('*')
        .eq('id', user.id)
        .maybeSingle(),
      supabase
        .from('business_profiles')
        .select('*')
        .eq('id', user.id)
        .maybeSingle()
    ]);

    if (customerResult.error) {
      console.error('[CUSTOMER_PROFILE_SERVICE] Error fetching customer profile:', customerResult.error);
      return { success: false, error: 'Failed to fetch customer profile' };
    }

    if (businessResult.error) {
      console.error('[CUSTOMER_PROFILE_SERVICE] Error fetching business profile:', businessResult.error);
      return { success: false, error: 'Failed to fetch business profile' };
    }

    return {
      success: true,
      data: {
        customerProfile: customerResult.data,
        businessProfile: businessResult.data,
        hasCustomerProfile: !!customerResult.data,
        hasBusinessProfile: !!businessResult.data,
      }
    };
  } catch (error) {
    console.error('[CUSTOMER_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

