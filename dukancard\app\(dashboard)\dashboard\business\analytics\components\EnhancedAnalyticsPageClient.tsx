"use client";

/**
 * Business Analytics Page Client Component
 *
 * This component displays real-time analytics for a business profile, including:
 * - Visitor metrics (total, today, yesterday, 7-day, 30-day, monthly)
 * - Engagement metrics (likes, subscriptions, ratings)
 * - Visit trends (daily, hourly, and monthly)
 *
 * Database Implementation:
 *
 * 1. Database Tables:
 *    - card_visits: Records individual card visits with visitor_identifier and business_profile_id
 *    - business_profiles: Stores pre-aggregated metrics (total_visits, today_visits, etc.)
 *    - monthly_visit_metrics: Stores monthly unique visits data with year and month
 *
 * 2. Timezone Handling:
 *    - All visit metrics are calculated based on IST (Indian Standard Time, UTC+5:30)
 *    - The visited_at column in card_visits stores timestamps in UTC
 *    - All database functions convert these timestamps to IST using 'visited_at AT TIME ZONE 'Asia/Kolkata''
 *    - The update_visit_counts() function uses the most recent visit date in IST as "today"
 *    - This ensures accurate metrics regardless of when the functions run
 *
 * 3. Database Functions:
 *    - update_visit_counts(): Updates all visit metrics when a new visit is recorded
 *      This function calculates total_visits, today_visits, yesterday_visits, visits_7_days,
 *      and visits_30_days based on the card_visits table, using the most recent visit date in IST as "today".
 *
 *    - reset_daily_visit_counts(): Resets today's counts and shifts today's to yesterday's at midnight IST
 *      This function is called by a scheduled cron job at midnight IST.
 *
 *    - update_period_visit_counts(): Updates the 7-day and 30-day visit counts
 *      This function is called by a scheduled cron job at midnight IST.
 *
 *    - reset_all_visit_counts(): Wrapper function that calls both reset_daily_visit_counts()
 *      and update_period_visit_counts()
 *
 *    - update_monthly_visit_counts(): Updates monthly visit metrics when a new visit is recorded
 *      This function calculates unique visits for the current month based on the card_visits table.
 *
 *    - update_all_monthly_visit_metrics(): Updates monthly visit metrics for all businesses
 *      This function is called by a scheduled cron job on the 1st day of each month.
 *
 *    - populate_historical_monthly_data(): Populates historical monthly data from existing card_visits records
 *      This function is used for initial data migration and can be run manually.
 *
 *    - get_total_unique_visits(): Gets the total number of unique visitors for a business
 *    - get_daily_unique_visit_trend(): Gets the daily trend of unique visitors for a date range based on IST dates
 *    - get_hourly_unique_visit_trend(): Gets the hourly trend of unique visitors for a specific date based on IST hours
 *    - get_monthly_unique_visits(): Gets monthly unique visits for a specific business, month, and year
 *    - get_monthly_unique_visit_trend(): Gets monthly trend data for a date range
 *    - get_available_years_for_monthly_metrics(): Gets available years from monthly_visit_metrics table
 *
 * 4. Database Triggers:
 *    - handle_new_visit: Trigger on card_visits table that calls update_visit_counts()
 *      when a new visit is recorded
 *    - handle_new_visit_monthly: Trigger on card_visits table that calls update_monthly_visit_counts()
 *      when a new visit is recorded
 *
 * 5. Scheduled Jobs:
 *    - reset-visit-counts: A cron job runs reset_all_visit_counts() at midnight IST (6:30 PM UTC)
 *      Schedule: '30 18 * * *' (6:30 PM UTC daily)
 *    - clean-card-visits: A cron job runs clean_old_card_visits() at 1 AM IST (7:30 PM UTC)
 *      Schedule: '30 19 * * *' (7:30 PM UTC daily)
 *      This job deletes card_visits records older than 31 days to maintain database performance
 *    - update-monthly-visit-metrics: A cron job runs update_all_monthly_visit_metrics() at midnight IST on the 1st day of each month
 *      Schedule: '30 18 1 * *' (6:30 PM UTC on the 1st day of each month)
 *
 * 6. Visit Tracking:
 *    - Visits are tracked using a VisitTracker component on the public card page
 *    - Each visitor gets a unique identifier stored in localStorage
 *    - Only cards with "online" status are tracked
 *    - Both authenticated and non-authenticated users are tracked
 *
 * 7. Row-Level Security:
 *    - All tables have RLS policies that allow users to perform CRUD operations only on their own data
 *    - The business_profile_id field is used to match against auth.uid() for access control
 *
 * Real-time Updates:
 * - The component uses realtime database for all metrics:
 *   - Total unique visits, today's visits, yesterday's visits, 7-day visits, and 30-day visits from business_profiles table
 *   - Total likes, total subscriptions, and average rating from business_profiles table
 *   - Monthly unique visits from monthly_visit_metrics table
 * - When changes occur in any of these tables, the UI updates automatically with animations
 * - Note: card_visits table does not have realtime enabled; instead, we rely on the business_profiles table
 *   which has pre-aggregated metrics that are updated by triggers when new visits are recorded
 */

import { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { BarChart3 } from "lucide-react";
import { realtimeService } from "@/backend/supabase/services/realtime/realtimeService";
import EnhancedEngagementMetricsSection from "./EnhancedEngagementMetricsSection";
import EnhancedVisitMetricsSection from "./EnhancedVisitMetricsSection";
import DailyVisitTrendChart from "./DailyVisitTrendChart";
import HourlyVisitTrendChart from "./HourlyVisitTrendChart";
import MonthlyVisitTrendChart from "./MonthlyVisitTrendChart";
// Removed unused import: AnalyticsBackground

interface AnalyticsData {
  totalUniqueVisits: number;
  todayUniqueVisits: number;
  yesterdayUniqueVisits: number;
  visits7Days: number;
  visits30Days: number;
  currentMonthUniqueVisits: number;
  previousMonthUniqueVisits: number;
  currentYear: number;
  currentMonth: number;
  dailyTrend7Days: { date: string; visits: number }[];
  dailyTrend30Days: { date: string; visits: number }[];
  hourlyTrendToday: { hour: number; visits: number }[];
  monthlyTrend: { year: number; month: number; visits: number }[];
  availableYears: number[];
}

interface BusinessProfile {
  total_likes: number;
  total_subscriptions: number;
  average_rating: number;
  total_visits: number;
  today_visits: number;
  yesterday_visits: number;
  visits_7_days: number;
  visits_30_days: number;
}

interface EnhancedAnalyticsPageClientProps {
  analyticsData: AnalyticsData;
  userId: string;
  initialProfile: BusinessProfile;
  userPlan: string | null;
}

export default function EnhancedAnalyticsPageClient({
  analyticsData,
  userId,
  initialProfile,
  userPlan,
}: EnhancedAnalyticsPageClientProps) {
  // State for real-time profile data
  const [profile, setProfile] = useState<BusinessProfile>(initialProfile);

  // Ref to store current profile values for comparison without triggering re-renders
  const profileRef = useRef<BusinessProfile>(initialProfile);

  // Sync profileRef with initialProfile when it changes
  useEffect(() => {
    profileRef.current = initialProfile;
    setProfile(initialProfile);
  }, [initialProfile]);

  // State for animated counters
  const [isVisitUpdated, setIsVisitUpdated] = useState(false);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      }
    },
  };

  /**
   * Set up Supabase real-time subscriptions
   *
   * This effect sets up real-time subscriptions to:
   * 1. The business_profiles table for all general metrics:
   *    - total_likes, total_subscriptions, average_rating (engagement metrics)
   *    - total_visits, today_visits, yesterday_visits, visits_7_days, visits_30_days (visit metrics)
   * 2. The monthly_visit_metrics table for monthly visit data
   *
   * Note: We don't subscribe to card_visits table as it doesn't have realtime enabled.
   * Instead, we rely on the business_profiles table which has pre-aggregated metrics
   * that are updated by triggers when new visits are recorded.
   *
   * When changes are detected:
   * 1. The UI is updated with the new values
   * 2. Animations are triggered to highlight the changes
   */
  useEffect(() => {
    console.log("Setting up real-time subscriptions for user:", userId);
    console.log("Initial profile data:", initialProfile);

    // Set up real-time subscription to business_profiles table
    const profileSubscription = realtimeService.subscribeToBusinessProfile(
      userId,
      (payload) => {
        console.log("Real-time update received:", payload);
        const newData = payload.new as BusinessProfile;
        if (newData) {
          console.log("Current profile:", profileRef.current);
          console.log("New profile data:", newData);

          // Always create a new profile object to ensure React detects the change
          const updatedProfile = {
            total_likes: newData.total_likes ?? profileRef.current.total_likes ?? 0,
            total_subscriptions: newData.total_subscriptions ?? profileRef.current.total_subscriptions ?? 0,
            average_rating: newData.average_rating ?? profileRef.current.average_rating ?? 0,
            total_visits: newData.total_visits ?? profileRef.current.total_visits ?? 0,
            today_visits: newData.today_visits ?? profileRef.current.today_visits ?? 0,
            yesterday_visits: newData.yesterday_visits ?? profileRef.current.yesterday_visits ?? 0,
            visits_7_days: newData.visits_7_days ?? profileRef.current.visits_7_days ?? 0,
            visits_30_days: newData.visits_30_days ?? profileRef.current.visits_30_days ?? 0,
          };

          // Check if visit metrics have changed
          const visitMetricsChanged =
            newData.total_visits !== profileRef.current.total_visits ||
            newData.today_visits !== profileRef.current.today_visits ||
            newData.yesterday_visits !== profileRef.current.yesterday_visits ||
            newData.visits_7_days !== profileRef.current.visits_7_days ||
            newData.visits_30_days !== profileRef.current.visits_30_days;

          // Trigger animation for visit updates
          if (visitMetricsChanged) {
            console.log("Visit metrics changed, triggering animation");
            setIsVisitUpdated(true);
            setTimeout(() => setIsVisitUpdated(false), 500);
          }

          // Always update both the state and the ref
          console.log("Updating profile state:", updatedProfile);
          setProfile(updatedProfile);
          profileRef.current = updatedProfile;
        }
      },
      'analytics'
    );

    // Set up real-time subscription to monthly_visit_metrics table
    const monthlyMetricsSubscription = realtimeService.subscribeToMonthlyMetrics(
      userId,
      (payload) => {
        console.log("Monthly metrics update received:", payload);

        // When monthly metrics change, trigger visit update animation
        setIsVisitUpdated(true);
        setTimeout(() => setIsVisitUpdated(false), 500);

        // Refresh analytics data (this would ideally be implemented with a server action)
        // For now, we'll rely on the business_profiles subscription for updates
      },
      'analytics'
    );

    // Cleanup function
    return () => {
      profileSubscription.unsubscribe();
      monthlyMetricsSubscription.unsubscribe();
    };
  }, [userId, initialProfile]);

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="space-y-6">
        <motion.div variants={itemVariants}>
          <div className="mb-6">
            {/* Main Analytics Card */}
            <div className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg relative overflow-hidden max-w-full">

          {/* Content with relative positioning */}
          <div className="relative z-10">
            {/* Analytics Header */}
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
              <div className="p-2 rounded-lg bg-primary/10 text-primary self-start">
                <BarChart3 className="w-4 sm:w-5 h-4 sm:h-5" />
              </div>
              <div className="flex-1">
                <h2 className="text-base sm:text-lg md:text-xl font-semibold text-neutral-800 dark:text-neutral-100">
                  Business Analytics
                </h2>
                <p className="text-xs sm:text-sm text-neutral-500 dark:text-neutral-400 mt-0.5">
                  Comprehensive insights into your business card performance
                </p>
              </div>

            </div>

            {/* Engagement Metrics Section */}
            <div className="mb-6 sm:mb-8">
              <EnhancedEngagementMetricsSection
                profile={profile}
                initialProfile={initialProfile}
              />
            </div>

            {/* Visitor Metrics Section */}
            <div className="mb-6 sm:mb-8">
              <EnhancedVisitMetricsSection
                totalUniqueVisits={profile.total_visits}
                todayUniqueVisits={profile.today_visits}
                yesterdayUniqueVisits={profile.yesterday_visits}
                currentMonthUniqueVisits={analyticsData.currentMonthUniqueVisits}
                isVisitUpdated={isVisitUpdated}
              />
            </div>

            {/* Charts Section */}
            <div className="grid gap-6 grid-cols-1 lg:grid-cols-2 mb-8">
              {/* Daily Visit Trend Chart */}
              <DailyVisitTrendChart
                trend7Days={analyticsData.dailyTrend7Days || []}
                trend30Days={analyticsData.dailyTrend30Days || []}
                userPlan={userPlan}
              />

              {/* Hourly Visit Trend Chart */}
              <HourlyVisitTrendChart
                data={analyticsData.hourlyTrendToday || []}
                userPlan={userPlan}
              />
            </div>

            {/* Monthly Trend Chart */}
            <div className="mb-6 sm:mb-8">
              <MonthlyVisitTrendChart
                monthlyTrend={analyticsData.monthlyTrend || []}
                availableYears={analyticsData.availableYears || []}
                userPlan={userPlan}
              />
            </div>
            </div>
          </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
}
