// import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { SupabaseClient } from "@supabase/supabase-js";

// Define the webhook error log interface
interface WebhookErrorLog {
  id: string;
  event_type: string;
  event_id?: string;
  subscription_id?: string;
  error_message: string;
  payload: Record<string, unknown>;
  retry_count: number;
  status: 'pending' | 'retrying' | 'resolved' | 'failed';
  created_at: string;
  updated_at: string;
}

/**
 * Log a webhook error to the database for tracking and potential retry
 * @param eventType The type of webhook event
 * @param eventId The ID of the webhook event (if available)
 * @param subscriptionId The subscription ID (if available)
 * @param errorMessage The error message
 * @param payload The webhook payload
 * @param supabase The Supabase client
 * @returns The result of logging the error
 */
export async function logWebhookError(
  eventType: string,
  eventId: string | undefined,
  subscriptionId: string | undefined,
  errorMessage: string,
  payload: Record<string, unknown>,
  supabase?: SupabaseClient
): Promise<{ success: boolean; error_id?: string }> {
  try {
    // Create Supabase client if not provided - use admin client for webhooks
    const client = supabase || createAdminClient();

    // Create error log entry
    const errorLog: Omit<WebhookErrorLog, 'id'> = {
      event_type: eventType,
      event_id: eventId,
      subscription_id: subscriptionId,
      error_message: errorMessage,
      payload,
      retry_count: 0,
      status: 'pending',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Insert error log into database
    const { data, error } = await client
      .from('webhook_error_logs')
      .insert(errorLog)
      .select('id')
      .single();

    if (error) {
      console.error('[RAZORPAY_WEBHOOK] Error logging webhook error:', error);
      return { success: false };
    }

    return { success: true, error_id: data?.id };
  } catch (error) {
    console.error('[RAZORPAY_WEBHOOK] Exception logging webhook error:', error);
    return { success: false };
  }
}

/**
 * Update a webhook error log
 * @param errorId The ID of the error log
 * @param status The new status
 * @param retryCount The new retry count
 * @param errorMessage The new error message
 * @param supabase The Supabase client
 * @returns The result of updating the error log
 */
export async function updateWebhookErrorLog(
  errorId: string,
  status: 'pending' | 'retrying' | 'resolved' | 'failed',
  retryCount?: number,
  errorMessage?: string,
  supabase?: SupabaseClient
): Promise<{ success: boolean }> {
  try {
    // Create Supabase client if not provided - use admin client for webhooks
    const client = supabase || createAdminClient();

    // Create update data
    const updateData: Partial<WebhookErrorLog> = {
      status,
      updated_at: new Date().toISOString(),
    };

    // Add retry count if provided
    if (retryCount !== undefined) {
      updateData.retry_count = retryCount;
    }

    // Add error message if provided
    if (errorMessage) {
      updateData.error_message = errorMessage;
    }

    // Update error log in database
    const { error } = await client
      .from('webhook_error_logs')
      .update(updateData)
      .eq('id', errorId);

    if (error) {
      console.error('[RAZORPAY_WEBHOOK] Error updating webhook error log:', error);
      return { success: false };
    }

    return { success: true };
  } catch (error) {
    console.error('[RAZORPAY_WEBHOOK] Exception updating webhook error log:', error);
    return { success: false };
  }
}

/**
 * Get pending webhook errors for retry
 * @param maxRetryCount The maximum number of retries
 * @param supabase The Supabase client
 * @returns The pending webhook errors
 */
export async function getPendingWebhookErrors(
  maxRetryCount: number = 3,
  supabase?: SupabaseClient
): Promise<WebhookErrorLog[]> {
  try {
    // Create Supabase client if not provided - use admin client for webhooks
    const client = supabase || createAdminClient();

    // Get pending webhook errors
    const { data, error } = await client
      .from('webhook_error_logs')
      .select('*')
      .in('status', ['pending', 'retrying'])
      .lt('retry_count', maxRetryCount)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('[RAZORPAY_WEBHOOK] Error getting pending webhook errors:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('[RAZORPAY_WEBHOOK] Exception getting pending webhook errors:', error);
    return [];
  }
}
