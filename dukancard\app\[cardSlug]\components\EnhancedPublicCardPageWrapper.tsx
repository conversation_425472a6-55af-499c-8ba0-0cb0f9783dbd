"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "sonner";
import { useSearchParams, useRouter } from "next/navigation";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import {
  ProductServiceData,
  ProductSortBy,
} from "@/app/(dashboard)/dashboard/business/products/actions";

import { GalleryImage } from "@/lib/actions/gallery";
import { AdData } from "@/types/ad";
import ReviewsTab from "../../components/ReviewsTab";
import EnhancedTabsToggle from "./EnhancedTabsToggle";

import {
  getInteractionStatus,
  likeBusiness,
  subscribeToBusiness,
  unlikeBusiness,
  unsubscribeFromBusiness,
} from "@/lib/actions/interactions";

// Import our components
import EnhancedBusinessCardSection from "./EnhancedBusinessCardSection";
import EnhancedAdSection from "./EnhancedAdSection";
import EnhancedBusinessDetails from "./EnhancedBusinessDetails";


import ProductsTab from "../components/ProductsTab";
import GalleryTab from "./GalleryTab";
import VisitTracker from "../components/VisitTracker";

// Import animations
import {
  containerVariants,
  tabVariants,
  tabContentVariants,
} from "./animations";

// Define the BusinessProfile type
type BusinessProfile = BusinessCardData & {
  total_reviews?: number;
  subscription_status?: string;
  has_active_subscription?: boolean;
  trial_end_date?: Date | string | null;
};

interface EnhancedPublicCardPageWrapperProps {
  businessProfile: BusinessProfile;
  initialProducts: ProductServiceData[];
  totalProductCount: number;
  defaultSortPreference: ProductSortBy;
  isAuthenticated: boolean;
  currentUserId: string | null;
  userPlan: "free" | "basic" | "growth" | "pro" | "enterprise" | "trial" | undefined;
  topAdData: AdData;

  galleryImages?: GalleryImage[];
  galleryTotalCount?: number;
}

export default function EnhancedPublicCardPageWrapper({
  businessProfile,
  initialProducts,
  totalProductCount,
  defaultSortPreference,
  isAuthenticated,
  currentUserId,
  userPlan,
  topAdData,

  galleryImages = [],
  galleryTotalCount = 0,
}: EnhancedPublicCardPageWrapperProps) {
  // Get the tab from URL params
  const searchParams = useSearchParams();
  const router = useRouter();
  const tabParam = searchParams.get("tab");

  // State variables
  const [activeTab, setActiveTab] = useState<string>(
    tabParam === "reviews" ? "reviews" : tabParam === "gallery" ? "gallery" : "products"
  );
  const [isSubscribed, setIsSubscribed] = useState<boolean>(false);
  const [hasLiked, setHasLiked] = useState<boolean>(false);
  const [isLoadingInteraction, setIsLoadingInteraction] =
    useState<boolean>(false);
  const [isClient, setIsClient] = useState(false);





  // Check if the current user is a business user
  const isCurrentUserBusiness = currentUserId
    ? currentUserId.startsWith("business_")
    : false;

  // Effect to scroll to reviews section when tab changes to reviews
  useEffect(() => {
    if (activeTab === "reviews") {
      // Use setTimeout to ensure the DOM has fully updated and the tab content is rendered
      setTimeout(() => {
        const reviewsSection = document.getElementById("reviews-section");
        if (reviewsSection) {
          const offsetTop = reviewsSection.offsetTop;
          window.scrollTo({
            top: offsetTop - 100,
            behavior: "smooth"
          });
        }
      }, 300);
    }
  }, [activeTab]);

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // Update URL without full page reload
    const params = new URLSearchParams(searchParams);
    if (value === "reviews") {
      params.set("tab", "reviews");

      // Add direct scroll for reviews tab
      setTimeout(() => {
        const reviewsSection = document.getElementById("reviews-section");
        if (reviewsSection) {
          const offsetTop = reviewsSection.offsetTop;
          window.scrollTo({
            top: offsetTop - 100,
            behavior: "smooth"
          });
        }
      }, 300);
    } else if (value === "gallery") {
      params.set("tab", "gallery");

      // Add direct scroll for gallery tab
      setTimeout(() => {
        const gallerySection = document.getElementById("gallery-section");
        if (gallerySection) {
          const offsetTop = gallerySection.offsetTop;
          window.scrollTo({
            top: offsetTop - 100,
            behavior: "smooth"
          });
        }
      }, 300);
    } else {
      params.delete("tab");
    }
    router.replace(`/${businessProfile.business_slug}?${params.toString()}`, {
      scroll: false,
    });
  };

  // Handle review button click
  const handleReviewClick = () => {
    // If not authenticated, redirect to login with redirect back to this card
    if (!isAuthenticated) {
      const cardSlug = businessProfile.business_slug;
      window.location.href = `/login?message=Please log in to leave a review&redirect=${encodeURIComponent(cardSlug || '')}`;
      return;
    }

    // Set active tab to reviews
    setActiveTab("reviews");

    // Update URL without full page reload
    const params = new URLSearchParams(searchParams);
    params.set("tab", "reviews");
    router.replace(`/${businessProfile.business_slug}?${params.toString()}`, {
      scroll: false,
    });

    // Force scroll to the reviews tab section with a sufficient delay
    // to ensure the tab content is fully rendered
    setTimeout(() => {
      const reviewsSection = document.getElementById("reviews-section");
      if (reviewsSection) {
        const offsetTop = reviewsSection.offsetTop;
        window.scrollTo({
          top: offsetTop - 100,
          behavior: "smooth"
        });
      }
    }, 300);
  };

  // Fetch interaction status on component mount
  useEffect(() => {
    setIsClient(true);

    const fetchData = async () => {
      try {
        if (isAuthenticated && businessProfile.id) {
          const result = await getInteractionStatus(businessProfile.id || "");
          setIsSubscribed(result.isSubscribed || false);
          setHasLiked(result.hasLiked || false);
        }
      } catch (error) {
        console.error("Error fetching interaction status:", error);
      }
    };

    fetchData();
  }, [isAuthenticated, businessProfile.id]);

  // Generic interaction handler
  const handleInteraction = async (
    action: (_businessId: string) => Promise<{ success: boolean; error?: string }>,
    onSuccess: () => void,
    successMessage: string,
    errorPrefix: string
  ) => {
    if (!isAuthenticated) {
      toast.error("Please log in to interact with this business card");
      return;
    }

    setIsLoadingInteraction(true);
    const result = await action(businessProfile.id || "");
    setIsLoadingInteraction(false);

    if (result.success) {
      toast.success(successMessage);
      onSuccess();
    } else {
      toast.error(`${errorPrefix}: ${result.error || "Unknown error"}`);
    }
  };

  // Handle subscribe
  const handleSubscribe = () =>
    handleInteraction(
      subscribeToBusiness,
      () => setIsSubscribed(true),
      "Subscribed successfully!",
      "Failed to subscribe"
    );

  // Handle unsubscribe
  const handleUnsubscribe = () =>
    handleInteraction(
      unsubscribeFromBusiness,
      () => setIsSubscribed(false),
      "Unsubscribed successfully.",
      "Failed to unsubscribe"
    );

  // Handle like
  const handleLike = () =>
    handleInteraction(
      likeBusiness,
      () => setHasLiked(true),
      "Liked!",
      "Failed to like"
    );

  // Handle unlike
  const handleUnlike = () =>
    handleInteraction(
      unlikeBusiness,
      () => setHasLiked(false),
      "Unliked.",
      "Failed to unlike"
    );

  if (!isClient) {
    return null; // Render nothing on the server
  }

  // We don't need to show a loading indicator here anymore
  // The parent component (PublicCardPageClient) is already showing one

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="container mx-auto px-2 sm:px-4 max-w-full min-h-screen"
    >
      {/* Visit Tracker Component - Invisible component that tracks visits */}
      <VisitTracker
        businessProfileId={businessProfile.id || ""}
        status={businessProfile.status || "offline"}
      />

      {/* Main content area - No background container */}
      <div className="flex flex-col lg:flex-row gap-8 lg:gap-16 pb-8 relative min-h-[80vh]">
        {/* Left Column: Card - 2/5 width on desktop, sticky */}
        <div className="w-full lg:w-2/5 lg:sticky lg:top-24 self-start h-fit space-y-4 md:space-y-6 lg:space-y-8 z-10">
          <EnhancedBusinessCardSection
            businessProfile={businessProfile}
            isAuthenticated={isAuthenticated}
            userPlan={userPlan}
            totalLikes={businessProfile.total_likes ?? 0}
            totalSubscriptions={businessProfile.total_subscriptions ?? 0}
            averageRating={businessProfile.average_rating ?? 0}
            isSubscribed={isSubscribed}
            hasLiked={hasLiked}
            isLoadingInteraction={isLoadingInteraction}
            onSubscribe={handleSubscribe}
            onUnsubscribe={handleUnsubscribe}
            onLike={handleLike}
            onUnlike={handleUnlike}
            onReviewClick={handleReviewClick}
            isOwnBusiness={currentUserId === businessProfile.id}
            isCurrentUserBusiness={isCurrentUserBusiness}
          />
        </div>

        {/* Right Column: Business Details - 3/5 width on desktop, sticky */}
        <div className="w-full lg:w-3/5 flex flex-col gap-4 lg:sticky lg:top-24 self-start h-fit z-10">
          {/* Enhanced Business Details Section */}
          <EnhancedBusinessDetails
            businessProfile={businessProfile}
            isAuthenticated={isAuthenticated}
            totalLikes={businessProfile.total_likes ?? 0}
            totalSubscriptions={businessProfile.total_subscriptions ?? 0}
            averageRating={businessProfile.average_rating ?? 0}
            isSubscribed={isSubscribed}
            hasLiked={hasLiked}
            isLoadingInteraction={isLoadingInteraction}
            onSubscribe={handleSubscribe}
            onUnsubscribe={handleUnsubscribe}
            onLike={handleLike}
            onUnlike={handleUnlike}
            onReviewClick={handleReviewClick}
            isOwnBusiness={currentUserId === businessProfile.id}
          />
        </div>
      </div>

      {/* Ad Section - Below sticky content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="w-full mb-8"
      >
        <EnhancedAdSection
          topAdData={topAdData}
          businessCustomAd={businessProfile.custom_ads}
          userPlan={userPlan}
        />
      </motion.div>

      {/* Enhanced Tabs Section */}
      <motion.div
        variants={tabVariants}
        className="mt-8 mb-16"
      >
        {/* Enhanced Tabs Toggle */}
        <EnhancedTabsToggle
          activeTab={activeTab}
          onTabChange={handleTabChange}
          galleryCount={galleryTotalCount}
        />

        {/* AnimatePresence for smooth tab transitions */}
        <AnimatePresence mode="wait">
          {/* Products & Services Tab */}
          {activeTab === "products" && (
            <motion.div
              key="products"
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={tabContentVariants}
            >
              <ProductsTab
                businessProfile={businessProfile}
                initialProducts={initialProducts}
                totalProductCount={totalProductCount}
                defaultSortPreference={defaultSortPreference}
              />
            </motion.div>
          )}

          {/* Gallery Tab */}
          {activeTab === "gallery" && galleryImages.length > 0 && (
            <motion.div
              key="gallery"
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={tabContentVariants}
              id="gallery-section"
              className="scroll-mt-24 relative pt-4"
            >
              {/* Visual marker for scroll target */}
              <div className="absolute -top-2 left-0 right-0 h-1 bg-amber-500/20 rounded-full"></div>
              <GalleryTab
                images={galleryImages}
                totalCount={galleryTotalCount}
                businessName={businessProfile.business_name || ""}
                businessSlug={businessProfile.business_slug || ""}
              />
            </motion.div>
          )}

          {/* Reviews Tab */}
          {activeTab === "reviews" && (
            <motion.div
              key="reviews"
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={tabContentVariants}
              id="reviews-section"
              className="scroll-mt-24 relative pt-4" // Increased scroll margin and added padding
            >
              {/* Visual marker for scroll target */}
              <div className="absolute -top-2 left-0 right-0 h-1 bg-amber-500/20 rounded-full"></div>
              <ReviewsTab
                businessProfileId={businessProfile.id || ""}
                isAuthenticated={isAuthenticated}
                currentUserId={currentUserId}
                averageRating={businessProfile.average_rating ?? 0}
                totalReviews={businessProfile.total_reviews ?? 0}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </motion.div>
  );
}
