import { MetadataRoute } from "next";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in"; 

  // 1. Static Pages
  const staticPages = [
    "/",
    "/about",
    "/pricing",
    "/features",
    "/contact",
    "/privacy",
    "/login",
    "/discover",
    "/support",
    "/terms",
    "/cookies",
    "/refund",
    "/advertise",
    "/blog",
  ];

  const staticEntries: MetadataRoute.Sitemap = staticPages.map((path) => ({
    url: `${siteUrl}${path}`,
    lastModified: new Date(), // Use current date as last modified for static pages
    changeFrequency: "monthly" as const, // Static pages change less often
    priority: path === "/" ? 1.0 : 0.8, // Give homepage higher priority
  }));

  // 2. Add references to other sitemaps
  const otherSitemaps: MetadataRoute.Sitemap = [
    // Temporarily commented out to reduce crawler load on Supabase
    // {
    //   url: `${siteUrl}/cards/sitemap.xml`,
    //   lastModified: new Date(),
    //   changeFrequency: "daily" as const,
    //   priority: 0.9,
    // },
    // {
    //   url: `${siteUrl}/products/sitemap.xml`,
    //   lastModified: new Date(),
    //   changeFrequency: "daily" as const,
    //   priority: 0.9,
    // },
    // Base categories sitemap (without locations)
    // {
    //   url: `${siteUrl}/categories/sitemap.xml`,
    //   lastModified: new Date(),
    //   changeFrequency: "daily" as const,
    //   priority: 0.9,
    // },
    // Blog sitemap
    {
      url: `${siteUrl}/blog/sitemap.xml`,
      lastModified: new Date(),
      changeFrequency: "daily" as const,
      priority: 0.8,
    }
  ];

  // Combine all entries
  const allEntries = [...staticEntries, ...otherSitemaps];
  return allEntries;
}
