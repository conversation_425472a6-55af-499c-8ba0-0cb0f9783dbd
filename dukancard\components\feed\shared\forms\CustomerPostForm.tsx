'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, Plus, Loader2 } from 'lucide-react';
import Image from 'next/image';
import { createCustomerPost, updateCustomerPost } from '@/lib/actions/customerPosts';
import { customerPostSchema, CustomerPostFormData, CustomerPostData } from '@/lib/types/posts';
import LocationDisplay from './LocationDisplay';
import MediaUpload from './MediaUpload';
import { useCustomerPostMediaUpload } from '../hooks/useCustomerPostMediaUpload';
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface CustomerPostFormProps {
  post?: CustomerPostData;
  onSuccess?: () => void;
  onCancel?: () => void;
  showCard?: boolean;
}

export default function CustomerPostForm({ post, onSuccess, onCancel, showCard = true }: CustomerPostFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadMethod, setUploadMethod] = useState<'upload' | 'url'>('upload');
  const router = useRouter();

  // Initialize form with existing post data or defaults
  const form = useForm<CustomerPostFormData>({
    resolver: zodResolver(customerPostSchema),
    defaultValues: {
      id: post?.id,
      content: post?.content || '',
      image_url: post?.image_url || null,
      mentioned_business_ids: post?.mentioned_business_ids || [],
    },
  });

  // Media upload hook
  const {
    uploadError,
    previewUrl,
    isUploading,
    imageToCrop: _imageToCrop,
    originalFile: _originalFile,
    handleFileSelect,
    handleUpload,
    clearImage,
    handleCropComplete: _handleCropComplete,
    handleCropCancel: _handleCropCancel,
  } = useCustomerPostMediaUpload({
    onUploadSuccess: (url) => {
      form.setValue('image_url', url, { shouldDirty: true });
    },
    onUploadError: (error) => {
      toast.error('Upload failed', { description: error });
    },
  });

  // Helper function to clear all images
  const handleClearAllImages = () => {
    clearImage();
    form.setValue('image_url', null, { shouldDirty: true });
    setUploadMethod('upload');
  };

  // Check if customer has complete profile (name and address)
  const checkCustomerProfile = async () => {
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      toast.error('Please log in to continue');
      return false;
    }

    const { data: profile, error } = await supabase
      .from('customer_profiles')
      .select('name, pincode, city, state, locality')
      .eq('id', user.id)
      .single();

    if (error) {
      toast.error('Failed to check customer profile');
      return false;
    }

    // Check if name is filled
    if (!profile?.name || profile.name.trim() === '') {
      toast.error('Please complete your name in your profile before creating posts', {
        description: 'You will be redirected to update your profile',
        action: {
          label: 'Update Now',
          onClick: () => router.push('/dashboard/customer/profile')
        }
      });

      // Redirect after a short delay
      setTimeout(() => {
        router.push('/dashboard/customer/profile');
      }, 2000);

      return false;
    }

    // Check if all address fields are filled
    if (!profile?.pincode || !profile?.city || !profile?.state || !profile?.locality) {
      toast.error('Please complete your address in your profile before creating posts', {
        description: 'You will be redirected to update your profile',
        action: {
          label: 'Update Now',
          onClick: () => router.push('/dashboard/customer/profile')
        }
      });

      // Redirect after a short delay
      setTimeout(() => {
        router.push('/dashboard/customer/profile');
      }, 2000);

      return false;
    }

    return true;
  };

  // Handle form submission
  const onSubmit = async (data: CustomerPostFormData) => {
    setIsSubmitting(true);

    try {
      // Check customer profile (name and address) before creating new posts
      if (!post?.id) {
        const hasValidProfile = await checkCustomerProfile();
        if (!hasValidProfile) {
          setIsSubmitting(false);
          return;
        }
      }
      const finalData = { ...data };

      let result;

      if (post?.id) {
        // Updating existing post
        result = await updateCustomerPost(post.id, finalData);
      } else {
        // Creating new post
        if (previewUrl && !finalData.image_url) {
          // Handle file upload for new posts
          const createResult = await createCustomerPost({
            ...finalData,
            image_url: null // Create post first without image
          });

          if (createResult.success && createResult.data) {
            const postData = createResult.data as CustomerPostData;
            const uploadUrl = await handleUpload(postData.id, undefined, postData.created_at);
            if (uploadUrl) {
              // Update post with image URL
              const updateResult = await updateCustomerPost(postData.id, {
                ...finalData,
                image_url: uploadUrl
              });
              result = updateResult;
            } else {
              result = createResult; // Post created but image upload failed
            }
          } else {
            result = createResult;
          }
        } else {
          // No file upload needed or URL provided
          result = await createCustomerPost(finalData);
        }
      }

      if (result.success) {
        toast.success(result.message);
        form.reset();
        clearImage(); // Clear media upload state
        setUploadMethod('upload'); // Reset to upload method
        if (onSuccess) {
          onSuccess();
        }
      } else {
        toast.error(result.error || result.message);
      }
    } catch (error) {
      console.error('Error submitting post:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formContent = (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="space-y-4">
          {/* Location Display */}
          <LocationDisplay className="mb-4" />

          {/* Post content */}
          <FormField
            control={form.control}
            name="content"
            render={({ field }) => {
              const charCount = field.value?.length || 0;
              const maxChars = 2000;
              const isOverLimit = charCount > maxChars;

              return (
                <FormItem>
                  <FormLabel>Content</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Textarea
                        placeholder="What's on your mind?"
                        className={`min-h-[120px] pr-16 ${isOverLimit ? 'border-destructive focus:border-destructive' : ''}`}
                        {...field}
                      />
                      <div className={`absolute bottom-2 right-2 text-xs ${
                        isOverLimit ? 'text-destructive' : 'text-muted-foreground'
                      }`}>
                        {charCount}/{maxChars}
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />

          {/* Image Upload/URL Section */}
          <FormField
            control={form.control}
            name="image_url"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Add Image (optional)</FormLabel>

                {/* Upload method tabs */}
                <Tabs value={uploadMethod} onValueChange={(value) => setUploadMethod(value as 'upload' | 'url')}>
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="upload">Upload Image</TabsTrigger>
                    <TabsTrigger value="url">Image URL</TabsTrigger>
                  </TabsList>

                  <TabsContent value="upload" className="space-y-4">
                    <MediaUpload
                      previewUrl={previewUrl || (uploadMethod === 'upload' ? (field.value || null) : null)}
                      isUploading={isUploading}
                      uploadError={uploadError}
                      onFileSelect={handleFileSelect}
                      onClearImage={handleClearAllImages}
                      disabled={isSubmitting}
                    />
                  </TabsContent>

                  <TabsContent value="url" className="space-y-4">
                    <FormControl>
                      <div className="space-y-2">
                        <input
                          type="url"
                          placeholder="Enter image URL (https://...)"
                          value={uploadMethod === 'url' ? (field.value || '') : ''}
                          onChange={(e) => {
                            if (uploadMethod === 'url') {
                              field.onChange(e.target.value || null);
                            }
                          }}
                          disabled={isSubmitting}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                        />
                        {uploadMethod === 'url' && field.value && (
                          <div className="relative">
                            <Image
                              src={field.value}
                              alt="Preview"
                              width={400}
                              height={300}
                              className="w-full max-w-sm h-auto rounded-lg border border-gray-200 dark:border-gray-700"
                              onError={() => {
                                toast.error('Invalid image URL');
                                field.onChange(null);
                              }}
                            />
                            <button
                              type="button"
                              onClick={() => field.onChange(null)}
                              className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                              disabled={isSubmitting}
                            >
                              ×
                            </button>
                          </div>
                        )}
                      </div>
                    </FormControl>
                  </TabsContent>
                </Tabs>

                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex flex-col-reverse sm:flex-row gap-3 pt-6">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
              className="flex-1 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200"
            >
              Cancel
            </Button>
          )}

          <motion.div
            whileTap={{ scale: 0.98 }}
            className="flex-1"
          >
            <Button
              type="submit"
              disabled={isSubmitting}
              className={`
                w-full relative overflow-hidden
                bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-dark)]
                hover:from-[var(--brand-gold-dark)] hover:to-[var(--brand-gold)]
                text-[var(--brand-gold-foreground)] font-medium
                shadow-lg hover:shadow-xl
                transition-all duration-300
                before:absolute before:inset-0
                before:bg-gradient-to-r before:from-[var(--brand-gold)]/80 before:to-[var(--brand-gold-dark)]/80
                before:opacity-0 hover:before:opacity-20
                before:transition-opacity before:duration-300
                ${isSubmitting ? 'cursor-not-allowed opacity-80' : ''}
              `}
            >
              <AnimatePresence mode="wait">
                {isSubmitting ? (
                  <motion.div
                    key="submitting"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className="flex items-center justify-center"
                  >
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {post?.id ? 'Updating...' : 'Posting...'}
                  </motion.div>
                ) : (
                  <motion.div
                    key="submit"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className="flex items-center justify-center"
                  >
                    {post?.id ? (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Update Post
                      </>
                    ) : (
                      <>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Post
                      </>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </motion.div>
        </div>
      </form>
    </Form>
  );

  if (!showCard) {
    return formContent;
  }

  return (
    <>
      <Card className="w-full">
        <CardHeader>
          <CardTitle>{post?.id ? 'Edit Post' : 'Create New Post'}</CardTitle>
        </CardHeader>
        <CardContent>
          {formContent}
        </CardContent>
      </Card>
    </>
  );
}
