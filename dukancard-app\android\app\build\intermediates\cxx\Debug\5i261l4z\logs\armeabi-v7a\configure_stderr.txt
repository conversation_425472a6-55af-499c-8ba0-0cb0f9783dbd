CMake Warning in C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/android/app/.cxx/Debug/5i261l4z/armeabi-v7a/RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/./

  has 185 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


CMake Warning in C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Desktop/Dukancard Web App/dukancard-app/android/app/.cxx/Debug/5i261l4z/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/./

  has 197 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


