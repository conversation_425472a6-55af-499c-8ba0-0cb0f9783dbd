import { BusinessCardData } from "../schema";
import { ProductServiceData } from "@/backend/supabase/services/products/types";

// Type for raw database data
type RawBusinessCardData = Record<string, unknown> & {
  id?: string;
  business_name?: string | null;
  contact_email?: string | null;
  logo_url?: string | null;
  member_name?: string | null;
  title?: string | null;
  address_line?: string | null;
  city?: string | null;
  state?: string | null;
  pincode?: string | null;
  locality?: string | null;
  phone?: string | null;
  instagram_url?: string | null;
  facebook_url?: string | null;
  whatsapp_number?: string | null;
  about_bio?: string | null;
  status?: string | null;
  business_slug?: string | null;
  theme_color?: string | null;
  delivery_info?: string | null;
  business_category?: string | null;
  google_maps_url?: string | null;
  established_year?: number | null;
  total_likes?: number | null;
  total_subscriptions?: number | null;
  average_rating?: number | null;
  business_hours?: unknown;
  trial_end_date?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
  has_active_subscription?: boolean | null;
  custom_branding?: Record<string, unknown>;
  custom_ads?: Record<string, unknown>;
  products_services?: unknown[];
};

/**
 * Maps raw database data to BusinessCardData type with proper defaults
 * @param data - Raw data from database
 * @returns Properly mapped BusinessCardData
 */
export function mapBusinessCardData(data: RawBusinessCardData): BusinessCardData {
  return {
    id: data.id,
    business_name: data.business_name ?? "",
    contact_email: data.contact_email ?? "",
    has_active_subscription: Boolean(data.has_active_subscription) ?? false,
    trial_end_date: (typeof data.trial_end_date === 'string' ? data.trial_end_date : null),
    created_at: (typeof data.created_at === 'string' ? data.created_at : undefined),
    updated_at: (typeof data.updated_at === 'string' ? data.updated_at : undefined),
    logo_url: (typeof data.logo_url === 'string' ? data.logo_url : null),
    member_name: (typeof data.member_name === 'string' ? data.member_name : ""),
    title: (typeof data.title === 'string' ? data.title : ""),
    address_line: (typeof data.address_line === 'string' ? data.address_line : ""),
    city: (typeof data.city === 'string' ? data.city : ""),
    state: (typeof data.state === 'string' ? data.state : ""),
    pincode: (typeof data.pincode === 'string' ? data.pincode : ""),
    locality: (typeof data.locality === 'string' ? data.locality : ""),
    phone: (typeof data.phone === 'string' ? data.phone : ""),
    instagram_url: (typeof data.instagram_url === 'string' ? data.instagram_url : ""),
    facebook_url: (typeof data.facebook_url === 'string' ? data.facebook_url : ""),
    whatsapp_number: (typeof data.whatsapp_number === 'string' ? data.whatsapp_number : ""),
    about_bio: (typeof data.about_bio === 'string' ? data.about_bio : ""),
    status: (typeof data.status === 'string' && (data.status === 'online' || data.status === 'offline') ? data.status : "offline"),
    business_slug: (typeof data.business_slug === 'string' ? data.business_slug : ""),
    // Include metrics data
    total_likes: (typeof data.total_likes === 'number' ? data.total_likes : 0),
    total_subscriptions: (typeof data.total_subscriptions === 'number' ? data.total_subscriptions : 0),
    average_rating: (typeof data.average_rating === 'number' ? data.average_rating : 0),
    // Include additional fields
    theme_color: (typeof data.theme_color === 'string' ? data.theme_color : ""),
    delivery_info: (typeof data.delivery_info === 'string' ? data.delivery_info : ""),
    business_hours: data.business_hours ?? null,
    business_category: (typeof data.business_category === 'string' ? data.business_category : ""),
    google_maps_url: (typeof data.google_maps_url === 'string' ? data.google_maps_url : ""),
    established_year: (typeof data.established_year === 'number' ? data.established_year : null),
    custom_branding: {
      custom_header_text: (typeof data.custom_branding?.custom_header_text === 'string' ? data.custom_branding.custom_header_text : ""),
      custom_header_image_url: (typeof data.custom_branding?.custom_header_image_url === 'string' ? data.custom_branding.custom_header_image_url : ""),
      custom_header_image_light_url: (typeof data.custom_branding?.custom_header_image_light_url === 'string' ? data.custom_branding.custom_header_image_light_url : ""),
      custom_header_image_dark_url: (typeof data.custom_branding?.custom_header_image_dark_url === 'string' ? data.custom_branding.custom_header_image_dark_url : ""),
      hide_dukancard_branding: (typeof data.custom_branding?.hide_dukancard_branding === 'boolean' ? data.custom_branding.hide_dukancard_branding : false),
      pending_light_header_file: null, // Always null when loading from database
      pending_dark_header_file: null, // Always null when loading from database
    },
    custom_ads: (data.custom_ads && typeof data.custom_ads === 'object') ? {
      enabled: (typeof data.custom_ads.enabled === 'boolean' ? data.custom_ads.enabled : false),
      image_url: (typeof data.custom_ads.image_url === 'string' ? data.custom_ads.image_url : ""),
      link_url: (typeof data.custom_ads.link_url === 'string' ? data.custom_ads.link_url : ""),
      uploaded_at: (typeof data.custom_ads.uploaded_at === 'string' ? data.custom_ads.uploaded_at : ""),
    } : {
      enabled: false,
      image_url: "",
      link_url: "",
      uploaded_at: "",
    },
  };
}

/**
 * Maps public card data with products/services
 * @param data - Raw data from database
 * @returns Mapped data for public card display
 */
export function mapPublicCardData(data: RawBusinessCardData): BusinessCardData & { products_services?: ProductServiceData[] } {
  // Sort products (display_order removed, sort by created_at desc as fallback)
  const sortedProducts =
    (data.products_services as Partial<ProductServiceData>[])
      ?.sort(
        (a, b) =>
          new Date(b.created_at ?? 0).getTime() -
          new Date(a.created_at ?? 0).getTime()
      )
      .slice(0, 10) ?? [];

  return {
    id: data.id,
    business_name: data.business_name ?? "",
    contact_email: data.contact_email ?? "",
    has_active_subscription: Boolean(data.has_active_subscription) ?? false,
    trial_end_date: (typeof data.trial_end_date === 'string' ? data.trial_end_date : null),
    created_at: (typeof data.created_at === 'string' ? data.created_at : undefined),
    updated_at: (typeof data.updated_at === 'string' ? data.updated_at : undefined),
    logo_url: (typeof data.logo_url === 'string' ? data.logo_url : null),
    member_name: (typeof data.member_name === 'string' ? data.member_name : ""),
    title: (typeof data.title === 'string' ? data.title : ""),
    address_line: (typeof data.address_line === 'string' ? data.address_line : ""),
    city: (typeof data.city === 'string' ? data.city : ""),
    state: (typeof data.state === 'string' ? data.state : ""),
    pincode: (typeof data.pincode === 'string' ? data.pincode : ""),
    locality: (typeof data.locality === 'string' ? data.locality : ""),
    phone: (typeof data.phone === 'string' ? data.phone : ""),
    business_category: (typeof data.business_category === 'string' ? data.business_category : ""),
    business_hours: data.business_hours ?? null,
    delivery_info: (typeof data.delivery_info === 'string' ? data.delivery_info : ""),
    google_maps_url: (typeof data.google_maps_url === 'string' ? data.google_maps_url : ""),
    established_year: (typeof data.established_year === 'number' ? data.established_year : null),
    instagram_url: (typeof data.instagram_url === 'string' ? data.instagram_url : ""),
    facebook_url: (typeof data.facebook_url === 'string' ? data.facebook_url : ""),
    whatsapp_number: (typeof data.whatsapp_number === 'string' ? data.whatsapp_number : ""),
    about_bio: (typeof data.about_bio === 'string' ? data.about_bio : ""),
    status: (typeof data.status === 'string' && (data.status === 'online' || data.status === 'offline') ? data.status : "offline"),
    business_slug: (typeof data.business_slug === 'string' ? data.business_slug : ""),
    products_services: sortedProducts as ProductServiceData[],
  };
}
