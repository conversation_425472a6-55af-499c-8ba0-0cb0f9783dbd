'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';
import Image from 'next/image';
import { MoreVertical, Edit, Trash2, User } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { createClient } from '@/utils/supabase/client';
import { deleteCustomerPost } from '@/lib/actions/customerPosts';
import { CustomerPostWithProfile } from '@/lib/types/posts';

interface ModernCustomerPostCardProps {
  post: CustomerPostWithProfile;
  index?: number;
  onPostUpdate?: (_postId: string, _newContent: string) => void;
  onPostDelete?: (_postId: string) => void;
}

export default function ModernCustomerPostCard({
  post,
  index = 0,
  onPostUpdate: _onPostUpdate,
  onPostDelete: _onPostDelete
}: ModernCustomerPostCardProps) {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isOwner, setIsOwner] = useState(false);
  const [ownershipLoading, setOwnershipLoading] = useState(true);

  const customer = post.customer_profiles || {
    id: post.customer_id,
    name: 'Anonymous Customer',
    avatar_url: null,
    city: null,
    state: null,
    locality: null
  };

  // Check if current user owns this post
  useEffect(() => {
    const checkOwnership = async () => {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      setIsOwner(user?.id === post.customer_id);
      setOwnershipLoading(false);
    };

    checkOwnership();
  }, [post.customer_id]);

  // Avatar fallback now uses User icon instead of initials

  const formattedDate = formatDistanceToNow(new Date(post.created_at), { addSuffix: true });

  const handleDeletePost = async () => {
    try {
      const result = await deleteCustomerPost(post.id);
      if (result.success) {
        toast.success('Post deleted successfully');
        if (_onPostDelete) {
          _onPostDelete(post.id);
        }
      } else {
        toast.error(result.error || 'Failed to delete post');
      }
    } catch (error) {
      console.error('Error deleting post:', error);
      toast.error('An unexpected error occurred');
    }
    setShowDeleteDialog(false);
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: index * 0.1 }}
        className="w-full"
      >
        <Card className="w-full bg-white dark:bg-black border-neutral-200 dark:border-neutral-800 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
          <CardContent className="p-0">
            {/* Post Header */}
            <div className="p-4 pb-2">
              {/* Customer Info and Time */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  <Avatar className="h-12 w-12 border-2 border-neutral-100 dark:border-neutral-800">
                    <AvatarImage
                      src={customer.avatar_url || ''}
                      alt={customer.name || 'Customer'}
                      className="object-cover"
                    />
                    <AvatarFallback className="bg-muted text-foreground">
                      <User className="h-6 w-6" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-base text-neutral-900 dark:text-neutral-100 truncate">
                      {customer.name}
                    </h3>
                    <div className="flex items-center gap-2 text-xs text-neutral-400 dark:text-neutral-500 mt-1">
                      <span>{formattedDate}</span>
                      {customer.city && (
                        <>
                          <span>•</span>
                          <span>{customer.city}</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
                {/* Three-dot menu - only show for post owners */}
                {isOwner && !ownershipLoading && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-full"
                      >
                        <MoreVertical className="h-5 w-5 text-neutral-500 dark:text-neutral-400" />
                        <span className="sr-only">Open post menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      <DropdownMenuItem className="flex items-center gap-2">
                        <Edit className="h-4 w-4" />
                        Edit Post
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="flex items-center gap-2 text-destructive focus:text-destructive"
                        onClick={() => setShowDeleteDialog(true)}
                      >
                        <Trash2 className="h-4 w-4" />
                        Delete Post
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>


            </div>

            {/* Post Content */}
            <div className="px-4 pb-2">
              <p className="text-neutral-900 dark:text-neutral-100 leading-relaxed whitespace-pre-wrap">
                {post.content}
              </p>
            </div>



            {/* Post Image */}
            {post.image_url && !imageError && (
              <div className="px-4 pb-4">
                <div className="relative rounded-lg overflow-hidden bg-neutral-100 dark:bg-neutral-800">
                  {imageLoading && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="h-8 w-8 border-2 border-neutral-300 border-t-transparent rounded-full animate-spin" />
                    </div>
                  )}
                  <Image
                    src={post.image_url}
                    alt="Post image"
                    width={600}
                    height={400}
                    className={`w-full h-auto max-h-96 object-cover transition-opacity duration-300 ${
                      imageLoading ? 'opacity-0' : 'opacity-100'
                    }`}
                    onLoad={() => setImageLoading(false)}
                    onError={() => {
                      setImageError(true);
                      setImageLoading(false);
                    }}
                  />
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Post</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this post? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeletePost}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
