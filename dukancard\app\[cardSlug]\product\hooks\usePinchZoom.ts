"use client";

import { useState, useEffect, RefObject } from "react";

interface PinchZoomOptions {
  minScale?: number;
  maxScale?: number;
  scaleStep?: number;
}

export function usePinchZoom(
  elementRef: RefObject<HTMLElement | null>,
  setScale: (_scale: number | ((_prev: number) => number)) => void,
  currentScale: number,
  options: PinchZoomOptions = {}
) {
  const {
    minScale = 0.5,
    maxScale = 3
    // scaleStep is unused but kept in the interface for future use
  } = options;

  // Track touch points for pinch gesture
  // We don't directly use touchCache but need the setter
  const [, setTouchCache] = useState<Touch[]>([]);
  const [initialDistance, setInitialDistance] = useState<number | null>(null);
  const [initialScale, setInitialScale] = useState<number>(1);
  const [isPinching, setIsPinching] = useState(false);
  const [lastTouchTime, setLastTouchTime] = useState(0);

  // Calculate distance between two touch points
  const getDistance = (touches: Touch[]): number => {
    if (touches.length < 2) return 0;

    const dx = touches[0].clientX - touches[1].clientX;
    const dy = touches[0].clientY - touches[1].clientY;
    return Math.sqrt(dx * dx + dy * dy);
  };

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Touch start handler
    const handleTouchStart = (e: TouchEvent) => {
      const now = Date.now();
      setLastTouchTime(now);

      const touches = Array.from(e.touches);
      setTouchCache(touches);

      if (touches.length === 2) {
        // Start of pinch - store initial distance and scale
        setInitialDistance(getDistance(touches));
        setInitialScale(currentScale);
        setIsPinching(true);
        e.preventDefault();
      }
    };

    // Touch move handler for pinch zoom
    const handleTouchMove = (e: TouchEvent) => {
      const touches = Array.from(e.touches);

      // Throttle touch move events for better performance
      const now = Date.now();
      if (now - lastTouchTime < 16) { // ~60fps
        return;
      }
      setLastTouchTime(now);

      if (touches.length === 2 && initialDistance && initialDistance > 0) {
        // Calculate new scale based on change in distance
        const currentDistance = getDistance(touches);
        const scaleFactor = currentDistance / initialDistance;
        const newScale = initialScale * scaleFactor;

        // Apply scale within bounds with smoother transition
        const smoothScale = currentScale + (Math.min(Math.max(newScale, minScale), maxScale) - currentScale) * 0.3;

        // Update scale - progressive centering will be handled by the effect in the parent component
        setScale(smoothScale);

        // Prevent default to avoid browser gestures interfering
        e.preventDefault();
        e.stopPropagation();
      }
    };

    // Touch end handler
    const handleTouchEnd = (e: TouchEvent) => {
      const touches = Array.from(e.touches);
      setTouchCache(touches);

      if (touches.length < 2) {
        // End of pinch
        setInitialDistance(null);
        setIsPinching(false);
      }

      // Double tap detection (for mobile)
      const now = Date.now();
      const timeDiff = now - lastTouchTime;

      if (timeDiff < 300 && touches.length === 0 && !isPinching) {
        // Double tap detected - toggle zoom
        if (currentScale > 1) {
          setScale(1);
        } else {
          setScale(2);
        }
      }

      setLastTouchTime(now);
    };

    // Add event listeners with proper options
    element.addEventListener("touchstart", handleTouchStart, { passive: false });
    element.addEventListener("touchmove", handleTouchMove, { passive: false });
    element.addEventListener("touchend", handleTouchEnd, { passive: false });
    element.addEventListener("touchcancel", handleTouchEnd, { passive: false });

    // Prevent default on parent elements to avoid interference
    const preventDefaultOnParent = (e: TouchEvent) => {
      if (currentScale > 1 || isPinching) {
        e.preventDefault();
        e.stopPropagation();
      }
    };

    // Add listeners to parent elements to prevent scrolling when zoomed
    const parentElement = element.parentElement;
    if (parentElement && currentScale > 1) {
      parentElement.addEventListener("touchmove", preventDefaultOnParent, { passive: false });
    }

    // Clean up
    return () => {
      element.removeEventListener("touchstart", handleTouchStart);
      element.removeEventListener("touchmove", handleTouchMove);
      element.removeEventListener("touchend", handleTouchEnd);
      element.removeEventListener("touchcancel", handleTouchEnd);

      if (parentElement && currentScale > 1) {
        parentElement.removeEventListener("touchmove", preventDefaultOnParent);
      }
    };
  }, [elementRef, initialDistance, initialScale, currentScale, setScale, minScale, maxScale, isPinching, lastTouchTime]);
}
