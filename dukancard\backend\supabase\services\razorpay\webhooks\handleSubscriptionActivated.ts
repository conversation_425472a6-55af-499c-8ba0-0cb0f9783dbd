import { RazorpayWebhookData, RazorpaySubscription } from "./types";
import { SupabaseClient } from "@supabase/supabase-js";
import { createAdminClient } from "@/utils/supabase/admin";
import {
  SUBSCRIPTION_STATUS,
  extractWebhookTimestamp
} from "../utils";
import { webhookProcessor, type WebhookProcessingContext } from "../webhookProcessor";

/**
 * Handle subscription.activated event
 *
 * This event is triggered when a subscription is activated.
 * It updates all subscription date fields, including:
 * - subscription_start_date: The start date of the current billing cycle
 * - subscription_expiry_time: The end date of the current billing cycle
 * - subscription_charge_time: The date when the next payment will be charged
 *
 * @param payload The webhook payload
 * @param supabase The Supabase client
 * @returns The result of handling the event
 */
export async function handleSubscriptionActivated(
  payload: RazorpayWebhookData,
  _supabase: SupabaseClient,
  razorpayEventId?: string
): Promise<{ success: boolean; message: string }> {
  let context: WebhookProcessingContext | null = null;

  try {
    // Extract subscription data from payload
    const subscriptionData = payload.payload.subscription;

    if (!subscriptionData || !subscriptionData.entity) {
      console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload");
      return { success: false, message: "Subscription data not found in payload" };
    }

    // Cast to proper type to access properties
    const subscription = subscriptionData.entity as unknown as RazorpaySubscription;
    const subscriptionId = subscription.id;
    console.log(`[RAZORPAY_WEBHOOK] Subscription activated: ${subscriptionId}`);

    // Extract webhook timestamp from Razorpay payload for sequence validation
    const webhookTimestamp = extractWebhookTimestamp(payload);

    // Use centralized webhook processor for race condition protection
    context = {
      subscriptionId,
      eventType: 'subscription.activated',
      eventId: razorpayEventId || `activated_${subscriptionId}_${Date.now()}`,
      payload: payload as unknown as Record<string, unknown>,
      webhookTimestamp
    };

    const processingResult = await webhookProcessor.processWebhookEvent(context);
    if (!processingResult.shouldProcess) {
      return { success: processingResult.success, message: processingResult.message };
    }

    // Get admin client to bypass RLS
    const adminClient = createAdminClient();

    // Import centralized subscription logic
    const { isTerminalStatus } = await import('../utils');

    // Check if this subscription is in a terminal state (cancelled, expired, completed)
    const { data: existingSubscription, error: checkError } = await adminClient
      .from("payment_subscriptions")
      .select("cancelled_at, plan_id, subscription_status")
      .eq("razorpay_subscription_id", subscriptionId)
      .maybeSingle();

    if (checkError) {
      console.error(`[RAZORPAY_WEBHOOK] Error checking subscription status for ${subscriptionId}:`, checkError);
      // Continue processing anyway
    } else if (existingSubscription) {
      // CENTRALIZED LOGIC: Check if subscription is in a terminal state
      // CRITICAL FIX: Don't check cancelled_at alone - only check actual subscription_status
      // Trial users can have cancelled_at but should still be able to activate subscriptions
      // Free plan users should not be able to activate paid subscriptions
      // HALTED subscriptions should be recoverable (not terminal) regardless of plan

      // SPECIAL CASE: Allow halted subscriptions to be reactivated regardless of plan
      if (existingSubscription.subscription_status === "halted") {
        console.log(`[RAZORPAY_WEBHOOK] Halted subscription ${subscriptionId} can be reactivated - this is allowed`);
        // Skip all terminal state checks for halted subscriptions - they can always be reactivated
      } else {
        // Check for terminal states only for non-halted subscriptions
        // Free plan users (who are not halted) cannot activate paid subscriptions
        const isTerminalState = (existingSubscription.plan_id === "free" && existingSubscription.subscription_status === "active") ||
                               isTerminalStatus(existingSubscription.subscription_status);

        if (isTerminalState) {
          console.log(`[RAZORPAY_WEBHOOK] Subscription is in terminal state (${existingSubscription.subscription_status}, plan: ${existingSubscription.plan_id}), ignoring subscription.activated event`);
          return { success: true, message: `Subscription is in terminal state (${existingSubscription.subscription_status}, plan: ${existingSubscription.plan_id}), ignoring subscription.activated event` };
        }
      }

      // ADDITIONAL CHECK: Allow trial users to activate even if they have cancelled_at
      // This is critical for trial-to-paid transitions
      if (existingSubscription.subscription_status === SUBSCRIPTION_STATUS.TRIAL) {
        console.log(`[RAZORPAY_WEBHOOK] Trial user ${subscriptionId} activating subscription - this is allowed even with cancelled_at timestamp`);
      }
    }

    // Check if this is a resume operation
    const { data: dbSubscription, error: dbSubError } = await adminClient
      .from("payment_subscriptions")
      .select("original_plan_id, original_plan_cycle, subscription_paused_at, plan_cycle") // Added plan_cycle here
      .eq("razorpay_subscription_id", subscriptionId)
      .maybeSingle();

    if (dbSubError) {
      console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription details for resume check ${subscriptionId}:`, dbSubError);
      // Continue with normal activation if DB check fails, but log it.
    }

    let isResumeFlow = false;
    let originalPlanIdToRestore: string | null = null;
    let originalPlanCycleToRestore: string | null = null;

    if (dbSubscription && dbSubscription.subscription_paused_at && dbSubscription.original_plan_id) {
      console.log(`[RAZORPAY_WEBHOOK] Subscription ${subscriptionId} is being resumed. Original plan: ${dbSubscription.original_plan_id}, Original cycle: ${dbSubscription.original_plan_cycle}`);
      isResumeFlow = true;
      originalPlanIdToRestore = dbSubscription.original_plan_id;
      originalPlanCycleToRestore = dbSubscription.original_plan_cycle;
    }

    // Check if this is a plan switch (this logic should likely run *before* resume check or be integrated)
    const isPlanSwitch = subscription.notes?.is_plan_switch === "true";
    const oldSubscriptionId = subscription.notes?.old_subscription_id;
    const oldSubscriptionAlreadyCancelled = subscription.notes?.cancelled_old_subscription === "true";

    console.log(`[RAZORPAY_WEBHOOK] Plan switch detection:`, {
      isPlanSwitch,
      oldSubscriptionId,
      oldSubscriptionAlreadyCancelled,
      isResumeFlow,
      subscriptionNotes: subscription.notes
    });

    if (isPlanSwitch && !isResumeFlow) { // Don't do plan switch cancellation if it's a resume of the *same* subscription
      console.log(`[RAZORPAY_WEBHOOK] ✅ Processing plan switch from subscription ${oldSubscriptionId} to ${subscriptionId}`);

      if (oldSubscriptionId && !oldSubscriptionAlreadyCancelled) {
        console.log(`[RAZORPAY_WEBHOOK] Cancelling old subscription ${oldSubscriptionId}`);
        const { pauseSubscription } = await import("../../../lib/razorpay/services/subscription");
        const cancelResult = await cancelSubscription(oldSubscriptionId, false);

        if (!cancelResult.success) {
          console.error(`[RAZORPAY_WEBHOOK] CRITICAL: Failed to cancel old subscription ${oldSubscriptionId}:`, cancelResult.error);
          return {
            success: false,
            message: `Critical error: Failed to cancel old subscription ${oldSubscriptionId}. This could result in double billing.`
          };
        } else {
          console.log(`[RAZORPAY_WEBHOOK] Successfully cancelled old subscription ${oldSubscriptionId}`);

          // For UPI/netbanking plan switches, we need to update the old subscription record
          // with the new subscription ID instead of creating a new record
          console.log(`[RAZORPAY_WEBHOOK] Updating old subscription record ${oldSubscriptionId} with new subscription ID ${subscriptionId}`);

          // Update the old subscription record with the new Razorpay subscription ID
          const { error: updateError } = await adminClient
            .from("payment_subscriptions")
            .update({
              razorpay_subscription_id: subscriptionId,
              updated_at: new Date().toISOString()
            })
            .eq("razorpay_subscription_id", oldSubscriptionId);

          if (updateError) {
            console.error(`[RAZORPAY_WEBHOOK] Error updating old subscription record ${oldSubscriptionId} with new ID ${subscriptionId}:`, updateError);
            return {
              success: false,
              message: `Failed to update old subscription record with new subscription ID`
            };
          }

          console.log(`[RAZORPAY_WEBHOOK] Successfully updated old subscription record ${oldSubscriptionId} with new subscription ID ${subscriptionId}`);
        }
      } else if (oldSubscriptionId && oldSubscriptionAlreadyCancelled) {
        console.log(`[RAZORPAY_WEBHOOK] Old subscription ${oldSubscriptionId} was already cancelled, skipping cancellation`);
      }
    }

    let paymentMethod = "card";
    try {
      const lastPaymentId = subscription.notes?.last_payment_id;
      if (lastPaymentId) {
        const { getPaymentDetails } = await import("../../../lib/razorpay/services/payment");
        const paymentResult = await getPaymentDetails(lastPaymentId);
        if (paymentResult.success && paymentResult.data) {
          paymentMethod = paymentResult.data.method;
        } else {
          console.error(`[RAZORPAY_WEBHOOK] Error fetching payment ${lastPaymentId}:`, paymentResult.error);
        }
      }
    } catch (paymentError) {
      console.error(`[RAZORPAY_WEBHOOK] Error fetching payment method:`, paymentError);
    }

    const planTypeFromNotes = subscription.notes?.plan_type;
    const planCycleFromNotes = subscription.notes?.plan_cycle;

    // Prepare data for updateSubscriptionStatus
    const updateData: Record<string, unknown> = {
      subscription_start_date: new Date(subscription.current_start * 1000).toISOString(),
      subscription_expiry_time: subscription.current_end ? new Date(subscription.current_end * 1000).toISOString() : null,
      subscription_charge_time: subscription.charge_at ? new Date(subscription.charge_at * 1000).toISOString() : null,
      razorpay_customer_id: subscription.customer_id || null,
      last_payment_method: paymentMethod,
      cancellation_requested_at: null, // Clear cancellation request
      razorpay_plan_id: subscription.plan_id // Always update with Razorpay's current plan_id
    };

    if (isResumeFlow && originalPlanIdToRestore) {
      console.log(`[RAZORPAY_WEBHOOK] Restoring original plan ${originalPlanIdToRestore} for subscription ${subscriptionId}`);
      updateData.plan_id = originalPlanIdToRestore;
      if (originalPlanCycleToRestore) {
          updateData.plan_cycle = originalPlanCycleToRestore;
      } else {
          // Fallback if original_plan_cycle was not stored or is null
          updateData.plan_cycle = planCycleFromNotes || 'monthly'; // Default to 'monthly' or use from notes
          console.warn(`[RAZORPAY_WEBHOOK] Original plan cycle not available or null for resume of ${subscriptionId}. Using fallback/notes: ${updateData.plan_cycle}`);
      }
      updateData.subscription_paused_at = null; // Clear paused timestamp
      updateData.original_plan_id = null; // Clear stored original plan ID
      updateData.original_plan_cycle = null; // Clear stored original plan cycle
    } else {
      // For new activations or non-resume scenarios
      updateData.plan_id = planTypeFromNotes;
      updateData.plan_cycle = planCycleFromNotes;
    }
    
    console.log(`[RAZORPAY_WEBHOOK] Updating subscription ${subscriptionId} with data:`, updateData);

    // For all activations (including plan switches), use the normal webhook processor
    // since the subscription record now exists with the correct subscription ID
    console.log(`[RAZORPAY_WEBHOOK] Updating subscription ${subscriptionId} with status ACTIVE`);

    const updateResult = await webhookProcessor.updateSubscriptionStatus(
      subscriptionId,
      SUBSCRIPTION_STATUS.ACTIVE,
      updateData,
      webhookTimestamp
    );

    // Mark event as processed
    if (updateResult.success) {
      await webhookProcessor.markEventAsSuccess(context.eventId, updateResult.message);
    } else {
      await webhookProcessor.markEventAsFailed(context.eventId, updateResult.message);
    }

    return updateResult;
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Error handling subscription activated:", error);

    // Mark event as failed
    const errorMessage = `Error handling subscription activated: ${error instanceof Error ? error.message : String(error)}`;
    if (context) {
      await webhookProcessor.markEventAsFailed(context.eventId, errorMessage);
    }

    return {
      success: false,
      message: errorMessage
    };
  }
}