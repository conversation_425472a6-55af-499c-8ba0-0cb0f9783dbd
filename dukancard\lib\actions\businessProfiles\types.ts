// Define sorting options for business profiles
export type BusinessSortBy =
  | "name_asc"
  | "name_desc"
  | "created_asc"
  | "created_desc"
  | "likes_asc"
  | "likes_desc"
  | "subscriptions_asc"
  | "subscriptions_desc"
  | "rating_asc"
  | "rating_desc";

// Define types for the business profile data
export type BusinessProfilePublicData = {
  id: string;
  business_name: string | null;
  logo_url: string | null;
  member_name: string | null;
  title: string | null;
  address_line: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  locality: string | null;
  phone: string | null;
  instagram_url: string | null;
  facebook_url: string | null;
  whatsapp_number: string | null;
  about_bio: string | null;
  status: string | null;
  business_slug: string | null;
  theme_color: string | null;
  delivery_info: string | null;
  business_hours: Record<string, unknown> | null;
  business_category: string | null;
  google_maps_url: string | null;
  total_likes: number | null;
  total_subscriptions: number | null;
  average_rating: number | null;
  established_year: number | null;
  custom_branding?: {
    custom_header_text?: string;
    custom_header_image_url?: string;
    hide_dukancard_branding?: boolean;
  } | null;
  custom_ads?: {
    enabled?: boolean;
    image_url?: string;
    link_url?: string;
    uploaded_at?: string;
  } | null;
  created_at: string | null;
  updated_at: string | null;
  trial_end_date: string | null;
  contact_email: string | null;
  // Subscription data
  subscription_status: string | null;
  plan_id: string | null;
};

// Define types for business profile with products
export type BusinessProfileWithProducts = BusinessProfilePublicData & {
  products_services?: Record<string, unknown>[];
};

// Define types for sitemap data
export type SitemapProfileData = {
  business_slug: string;
  updated_at: string | null;
};
