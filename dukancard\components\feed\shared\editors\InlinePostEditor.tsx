"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Check, X } from "lucide-react";
import { toast } from "sonner";
import { updatePostContent } from "@/lib/actions/posts";
import { motion, AnimatePresence } from "framer-motion";

export interface InlinePostEditorProps {
  postId: string;
  initialContent: string;
  onSave?: (_newContent: string) => void;
  onCancel?: () => void;
  className?: string;
}

export default function InlinePostEditor({
  postId,
  initialContent,
  onSave,
  onCancel,
  className = "",
}: InlinePostEditorProps) {
  const [content, setContent] = useState(initialContent);
  const [isLoading, setIsLoading] = useState(false);
  const [charCount, setCharCount] = useState(initialContent.length);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const maxChars = 2000;
  const isOverLimit = charCount > maxChars;
  const hasChanges = content.trim() !== initialContent.trim();

  // Focus textarea when component mounts
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
      // Set cursor to end of text
      textareaRef.current.setSelectionRange(content.length, content.length);
    }
  }, [content.length]);

  // Handle content change
  const handleContentChange = (value: string) => {
    setContent(value);
    setCharCount(value.length);
  };

  // Handle save
  const handleSave = async () => {
    if (!hasChanges) {
      onCancel?.();
      return;
    }

    if (isOverLimit) {
      toast.error("Content too long", {
        description: `Please reduce content to ${maxChars} characters or less.`
      });
      return;
    }

    if (content.trim().length === 0) {
      toast.error("Content required", {
        description: "Post content cannot be empty."
      });
      return;
    }

    setIsLoading(true);

    try {
      const result = await updatePostContent(postId, content.trim());

      if (result.success) {
        toast.success("Post updated successfully");
        onSave?.(content.trim());
      } else {
        toast.error("Failed to update post", {
          description: result.error || "Please try again."
        });
      }
    } catch (error) {
      console.error("Error updating post:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    setContent(initialContent);
    setCharCount(initialContent.length);
    onCancel?.();
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      handleCancel();
    } else if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSave();
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Textarea */}
      <div className="relative">
        <Textarea
          ref={textareaRef}
          value={content}
          onChange={(e) => handleContentChange(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="What's on your mind?"
          className={`min-h-[100px] resize-none ${
            isOverLimit ? "border-destructive focus:border-destructive" : ""
          }`}
          disabled={isLoading}
        />

        {/* Character count */}
        <div className={`absolute bottom-2 right-2 text-xs ${
          isOverLimit ? "text-destructive" : "text-muted-foreground"
        }`}>
          {charCount}/{maxChars}
        </div>
      </div>

      {/* Action buttons */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-3">
        <div className="text-xs text-muted-foreground order-2 sm:order-1">
          Press Ctrl+Enter to save, Esc to cancel
        </div>

        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 order-1 sm:order-2">
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="flex-1 sm:flex-none"
          >
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleCancel}
              disabled={isLoading}
              className="w-full sm:w-auto border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200"
            >
              <X className="h-4 w-4 mr-1" />
              Cancel
            </Button>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="flex-1 sm:flex-none"
          >
            <Button
              type="button"
              size="sm"
              onClick={handleSave}
              disabled={isLoading || isOverLimit || !hasChanges}
              className={`
                w-full sm:w-auto relative overflow-hidden
                bg-gradient-to-r from-blue-500 to-blue-600
                hover:from-blue-600 hover:to-blue-700
                text-white font-medium
                shadow-lg hover:shadow-xl
                transition-all duration-300
                before:absolute before:inset-0
                before:bg-gradient-to-r before:from-blue-400 before:to-blue-500
                before:opacity-0 hover:before:opacity-20
                before:transition-opacity before:duration-300
                ${isLoading || isOverLimit || !hasChanges ? 'cursor-not-allowed opacity-80' : ''}
              `}
              style={{
                boxShadow: (isLoading || isOverLimit || !hasChanges)
                  ? '0 4px 20px rgba(59, 130, 246, 0.3)'
                  : '0 4px 20px rgba(59, 130, 246, 0.4), 0 0 20px rgba(59, 130, 246, 0.2)'
              }}
            >
              <AnimatePresence mode="wait">
                {isLoading ? (
                  <motion.div
                    key="saving"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className="flex items-center justify-center"
                  >
                    <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                    Saving...
                  </motion.div>
                ) : (
                  <motion.div
                    key="save"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className="flex items-center justify-center"
                  >
                    <Check className="h-4 w-4 mr-1" />
                    Save
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
