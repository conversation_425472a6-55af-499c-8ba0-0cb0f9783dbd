/**
 * Supabase Client Configuration
 * Creates the main Supabase client instance for React Native
 */

import { createClient } from '@supabase/supabase-js';
import { SUPABASE_CONFIG } from './publicKeys';

// Create the main Supabase client using public keys
export const supabase = createClient(
  SUPABASE_CONFIG.url,
  SUPABASE_CONFIG.anonKey,
  {
    auth: {
      // Enable automatic session refresh
      autoRefreshToken: true,
      // Persist session in storage
      persistSession: true,
      // Detect session in URL (for OAuth flows)
      detectSessionInUrl: false, // Set to false for React Native
    },
    global: {
      headers: {
        'X-Client-Info': 'dukancard-mobile-client',
      },
    },
  }
);

// Export the client as default export for compatibility
export default supabase;
