import React, { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { ChevronDown, ChevronUp } from 'lucide-react-native';

interface CollapsibleDescriptionProps {
  description: string;
  textColor: string;
  maxLines?: number;
}

export default function CollapsibleDescription({
  description,
  textColor,
  maxLines = 3,
}: CollapsibleDescriptionProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <View style={{ marginTop: 12 }}>
      <Text
        style={{
          fontSize: 16,
          lineHeight: 24,
          color: textColor,
        }}
        numberOfLines={isExpanded ? undefined : maxLines}
      >
        {description}
      </Text>
      
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          marginTop: 8,
          paddingVertical: 8,
        }}
        onPress={toggleExpanded}
        activeOpacity={0.7}
      >
        <Text
          style={{
            fontSize: 14,
            fontWeight: '500',
            color: '#D4AF37',
            marginRight: 4,
          }}
        >
          {isExpanded ? 'Show Less' : 'Show More'}
        </Text>
        {isExpanded ? (
          <ChevronUp size={16} color="#D4AF37" />
        ) : (
          <ChevronDown size={16} color="#D4AF37" />
        )}
      </TouchableOpacity>
    </View>
  );
}
