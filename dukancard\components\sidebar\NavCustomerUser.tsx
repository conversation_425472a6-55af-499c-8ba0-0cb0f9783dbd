"use client";

import React, { useState, useEffect } from "react";
import {
  ChevronsUpDown,
  LogOut,
  // Import other icons if needed for dropdown items
} from "lucide-react";
import { signOutUser } from "@/app/auth/actions"; // Assuming this action is correct
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// Helper to get initials
const getInitials = (name: string | null | undefined): string => {
  if (!name) return "?";
  const names = name.trim().split(/\s+/);
  if (names.length === 1 && names[0]) return names[0].charAt(0).toUpperCase();
  if (names.length > 1 && names[0] && names[names.length - 1]) {
    return (
      names[0].charAt(0).toUpperCase() +
      names[names.length - 1].charAt(0).toUpperCase()
    );
  }
  return "?";
};

interface NavCustomerUserProps {
  user: {
    name: string | null;
    avatar: string | null;
    // email?: string | null; // Add if needed
  };
}

export function NavCustomerUser({ user }: NavCustomerUserProps) {
  const { isMobile } = useSidebar();
  const initials = getInitials(user.name);
  const displayName = user.name || "User";
  const [isTablet, setIsTablet] = useState(false);

  useEffect(() => {
    // Check if device is a tablet (between 768px and 1024px)
    const checkTablet = () => {
      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);
    };

    // Initial check
    checkTablet();

    // Add event listener for resize
    window.addEventListener('resize', checkTablet);

    // Cleanup
    return () => window.removeEventListener('resize', checkTablet);
  }, []);

  // Calculate bottom padding based on device type
  const bottomPadding = isMobile ? "pb-16" : isTablet ? "pb-14" : "";

  return (
    <SidebarMenu className={cn(bottomPadding)}>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg border border-[var(--brand-gold)]/30">
                {user.avatar ? (
                  <AvatarImage src={user.avatar} alt={displayName} />
                ) : null}
                <AvatarFallback className="rounded-lg bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-900 text-blue-800 dark:text-blue-200 text-xs border border-[var(--brand-gold)]/30">
                  {initials}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{displayName}</span>
                {/* Optional: Add email or other subtext if available */}
                {/* <span className="truncate text-xs text-muted-foreground">{user.email}</span> */}
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                 <Avatar className="h-8 w-8 rounded-lg">
                    {user.avatar ? (
                      <AvatarImage src={user.avatar} alt={displayName} />
                    ) : null}
                    <AvatarFallback className="rounded-lg bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-900 text-blue-800 dark:text-blue-200 text-xs">
                      {initials}
                    </AvatarFallback>
                 </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">{displayName}</span>
                   {/* <span className="truncate text-xs text-muted-foreground">{user.email}</span> */}
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            {/* Add relevant customer dropdown items here, e.g., link to settings */}
            {/* <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" /> Settings
            </DropdownMenuItem>
            <DropdownMenuSeparator /> */}
            <form action={signOutUser} className="w-full px-2 py-1.5">
              <Button
                variant="ghost"
                type="submit"
                className="w-full justify-start p-0 h-auto font-normal cursor-pointer"
              >
                <LogOut className="mr-2 h-4 w-4" />
                Log out
              </Button>
            </form>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
