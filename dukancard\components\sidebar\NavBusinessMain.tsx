"use client";

import React from "react";
import { usePathname } from 'next/navigation';
import { SidebarLink } from "./SidebarLink";
import {
  ChevronRight,
  LayoutDashboard,
  CreditCard,
  Package,
  BarChart3,
  Settings,
  WalletCards,
  Tag,
  Heart,
  Bell,
  Store,
  Users,
  User,
  Activity,
  Star,
  Image,
  LayoutList,
  FileEdit,
  type LucideIcon
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import { Badge } from "@/components/ui/badge";

// Map string icon names to actual Lucide components
export const iconMap: { [key: string]: LucideIcon } = {
  LayoutDashboard,
  CreditCard,
  Package,
  BarChart3,
  Settings,
  WalletCards,
  Tag,
  Heart,
  Bell,
  Store,
  Users,
  User,
  Activity,
  Star,
  Image,
  LayoutList,
  FileEdit,
};

interface NavItemData {
  href: string;
  icon: string;
  label: string;
  isActive?: boolean;
  badge?: string;
  badgeVariant?: "default" | "secondary" | "destructive" | "outline" | "upgrade";
  items?: {
    href: string;
    label: string;
  }[];
}

interface NavBusinessMainProps {
  items: NavItemData[];
}

export function NavBusinessMain({ items }: NavBusinessMainProps) {
  const pathname = usePathname();

  return (
    <SidebarGroup>
      {/* <SidebarGroupLabel>Platform</SidebarGroupLabel> // Optional label */}
      <SidebarMenu>
        {items.map((item) => {
          const IconComponent = iconMap[item.icon];
          // Determine active state based on current path
          const isActive = pathname === item.href || (item.href !== "/dashboard/business" && pathname.startsWith(item.href));

          // If item has sub-items, render as collapsible
          if (item.items && item.items.length > 0) {
            return (
              <Collapsible
                key={item.label}
                asChild
                defaultOpen={isActive}
                className="group/collapsible"
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild>
                    {/* Apply brand gold styling more directly to active state */}
                    <SidebarMenuButton tooltip={item.label} isActive={isActive} className={cn(isActive && "bg-accent text-[var(--brand-gold)] dark:bg-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]")}>
                      {IconComponent && <IconComponent className={cn(isActive ? "text-[var(--brand-gold)]" : "")}/>}
                      <span>{item.label}</span>
                      <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.items.map((subItem) => {
                         const isSubActive = pathname === subItem.href;
                         return (
                            <SidebarMenuSubItem key={subItem.label}>
                              <SidebarMenuSubButton asChild isActive={isSubActive}>
                                <SidebarLink href={subItem.href}>
                                  <span>{subItem.label}</span>
                                </SidebarLink>
                              </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                         );
                      })}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            );
          }

          // Otherwise, render as a direct link
          return (
            <SidebarMenuItem key={item.label}>
               {/* Apply brand gold styling more directly to active state */}
              <SidebarMenuButton asChild tooltip={item.label} isActive={isActive} className={cn(isActive && "bg-accent text-[var(--brand-gold)] dark:bg-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]")}>
                <SidebarLink href={item.href}>
                  {IconComponent && <IconComponent className={cn(isActive ? "text-[var(--brand-gold)]" : "")}/>}
                  <span>{item.label}</span>
                  {item.badge && (
                    <Badge
                      variant={item.badgeVariant === "upgrade" ? "default" : item.badgeVariant || "default"}
                      className={cn(
                        "ml-2 text-xs",
                        item.badgeVariant === "upgrade" && "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300"
                      )}
                    >
                      {item.badge}
                    </Badge>
                  )}
                </SidebarLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
