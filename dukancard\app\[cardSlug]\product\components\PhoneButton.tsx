"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Phone } from "lucide-react";
import { useState } from "react";

interface PhoneButtonProps {
  phoneNumber: string;
  _businessName: string; // Prefix with underscore to indicate it's intentionally unused
}

export default function PhoneButton({
  phoneNumber,
  _businessName, // Prefix with underscore to indicate it's intentionally unused
}: PhoneButtonProps) {
  const [isHovered, setIsHovered] = useState(false);

  // Format phone number (remove any non-digit characters)
  const formattedNumber = phoneNumber.replace(/\D/g, "");

  return (
    <div className="relative group">
      {/* Button glow effect */}
      <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500/30 to-blue-600/30 rounded-xl blur-md opacity-75 group-hover:opacity-100 transition-opacity duration-300"></div>

      <Button
        className="relative w-full bg-gradient-to-br from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium flex items-center justify-center gap-3 py-6 rounded-xl border border-blue-500/20 transition-all duration-300"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={() => window.open(`tel:${formattedNumber}`, "_blank")}
      >
        {/* Shimmer effect */}
        <div className="absolute inset-0 w-full h-full overflow-hidden rounded-xl">
          <div className="absolute inset-0 w-[200%] h-full bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer"></div>
        </div>

        <Phone className={`w-5 h-5 ${isHovered ? 'animate-pulse' : ''}`} />
        <span className="text-base font-semibold tracking-wide">Call</span>
      </Button>
    </div>
  );
}

// Fallback button for when phone is not available
export function PhoneButtonDisabled() {
  return (
    <div className="relative group">
      {/* Button glow effect (dimmed) */}
      <div className="absolute -inset-0.5 bg-gradient-to-r from-neutral-400/20 to-neutral-600/20 rounded-xl blur-md opacity-50 transition-opacity duration-300"></div>

      <Button
        className="relative w-full bg-gradient-to-br from-neutral-500 to-neutral-600 text-white font-medium flex items-center justify-center gap-3 py-6 cursor-not-allowed opacity-80 rounded-xl border border-neutral-500/10 transition-all duration-300"
        disabled
      >
        <Phone className="w-5 h-5" />
        <span className="text-base font-semibold tracking-wide">Call Unavailable</span>
      </Button>
    </div>
  );
}
