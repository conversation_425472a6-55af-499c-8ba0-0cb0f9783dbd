"use client";

import { motion, AnimatePresence } from "framer-motion";
import React, { useState, useEffect, useRef, useCallback } from "react";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  combinedSearchSchema,
  CombinedSearchFormData,
} from "@/lib/schemas/locationSchemas";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  MapPin,
  Loader2,
  Building,
  ArrowRight,
  Building2
} from "lucide-react";
import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList
} from "@/components/ui/command";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getPincodeDetails } from "@/backend/supabase/services/location/location";
import { getCitySuggestionsClient, getPincodeDetailsClient } from "@/lib/client/locationUtils";

interface ModernSearchSectionProps {
  initialValues: {
    pincode?: string | null;
    city?: string | null;
    locality?: string | null;
  };
  onSearch: (_data: CombinedSearchFormData) => void;
  isSearching: boolean;
}

export default function ModernSearchSection({
  initialValues,
  onSearch,
  isSearching
}: ModernSearchSectionProps) {
  const [isPincodeLoading, setIsPincodeLoading] = useState(false);
  const [isLoadingCities, setIsLoadingCities] = useState(false);
  // Set default search type and ensure it's initialized properly for animations
  const [searchType, setSearchType] = useState<"pincode" | "city">(
    initialValues.city ? "city" : "pincode"
  );

  // Create a unique ID for the layout animation to ensure it works on initial render
  const [layoutId] = useState(`searchTypeBackground-${Math.random()}`);
  const [availableLocalities, setAvailableLocalities] = useState<string[]>([]);
  const [citySuggestions, setCitySuggestions] = useState<string[]>([]);
  const [cityQuery, setCityQuery] = useState(initialValues.city || "");
  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);
  const cityInputRef = useRef<HTMLInputElement>(null);

  // Form for combined search
  const form = useForm<CombinedSearchFormData>({
    resolver: zodResolver(combinedSearchSchema),
    defaultValues: {
      businessName: "", // Keep this for schema compatibility
      pincode: initialValues.pincode || "",
      city: initialValues.city || "",
      locality: initialValues.locality || "_any",
    },
    mode: "onChange",
  });

  // Fetch localities when pincode is valid
  const handlePincodeChange = useCallback(async (pincode: string) => {
    if (!pincode || pincode.length !== 6 || !/^\d{6}$/.test(pincode)) {
      setAvailableLocalities([]);
      form.setValue("locality", "_any");
      return;
    }

    setIsPincodeLoading(true);
    try {
      // Try client-side function first for better performance
      const result = await getPincodeDetailsClient(pincode);
      if (result.localities) {
        setAvailableLocalities(result.localities);
      } else if (result.error) {
        // Fall back to server action if client-side fails
        console.log("Falling back to server action for pincode details");
        const serverResult = await getPincodeDetails(pincode);
        if (serverResult.data?.localities) {
          setAvailableLocalities(serverResult.data.localities);
        } else {
          setAvailableLocalities([]);
        }
      } else {
        setAvailableLocalities([]);
      }
    } catch (error) {
      console.error("Error fetching localities:", error);
      setAvailableLocalities([]);
    } finally {
      setIsPincodeLoading(false);
    }
  }, [form]);

  // Fetch city suggestions when user types
  useEffect(() => {
    const fetchCitySuggestions = async () => {
      if (cityQuery.length < 2) {
        setCitySuggestions([]);
        setIsLoadingCities(false);
        return;
      }

      setIsLoadingCities(true);
      try {
        // Use client-side function for better performance
        const result = await getCitySuggestionsClient(cityQuery);
        if (result.cities) {
          setCitySuggestions(result.cities);
        } else {
          setCitySuggestions([]);
        }
      } catch (error) {
        console.error("Error fetching city suggestions:", error);
        setCitySuggestions([]);
      } finally {
        setIsLoadingCities(false);
      }
    };

    fetchCitySuggestions();
  }, [cityQuery]);

  // Handle city selection from dropdown
  const handleCitySelect = (city: string) => {
    setCityQuery(city);
    form.setValue("city", city, { shouldValidate: true });
    setIsCityDropdownOpen(false);
  };

  // Handle search type change
  const handleSearchTypeChange = (type: "pincode" | "city") => {
    setSearchType(type);

    // Clear the other field when switching
    if (type === "pincode") {
      form.setValue("city", "");
    } else {
      form.setValue("pincode", "");
      form.setValue("locality", "");
      setAvailableLocalities([]);
    }
  };

  // Initialize localities if pincode is provided
  useEffect(() => {
    if (initialValues.pincode) {
      handlePincodeChange(initialValues.pincode);
    }
  }, [initialValues.pincode, handlePincodeChange]);

  // Handle form submission
  const onSubmit = (data: CombinedSearchFormData) => {
    // Clear the field not being used based on search type
    if (searchType === "pincode") {
      data.city = "";
      // Convert "_any" locality to empty string
      if (data.locality === "_any") {
        data.locality = "";
      }
    } else {
      data.pincode = "";
      data.locality = "";
    }

    onSearch(data);
  };

  return (
    <div className="w-full">
      {/* No header text as requested */}

      {/* Main search container */}
      <motion.div
        className="relative"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        {/* Background elements */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          {/* Simple background glow */}
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[300px] h-[200px] bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10 rounded-full blur-[80px] opacity-50"></div>
        </div>

        {/* Search type selector - Exactly like homepage */}
        <motion.div
          className="flex justify-center mb-5"
          initial={{ opacity: 1, y: 0 }}
          animate={{ opacity: 1, y: 0 }}
          layoutRoot
        >
          <div className="inline-flex rounded-full p-1 bg-white/30 dark:bg-neutral-800/30 backdrop-blur-md border border-neutral-200/50 dark:border-neutral-700/50">
            <motion.button
              className={`relative px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                searchType === "pincode"
                  ? "text-[var(--brand-gold-foreground)]"
                  : "text-neutral-600 dark:text-neutral-400"
              }`}
              onClick={() => handleSearchTypeChange("pincode")}
              initial={{ opacity: 1 }}
              animate={searchType === "pincode" ? { opacity: 1, scale: 1.05 } : { opacity: 0.7, scale: 1 }}
              whileHover={{ scale: searchType === "pincode" ? 1.05 : 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {/* Always render the background for pincode with no opacity animation */}
              {searchType === "pincode" && (
                <motion.div
                  className="absolute inset-0 bg-[var(--brand-gold)] rounded-full -z-10"
                  layoutId={layoutId}
                  initial={{ opacity: 1 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", duration: 0.5 }}
                />
              )}
              <div className="flex items-center gap-1.5">
                <MapPin className="h-4 w-4" />
                <span>Pincode</span>
              </div>
            </motion.button>

            <motion.button
              className={`relative px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                searchType === "city"
                  ? "text-[var(--brand-gold-foreground)]"
                  : "text-neutral-600 dark:text-neutral-400"
              }`}
              onClick={() => handleSearchTypeChange("city")}
              initial={{ opacity: 1 }}
              animate={searchType === "city" ? { opacity: 1, scale: 1.05 } : { opacity: 0.7, scale: 1 }}
              whileHover={{ scale: searchType === "city" ? 1.05 : 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {/* Always render the background for city with no opacity animation */}
              {searchType === "city" && (
                <motion.div
                  className="absolute inset-0 bg-[var(--brand-gold)] rounded-full -z-10"
                  layoutId={layoutId}
                  initial={{ opacity: 1 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", duration: 0.5 }}
                />
              )}
              <div className="flex items-center gap-1.5">
                <Building className="h-4 w-4" />
                <span>City</span>
              </div>
            </motion.button>
          </div>
        </motion.div>

        {/* Search input area */}
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <AnimatePresence mode="wait">
              {searchType === "pincode" ? (
                <motion.div
                  key="pincode-search"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="flex flex-col gap-3 mb-4 px-0 mx-0 w-full">
                    <div className="relative flex-1 group">
                      <motion.div
                        className="absolute -inset-0.5 rounded-lg bg-gradient-to-r from-[var(--brand-gold)]/20 to-blue-500/20 opacity-0 group-hover:opacity-100 blur transition-opacity duration-300"
                        whileHover={{ opacity: 0.8 }}
                      />

                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10 pointer-events-none text-neutral-400 dark:text-neutral-500">
                        <MapPin className="h-4 w-4" />
                      </div>

                      <Input
                        id="pincode"
                        type="tel"
                        inputMode="numeric"
                        maxLength={6}
                        placeholder="Enter pincode to find nearby businesses"
                        {...form.register("pincode", {
                          onChange: (e) => handlePincodeChange(e.target.value),
                        })}
                        className="pl-10 pr-4 py-2 h-12 bg-white/80 dark:bg-neutral-900/80 backdrop-blur-sm border-neutral-200/50 dark:border-neutral-800/50 focus-visible:ring-1 focus-visible:ring-[var(--brand-gold)] focus-visible:border-[var(--brand-gold)] text-sm rounded-lg w-full shadow-sm relative z-0"
                      />

                      {isPincodeLoading && (
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          <Loader2 className="h-5 w-5 animate-spin text-[var(--brand-gold)]" />
                        </div>
                      )}
                    </div>

                    <div className="relative flex-1 group">
                      <Controller
                        name="locality"
                        control={form.control}
                        render={({ field }) => (
                          <Select
                            disabled={availableLocalities.length === 0 || isPincodeLoading}
                            value={field.value || "_any"}
                            onValueChange={(value) => field.onChange(value)}
                          >
                            <SelectTrigger
                              id="locality"
                              className="py-2 h-12 bg-white/80 dark:bg-neutral-900/80 backdrop-blur-sm border-neutral-200/50 dark:border-neutral-800/50 focus-visible:ring-1 focus-visible:ring-[var(--brand-gold)] focus-visible:border-[var(--brand-gold)] text-sm rounded-lg w-full shadow-sm relative z-10"
                            >
                              <Building2 className="h-4 w-4 mr-2 text-neutral-400 dark:text-neutral-500" />
                              <SelectValue placeholder="Select locality" />
                            </SelectTrigger>
                            <SelectContent className="max-h-[280px] bg-white dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700">
                              <SelectItem value="_any">
                                <div className="flex items-center">
                                  <span>Any Locality</span>
                                </div>
                              </SelectItem>
                              {availableLocalities.map((loc) => (
                                <SelectItem key={loc} value={loc}>
                                  {loc}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>

                    <motion.div
                      className="w-full md:w-auto mt-3 md:ml-4 md:mt-0 px-0"
                    >
                      <Button
                        type="submit"
                        disabled={isSearching || !form.getValues("pincode")}
                        className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-[var(--brand-gold-foreground)] font-medium h-12 px-4 md:px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 relative overflow-hidden w-full text-sm"
                      >
                        {/* Shimmer effect */}
                        <motion.div
                          className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent"
                          initial={{ x: "-100%" }}
                          animate={{ x: "100%" }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "linear",
                            repeatDelay: 0.5
                          }}
                        />

                        {isSearching ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <div className="flex items-center gap-1">
                            <span>Search</span>
                            <ArrowRight className="h-4 w-4" />
                          </div>
                        )}
                      </Button>
                    </motion.div>
                  </div>

                  {form.formState.errors.pincode && (
                    <motion.p
                      initial={{ opacity: 0, y: -5 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-sm text-red-500 dark:text-red-400 mt-2"
                    >
                      {form.formState.errors.pincode.message}
                    </motion.p>
                  )}
                </motion.div>
              ) : (
                <motion.div
                  key="city-search"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="flex flex-col gap-3 px-0 mx-0 w-full">
                    <div className="relative flex-1 group">
                      <motion.div
                        className="absolute -inset-0.5 rounded-lg bg-gradient-to-r from-[var(--brand-gold)]/20 to-blue-500/20 opacity-0 group-hover:opacity-100 blur transition-opacity duration-300"
                        whileHover={{ opacity: 0.8 }}
                      />

                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10 pointer-events-none text-neutral-400 dark:text-neutral-500">
                        <Building className="h-4 w-4" />
                      </div>

                      <div className="relative">
                        <Input
                          id="city"
                          placeholder="Enter city name (min. 2 characters)"
                          value={cityQuery}
                          onChange={(e) => {
                            setCityQuery(e.target.value);
                            form.setValue("city", e.target.value, { shouldValidate: true });
                            if (e.target.value.length >= 2) {
                              setIsCityDropdownOpen(true);
                            } else {
                              setIsCityDropdownOpen(false);
                            }
                          }}
                          onFocus={() => {
                            if (cityQuery.length >= 2) {
                              setIsCityDropdownOpen(true);
                            }
                          }}
                          className="pl-10 pr-4 py-2 h-12 bg-white/80 dark:bg-neutral-900/80 backdrop-blur-sm border-neutral-200/50 dark:border-neutral-800/50 focus-visible:ring-1 focus-visible:ring-[var(--brand-gold)] focus-visible:border-[var(--brand-gold)] text-sm rounded-lg w-full shadow-sm relative z-0"
                          ref={cityInputRef}
                        />

                        {isCityDropdownOpen && (cityQuery.length >= 2) && (
                          <div className="absolute z-50 w-full mt-1 bg-white/90 dark:bg-neutral-900/90 backdrop-blur-md rounded-lg shadow-lg border border-neutral-200/50 dark:border-neutral-700/50 max-h-60 overflow-auto">
                            <Command>
                              <CommandList>
                                <CommandGroup>
                                  {isLoadingCities ? (
                                    // Skeleton loading UI
                                    <>
                                      {[1, 2, 3, 4, 5].map((index) => (
                                        <div key={index} className="p-3 flex items-center animate-pulse">
                                          <div className="h-4 w-4 rounded-full bg-[var(--brand-gold)]/20 dark:bg-[var(--brand-gold)]/10 mr-2 flex-shrink-0"></div>
                                          <div className="h-4 w-full max-w-[120px] bg-neutral-200 dark:bg-neutral-700 rounded"></div>
                                        </div>
                                      ))}
                                    </>
                                  ) : citySuggestions.length > 0 ? (
                                    // Results
                                    citySuggestions.map((city) => (
                                      <CommandItem
                                        key={city}
                                        onSelect={() => handleCitySelect(city)}
                                        className="cursor-pointer p-3 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors"
                                      >
                                        <Building className="mr-2 h-4 w-4 text-[var(--brand-gold)]" />
                                        <span>{city}</span>
                                      </CommandItem>
                                    ))
                                  ) : (
                                    // No results
                                    <div className="p-3 text-center text-neutral-500 dark:text-neutral-400">
                                      No cities found
                                    </div>
                                  )}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </div>
                        )}
                      </div>
                    </div>

                    <motion.div
                      className="w-full md:w-auto mt-3 md:ml-4 md:mt-0 px-0"
                    >
                      <Button
                        type="submit"
                        disabled={isSearching || !form.getValues("city")}
                        className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-[var(--brand-gold-foreground)] font-medium h-12 px-4 md:px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 relative overflow-hidden w-full text-sm"
                      >
                        {/* Shimmer effect */}
                        <motion.div
                          className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent"
                          initial={{ x: "-100%" }}
                          animate={{ x: "100%" }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "linear",
                            repeatDelay: 0.5
                          }}
                        />

                        {isSearching ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <div className="flex items-center gap-1">
                            <span>Search</span>
                            <ArrowRight className="h-4 w-4" />
                          </div>
                        )}
                      </Button>
                    </motion.div>
                  </div>

                  {form.formState.errors.city && (
                    <motion.p
                      initial={{ opacity: 0, y: -5 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-sm text-red-500 dark:text-red-400 mt-2"
                    >
                      {form.formState.errors.city.message}
                    </motion.p>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
        </form>


      </motion.div>
    </div>
  );
}
