"use client";

import { createContext, useContext, useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { searchDiscoverCombined } from "../actions/combinedActions";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import { NearbyProduct } from "../actions/types";
import { DISCOVER_PRODUCTS_PER_PAGE } from "../constants/paginationConstants";
import { mapProductSortToBackend } from "../utils/sortMappings";
import {
  BUSINESS_NAME_PARAM,
  BUSINESS_SORT_PARAM,
  PINCODE_PARAM,
  CITY_PARAM,
  LOCALITY_PARAM,
  VIEW_TYPE_PARAM,
  PRODUCT_SORT_PARAM,
  PRODUCT_TYPE_PARAM,
} from "../constants/urlParamConstants";
import {
  DiscoverContextType,
  DiscoverSearchResult,
  ProductFilterOption,
  ProductSortOption,
  ViewType,
} from "./types";
import { useBusinessContextFunctions } from "./businessContextFunctions";
import { useProductContextFunctions } from "./productContextFunctions";
import { useCommonContextFunctions } from "./commonContextFunctions";

// Create the context
const DiscoverContext = createContext<DiscoverContextType | undefined>(
  undefined
);

// Provider component
export function DiscoverProvider({ children }: { children: React.ReactNode }) {
  const searchParams = useSearchParams();

  // Get initial values from URL
  const initialBusinessName = searchParams.get(BUSINESS_NAME_PARAM) || null;
  const initialPincode = searchParams.get(PINCODE_PARAM) || null;
  const initialCity = searchParams.get(CITY_PARAM) || null;
  const initialLocality = searchParams.get(LOCALITY_PARAM) || null;
  const initialViewType =
    (searchParams.get(VIEW_TYPE_PARAM) as ViewType) || "products";

  // Get initial sort values from URL
  let initialBusinessSort = (searchParams.get(BUSINESS_SORT_PARAM) ||
    "created_desc") as BusinessSortBy;
  let initialProductSort = (searchParams.get(PRODUCT_SORT_PARAM) ||
    "newest") as ProductSortOption;

  // Handle migration from old sortBy parameter to new parameters
  const oldSortBy = searchParams.get("sortBy");
  if (oldSortBy) {
    // If we have the old parameter, use it for the appropriate view type and remove it
    if (initialViewType === "cards") {
      initialBusinessSort = oldSortBy as BusinessSortBy;
    } else if (initialViewType === "products") {
      initialProductSort = oldSortBy as ProductSortOption;
    }

    // Update URL to remove the old parameter and set the new ones
    if (typeof window !== "undefined") {
      const url = new URL(window.location.href);
      url.searchParams.delete("sortBy");
      url.searchParams.set(BUSINESS_SORT_PARAM, initialBusinessSort);
      url.searchParams.set(PRODUCT_SORT_PARAM, initialProductSort);
      window.history.replaceState({}, "", url.toString());
    }
  }
  const initialProductFilterBy =
    (searchParams.get(PRODUCT_TYPE_PARAM) as ProductFilterOption) || "all";

  // State
  const [viewType, setViewType] = useState<ViewType>(initialViewType);
  const [sortBy, setSortBy] = useState<BusinessSortBy>(initialBusinessSort);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [productFilterBy, setProductFilterBy] = useState<ProductFilterOption>(
    initialProductFilterBy
  );
  const [productSortBy, setProductSortBy] =
    useState<ProductSortOption>(initialProductSort);
  const [searchResult, setSearchResult] = useState<DiscoverSearchResult | null>(
    null
  );
  const [businesses, setBusinesses] = useState<BusinessCardData[]>([]);
  const [products, setProducts] = useState<NearbyProduct[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Get business context functions
  const { handleBusinessSortChange, handleBusinessSearch, loadMoreBusinesses } =
    useBusinessContextFunctions(
      viewType,
      setIsSearching,
      setSearchResult,
      setIsAuthenticated,
      setBusinesses,
      setHasMore,
      setTotalCount,
      setCurrentPage,
      setSortBy,
      setSearchError,
      businesses,
      sortBy
    );

  // Get product context functions
  const {
    handleProductSortChange,
    handleProductSearch,
    handleProductFilterChange,
    loadMoreProducts,
  } = useProductContextFunctions(
    viewType,
    setIsSearching,
    setSearchResult,
    setIsAuthenticated,
    setProducts,
    setHasMore,
    setTotalCount,
    setCurrentPage,
    setProductSortBy,
    setProductFilterBy,
    setSearchError,
    products,
    sortBy,
    productSortBy,
    productFilterBy
  );

  // Get common context functions
  const {
    isPending,
    handleViewChange,
    performSearch,
    loadMore: commonLoadMore,
  } = useCommonContextFunctions(
    viewType,
    setViewType,
    setIsSearching,
    setSearchResult,
    setIsAuthenticated,
    setBusinesses,
    setProducts,
    setHasMore,
    setTotalCount,
    setCurrentPage,
    setSearchError,
    sortBy,
    productSortBy,
    productFilterBy
  );

  // Wrapper for loadMore to pass the correct functions
  const loadMore = async () => {
    await commonLoadMore(
      loadMoreBusinesses,
      loadMoreProducts,
      currentPage,
      isLoadingMore,
      setIsLoadingMore
    );
  };

  // Load initial data on mount
  useEffect(() => {
    // Always set isSearching to true initially to show skeleton loaders
    setIsSearching(true);

    // If URL has search params, perform search with those params
    if (initialBusinessName || initialPincode || initialCity) {
      // Handle "_any" locality value
      const normalizedLocality =
        initialLocality === "_any" ? "" : initialLocality;

      // Use only the properties that exist in CombinedSearchFormData
      performSearch({
        businessName: initialBusinessName,
        pincode: initialPincode,
        city: initialCity,
        locality: normalizedLocality,
      });
    } else {
      // Otherwise, load all businesses or products based on viewType

      const fetchInitialData = async () => {
        try {
          // Load products by default for the initial load
          const initialViewType = "products";
          console.log("Initial load: Fetching products");

          const result = await searchDiscoverCombined({
            viewType: initialViewType,
            page: 1,
            limit: DISCOVER_PRODUCTS_PER_PAGE,
            productSort: mapProductSortToBackend(productSortBy),
            productType: productFilterBy === "all" ? null : productFilterBy,
          });

          if (result.data) {
            setSearchResult(result.data);
            setIsAuthenticated(result.data.isAuthenticated);

            if (result.data.products) {
              setProducts(result.data.products);
              // Use the hasMore flag from the server
              setHasMore(result.data.hasMore);
            } else {
              setHasMore(false);
            }

            setTotalCount(result.data.totalCount);
          } else {
            setSearchError(result.error || "Failed to fetch results.");
          }
        } catch (error) {
          console.error("Unexpected error loading initial data:", error);
          setSearchError("An unexpected error occurred. Please try again.");
        } finally {
          setIsSearching(false);
        }
      };

      fetchInitialData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Create the context value
  const contextValue: DiscoverContextType = {
    viewType,
    sortBy,
    isSearching,
    isPending,
    isLoadingMore,
    searchError,
    productFilterBy,
    productSortBy,
    searchResult,
    businesses,
    products,
    currentPage,
    hasMore,
    totalCount,
    isAuthenticated,
    performSearch,
    handleViewChange,
    handleBusinessSortChange,
    handleBusinessSearch,
    handleProductSearch,
    handleProductSortChange,
    handleProductFilterChange,
    loadMore,
  };

  return (
    <DiscoverContext.Provider value={contextValue}>
      {children}
    </DiscoverContext.Provider>
  );
}

// Create a custom hook to use the context
export function useDiscoverContext() {
  const context = useContext(DiscoverContext);
  if (context === undefined) {
    throw new Error(
      "useDiscoverContext must be used within a DiscoverProvider"
    );
  }
  return context;
}
