"use client";

import { createContext, useContext, useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { BusinessProfilePublicData } from "@/lib/actions/businessProfiles/types";
import { NearbyProduct } from "@/app/(main)/discover/actions/types";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import { mapProductSortToBackend } from "@/app/(main)/discover/utils/sortMappings";
import {
  BUSINESS_NAME_PARAM,
  BUSINESS_SORT_PARAM,
  PINCODE_PARAM,
  CITY_PARAM,
  LOCALITY_PARAM,
  VIEW_TYPE_PARAM,
  PRODUCT_SORT_PARAM,
  PRODUCT_TYPE_PARAM,
} from "@/app/(main)/discover/constants/urlParamConstants";
import {
  CategoryContextType,
  CategorySearchResult,
  ProductFilterOption,
  ProductSortOption,
  ViewType,
  SerializableCategory,
} from "./types";
import { useBusinessContextFunctions } from "./businessContextFunctions";
import { useProductContextFunctions } from "./productContextFunctions";
import { useCommonContextFunctions } from "./commonContextFunctions";
import { CATEGORY_PRODUCTS_PER_PAGE } from "../constants/paginationConstants";
import { searchCategoryCombined } from "../actions";

// Create the context
const CategoryContext = createContext<CategoryContextType | undefined>(
  undefined
);

// Hook to use the category context
export function useCategoryContext() {
  const context = useContext(CategoryContext);
  if (context === undefined) {
    throw new Error("useCategoryContext must be used within a CategoryProvider");
  }
  return context;
}

// Import LocationInfo type
import { LocationInfo } from "../ModernCategoryClient";

// Provider component
export function CategoryProvider({
  children,
  category,
  initialBusinesses,
  locationInfo,
}: {
  children: React.ReactNode;
  category: SerializableCategory;
  initialBusinesses: BusinessProfilePublicData[];
  locationInfo?: LocationInfo;
}) {
  const searchParams = useSearchParams();

  // Get initial values from URL params
  const initialViewType = (searchParams.get(VIEW_TYPE_PARAM) as ViewType) || "products"; // Default to products view
  const initialBusinessName = searchParams.get(BUSINESS_NAME_PARAM);
  const initialPincode = searchParams.get(PINCODE_PARAM);
  const initialCity = searchParams.get(CITY_PARAM);
  const initialLocality = searchParams.get(LOCALITY_PARAM);
  const initialBusinessSort = searchParams.get(BUSINESS_SORT_PARAM) as BusinessSortBy || "created_desc";
  const initialProductSort = searchParams.get(PRODUCT_SORT_PARAM) as ProductSortOption || "newest";
  const initialProductType = searchParams.get(PRODUCT_TYPE_PARAM) as ProductFilterOption || "all";

  // State for the category page
  const [viewType, setViewType] = useState<ViewType>(initialViewType);
  const [sortBy, setSortBy] = useState<BusinessSortBy>(initialBusinessSort);
  const [productSortBy, setProductSortBy] = useState<ProductSortOption>(initialProductSort);
  const [productFilterBy, setProductFilterBy] = useState<ProductFilterOption>(initialProductType);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [searchResult, setSearchResult] = useState<CategorySearchResult | null>(null);
  const [businesses, setBusinesses] = useState<(BusinessProfilePublicData | BusinessCardData)[]>(initialBusinesses || []);
  const [products, setProducts] = useState<NearbyProduct[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [totalCount, setTotalCount] = useState(initialBusinesses?.length || 0);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Get business context functions
  const { handleBusinessSortChange, handleBusinessSearch, loadMoreBusinesses } =
    useBusinessContextFunctions(
      category,
      viewType,
      setIsSearching,
      setSearchResult,
      setIsAuthenticated,
      setBusinesses,
      setHasMore,
      setTotalCount,
      setCurrentPage,
      setSortBy,
      setSearchError,
      businesses,
      sortBy,
      locationInfo // Pass locationInfo to the business context functions
    );

  // Get product context functions
  const {
    handleProductSortChange,
    handleProductSearch,
    handleProductFilterChange,
    loadMoreProducts,
  } = useProductContextFunctions(
    category,
    viewType,
    setIsSearching,
    setSearchResult,
    setIsAuthenticated,
    setProducts,
    setHasMore,
    setTotalCount,
    setCurrentPage,
    setProductSortBy,
    setProductFilterBy,
    setSearchError,
    products,
    sortBy,
    productSortBy,
    productFilterBy,
    locationInfo // Pass locationInfo to the product context functions
  );

  // Get common context functions
  const {
    isPending,
    handleViewChange,
    performSearch,
    loadMore: commonLoadMore,
  } = useCommonContextFunctions(
    category,
    viewType,
    setViewType,
    setIsSearching,
    setSearchResult,
    setIsAuthenticated,
    setBusinesses,
    setProducts,
    setHasMore,
    setTotalCount,
    setCurrentPage,
    setSearchError,
    sortBy,
    productSortBy,
    productFilterBy,
    locationInfo // Pass locationInfo to the common context functions
  );

  // Wrapper for loadMore to pass the correct functions
  const loadMore = async () => {
    await commonLoadMore(
      loadMoreBusinesses,
      loadMoreProducts,
      currentPage,
      isLoadingMore,
      setIsLoadingMore
    );
  };

  // Load initial data on mount
  useEffect(() => {
    // Always set isSearching to true initially to show skeleton loaders
    setIsSearching(true);

    // If URL has search params, perform search with those params
    if (initialBusinessName || initialPincode || initialCity) {
      // Handle "_any" locality value
      const normalizedLocality =
        initialLocality === "_any" ? "" : initialLocality;

      // Extract state and city slugs from the URL
      const urlParts = window.location.pathname.split('/').filter(part => part.trim() !== '');

      // Find the index of the category slug
      const categoryIndex = urlParts.findIndex(part => part === category.slug);

      // Get stateSlug and citySlug from URL if available
      let stateSlugFromUrl = null;
      let citySlugFromUrl = null;

      if (categoryIndex >= 0 && categoryIndex + 1 < urlParts.length) {
          stateSlugFromUrl = urlParts[categoryIndex + 1];
      }

      if (categoryIndex >= 0 && categoryIndex + 2 < urlParts.length) {
          citySlugFromUrl = urlParts[categoryIndex + 2];
      }

      // Use only the properties that exist in CombinedSearchFormData
      performSearch({
        businessName: initialBusinessName,
        state: locationInfo?.state || null, // Include state from locationInfo
        pincode: initialPincode,
        city: initialCity,
        locality: normalizedLocality,
        stateSlug: stateSlugFromUrl, // Pass state slug from URL
        citySlug: citySlugFromUrl, // Pass city slug from URL
      });
    } else {
      // Otherwise, load data based on the current view type
      const fetchInitialData = async () => {
        try {
          // Determine which view type to use - default to products
          const initialViewType = viewType || "products";
          console.log(`Initial load: Fetching ${initialViewType}`);

          // If we're in cards view and have initial businesses, use them
          if (initialBusinesses?.length > 0 && initialViewType === "cards") {
            setBusinesses(initialBusinesses);
            setHasMore(initialBusinesses.length >= 20);
            setCurrentPage(2);
            setIsSearching(false);
            return;
          }

          // Extract state, city, pincode, and locality slugs from the URL
          const urlParts = window.location.pathname.split('/').filter(part => part.trim() !== '');

          // Find the index of the category slug
          const categoryIndex = urlParts.findIndex(part => part === category.slug);

          // Get stateSlug, citySlug, and localitySlug from URL if available
          let stateSlugFromUrl = null;
          let citySlugFromUrl = null;
          let _pincodeFromUrl = null;
          let localitySlugFromUrl = null;

          if (categoryIndex >= 0 && categoryIndex + 1 < urlParts.length) {
              stateSlugFromUrl = urlParts[categoryIndex + 1];
          }

          if (categoryIndex >= 0 && categoryIndex + 2 < urlParts.length) {
              citySlugFromUrl = urlParts[categoryIndex + 2];
          }

          // Check for pincode (needed to extract locality slug)
          if (categoryIndex >= 0 && categoryIndex + 3 < urlParts.length) {
              _pincodeFromUrl = urlParts[categoryIndex + 3];
          }

          // Check for locality slug
          if (categoryIndex >= 0 && categoryIndex + 4 < urlParts.length) {
              localitySlugFromUrl = urlParts[categoryIndex + 4];
          }

          // Otherwise, fetch data from the server
          const result = await searchCategoryCombined({
            categoryName: category.name,
            viewType: initialViewType,
            state: locationInfo?.state || null, // Include state from locationInfo
            city: locationInfo?.city || null, // Include city from locationInfo
            pincode: locationInfo?.pincode || null, // Include pincode from locationInfo
            locality: locationInfo?.locality || null, // Include locality from locationInfo
            stateSlug: stateSlugFromUrl, // Pass state slug from URL
            citySlug: citySlugFromUrl, // Pass city slug from URL
            localitySlug: localitySlugFromUrl, // Pass locality slug from URL
            page: 1,
            limit: initialViewType === "products"
              ? CATEGORY_PRODUCTS_PER_PAGE
              : 20,
            productSort: mapProductSortToBackend(productSortBy),
            productType: productFilterBy === "all" ? null : productFilterBy,
          });

          if (result.data) {
            setSearchResult(result.data);
            setIsAuthenticated(result.data.isAuthenticated);

            if (initialViewType === "products" && result.data.products) {
              setProducts(result.data.products);
            } else if (initialViewType === "cards" && result.data.businesses) {
              setBusinesses(result.data.businesses);
            }

            // Use the hasMore flag from the server
            setHasMore(result.data.hasMore);
            setTotalCount(result.data.totalCount);
            setCurrentPage(result.data.nextPage ? 2 : 1);
          } else {
            setSearchError(result.error || "Failed to fetch results.");
          }
        } catch (error) {
          console.error("Unexpected error loading initial data:", error);
          setSearchError("An unexpected error occurred. Please try again.");
        } finally {
          setIsSearching(false);
        }
      };

      fetchInitialData();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Create the context value
  const contextValue: CategoryContextType = {
    category,
    viewType,
    sortBy,
    isSearching,
    isPending,
    isLoadingMore,
    searchError,
    productFilterBy,
    productSortBy,
    searchResult,
    businesses,
    products,
    currentPage,
    hasMore,
    totalCount,
    isAuthenticated,
    locationInfo,
    performSearch,
    handleViewChange,
    handleBusinessSortChange,
    handleBusinessSearch,
    handleProductSearch,
    handleProductSortChange,
    handleProductFilterChange,
    loadMore,
  };

  return (
    <CategoryContext.Provider value={contextValue}>
      {children}
    </CategoryContext.Provider>
  );
}
