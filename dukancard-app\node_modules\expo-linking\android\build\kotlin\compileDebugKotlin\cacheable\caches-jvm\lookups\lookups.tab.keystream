  Activity android.app  intent android.app.Activity  Context android.content  Intent android.content  data android.content.Intent  Uri android.net  toString android.net.Uri  Bundle 
android.os  bundleOf androidx.core.os  Package expo.modules.core.interfaces  ReactActivityLifecycleListener expo.modules.core.interfaces  FunctionBuilder expo.modules.kotlin.functions  SyncFunctionComponent expo.modules.kotlin.functions  Module expo.modules.kotlin.modules  ModuleDefinition expo.modules.kotlin.modules  ModuleDefinitionBuilder expo.modules.kotlin.modules  ModuleDefinitionData expo.modules.kotlin.modules  Name ;expo.modules.kotlin.modules.InternalModuleDefinitionBuilder  	sendEvent "expo.modules.kotlin.modules.Module  Events 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  Function 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  Name 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  OnStartObserving 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  OnStopObserving 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  
WeakReference 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  bundleOf 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  
initialURL 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  onURLReceivedObserver 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  onURLReceivedObservers 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  remove 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  to 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  Events 3expo.modules.kotlin.objects.ObjectDefinitionBuilder  Function 3expo.modules.kotlin.objects.ObjectDefinitionBuilder  FunctionWithoutArgs 3expo.modules.kotlin.objects.ObjectDefinitionBuilder  OnStartObserving 3expo.modules.kotlin.objects.ObjectDefinitionBuilder  OnStopObserving 3expo.modules.kotlin.objects.ObjectDefinitionBuilder  Activity expo.modules.linking  Boolean expo.modules.linking  Bundle expo.modules.linking  Context expo.modules.linking  ExpoLinkingModule expo.modules.linking  ExpoLinkingPackage expo.modules.linking  Intent expo.modules.linking  %LinkingReactActivityLifecycleListener expo.modules.linking  List expo.modules.linking  Module expo.modules.linking  
MutableSet expo.modules.linking  Package expo.modules.linking  ReactActivityLifecycleListener expo.modules.linking  Unit expo.modules.linking  Uri expo.modules.linking  
WeakReference expo.modules.linking  bundleOf expo.modules.linking  forEach expo.modules.linking  
initialURL expo.modules.linking  listOf expo.modules.linking  mutableSetOf expo.modules.linking  onURLReceivedObserver expo.modules.linking  onURLReceivedObservers expo.modules.linking  remove expo.modules.linking  to expo.modules.linking  	Companion &expo.modules.linking.ExpoLinkingModule  ModuleDefinition &expo.modules.linking.ExpoLinkingModule  
MutableSet &expo.modules.linking.ExpoLinkingModule  Unit &expo.modules.linking.ExpoLinkingModule  Uri &expo.modules.linking.ExpoLinkingModule  
WeakReference &expo.modules.linking.ExpoLinkingModule  bundleOf &expo.modules.linking.ExpoLinkingModule  
initialURL &expo.modules.linking.ExpoLinkingModule  mutableSetOf &expo.modules.linking.ExpoLinkingModule  onURLReceivedObserver &expo.modules.linking.ExpoLinkingModule  onURLReceivedObservers &expo.modules.linking.ExpoLinkingModule  remove &expo.modules.linking.ExpoLinkingModule  	sendEvent &expo.modules.linking.ExpoLinkingModule  to &expo.modules.linking.ExpoLinkingModule  ModuleDefinition 0expo.modules.linking.ExpoLinkingModule.Companion  
WeakReference 0expo.modules.linking.ExpoLinkingModule.Companion  bundleOf 0expo.modules.linking.ExpoLinkingModule.Companion  
initialURL 0expo.modules.linking.ExpoLinkingModule.Companion  mutableSetOf 0expo.modules.linking.ExpoLinkingModule.Companion  onURLReceivedObserver 0expo.modules.linking.ExpoLinkingModule.Companion  onURLReceivedObservers 0expo.modules.linking.ExpoLinkingModule.Companion  remove 0expo.modules.linking.ExpoLinkingModule.Companion  to 0expo.modules.linking.ExpoLinkingModule.Companion  %LinkingReactActivityLifecycleListener 'expo.modules.linking.ExpoLinkingPackage  listOf 'expo.modules.linking.ExpoLinkingPackage  ExpoLinkingModule :expo.modules.linking.LinkingReactActivityLifecycleListener  onReceiveURL :expo.modules.linking.LinkingReactActivityLifecycleListener  
WeakReference 
java.lang.ref  get java.lang.ref.WeakReference  	Function0 kotlin  	Function1 kotlin  Pair kotlin  to kotlin  invoke kotlin.Function1  to 
kotlin.String  List kotlin.collections  
MutableSet kotlin.collections  forEach kotlin.collections  listOf kotlin.collections  mutableSetOf kotlin.collections  remove kotlin.collections  add kotlin.collections.MutableSet  remove kotlin.collections.MutableSet  forEach kotlin.sequences  forEach kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               