'use server';

import { createClient } from '@/utils/supabase/server';
import { revalidatePath } from 'next/cache';
import { PostFormData } from '@/lib/types/posts';
import { ActionResponse } from '@/lib/types/api';
import { deletePostMedia } from '@/lib/actions/shared/upload-post-media';

/**
 * Create a new post
 */
export async function createPost(formData: PostFormData): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: 'Authentication required',
      error: 'You must be logged in to create a post'
    };
  }

  // Get the user's business profile
  const { data: businessProfile, error: profileError } = await supabase
    .from('business_profiles')
    .select('id, city_slug, state_slug, locality_slug, pincode')
    .eq('id', user.id)
    .single();

  if (profileError || !businessProfile) {
    return {
      success: false,
      message: 'Business profile not found',
      error: 'You must have a business profile to create a post'
    };
  }

  // Prepare post data
  const postData = {
    business_id: user.id,
    content: formData.content,
    image_url: formData.image_url || null,
    city_slug: businessProfile.city_slug,
    state_slug: businessProfile.state_slug,
    locality_slug: businessProfile.locality_slug,
    pincode: businessProfile.pincode,
    product_ids: formData.product_ids || [],
    mentioned_business_ids: formData.mentioned_business_ids || []
  };

  // Insert the post
  const { data, error } = await supabase
    .from('business_posts')
    .insert(postData)
    .select()
    .single();

  if (error) {
    console.error('Error creating post:', error);
    return {
      success: false,
      message: 'Failed to create post',
      error: error.message
    };
  }

  // Revalidate the feed pages
  revalidatePath('/dashboard/business/feed');
  revalidatePath('/dashboard/customer/feed');

  return {
    success: true,
    message: 'Post created successfully',
    data
  };
}

/**
 * Update only the content of an existing post (for inline editing)
 */
export async function updatePostContent(postId: string, content: string): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: 'Authentication required',
      error: 'You must be logged in to update a post'
    };
  }

  // Check if the post exists and belongs to the user
  const { data: existingPost, error: postError } = await supabase
    .from('business_posts')
    .select('id')
    .eq('id', postId)
    .eq('business_id', user.id)
    .single();

  if (postError || !existingPost) {
    return {
      success: false,
      message: 'Post not found',
      error: 'The post does not exist or you do not have permission to update it'
    };
  }

  // Update only the content and timestamp
  const { data, error } = await supabase
    .from('business_posts')
    .update({
      content: content.trim(),
      updated_at: new Date().toISOString()
    })
    .eq('id', postId)
    .select()
    .single();

  if (error) {
    console.error('Error updating post content:', error);
    return {
      success: false,
      message: 'Failed to update post',
      error: error.message
    };
  }

  // Revalidate the feed pages
  revalidatePath('/dashboard/business/feed');
  revalidatePath('/dashboard/customer/feed');
  revalidatePath('/dashboard/business/posts');

  return {
    success: true,
    message: 'Post updated successfully',
    data
  };
}

/**
 * Update only the product_ids of an existing post (for inline editing)
 */
export async function updatePostProducts(postId: string, productIds: string[]): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: 'Authentication required',
      error: 'You must be logged in to update a post'
    };
  }

  // Check if the post exists and belongs to the user
  const { data: existingPost, error: postError } = await supabase
    .from('business_posts')
    .select('id')
    .eq('id', postId)
    .eq('business_id', user.id)
    .single();

  if (postError || !existingPost) {
    return {
      success: false,
      message: 'Post not found',
      error: 'The post does not exist or you do not have permission to update it'
    };
  }

  // Update only the product_ids and timestamp
  const { data, error } = await supabase
    .from('business_posts')
    .update({
      product_ids: productIds,
      updated_at: new Date().toISOString()
    })
    .eq('id', postId)
    .select()
    .single();

  if (error) {
    console.error('Error updating post products:', error);
    return {
      success: false,
      message: 'Failed to update post products',
      error: error.message
    };
  }

  // Revalidate the feed pages
  revalidatePath('/dashboard/business/feed');
  revalidatePath('/dashboard/customer/feed');
  revalidatePath('/dashboard/business/posts');

  return {
    success: true,
    message: 'Post products updated successfully',
    data
  };
}

/**
 * Update an existing post (full update for form submissions)
 */
export async function updatePost(postId: string, formData: PostFormData): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: 'Authentication required',
      error: 'You must be logged in to update a post'
    };
  }

  // Check if the post exists and belongs to the user
  const { data: existingPost, error: postError } = await supabase
    .from('business_posts')
    .select('id')
    .eq('id', postId)
    .eq('business_id', user.id)
    .single();

  if (postError || !existingPost) {
    return {
      success: false,
      message: 'Post not found',
      error: 'The post does not exist or you do not have permission to update it'
    };
  }

  // Prepare update data
  const updateData = {
    content: formData.content,
    image_url: formData.image_url || null,
    product_ids: formData.product_ids || [],
    mentioned_business_ids: formData.mentioned_business_ids || [],
    updated_at: new Date().toISOString()
  };

  // Update the post
  const { data, error } = await supabase
    .from('business_posts')
    .update(updateData)
    .eq('id', postId)
    .select()
    .single();

  if (error) {
    console.error('Error updating post:', error);
    return {
      success: false,
      message: 'Failed to update post',
      error: error.message
    };
  }

  // Revalidate the feed pages
  revalidatePath('/dashboard/business/feed');
  revalidatePath('/dashboard/customer/feed');
  revalidatePath('/dashboard/business/posts');

  return {
    success: true,
    message: 'Post updated successfully',
    data
  };
}

/**
 * Delete a post
 */
export async function deletePost(postId: string): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: 'Authentication required',
      error: 'You must be logged in to delete a post'
    };
  }

  // Check if the post exists and belongs to the user, get creation date for media deletion
  const { data: existingPost, error: postError } = await supabase
    .from('business_posts')
    .select('id, created_at, image_url')
    .eq('id', postId)
    .eq('business_id', user.id)
    .single();

  if (postError || !existingPost) {
    return {
      success: false,
      message: 'Post not found',
      error: 'The post does not exist or you do not have permission to delete it'
    };
  }

  // Always attempt to delete the post folder from storage
  // This ensures we clean up any files that might exist, regardless of image_url status
  try {
    const mediaDeleteResult = await deletePostMedia(user.id, postId, existingPost.created_at);
    if (!mediaDeleteResult.success && mediaDeleteResult.error) {
      console.error('Error deleting post media:', mediaDeleteResult.error);
      // Continue with post deletion even if media deletion fails
    }
  } catch (mediaError) {
    console.error('Error deleting post media:', mediaError);
    // Continue with post deletion even if media deletion fails
  }

  // Delete the post
  const { error } = await supabase
    .from('business_posts')
    .delete()
    .eq('id', postId);

  if (error) {
    console.error('Error deleting post:', error);
    return {
      success: false,
      message: 'Failed to delete post',
      error: error.message
    };
  }

  // Revalidate the feed pages
  revalidatePath('/dashboard/business/feed');
  revalidatePath('/dashboard/customer/feed');
  revalidatePath('/dashboard/business/posts');

  return {
    success: true,
    message: 'Post deleted successfully'
  };
}
