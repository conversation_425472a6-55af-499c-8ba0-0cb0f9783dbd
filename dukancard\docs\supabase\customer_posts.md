# Customer Posts Table Documentation

## Table Overview

The `customer_posts` table in the Dukancard application stores posts created by customers. It enables community engagement features where customers can share experiences, recommendations, reviews, and local insights with other community members. This complements the `business_posts` table by allowing user-generated content from the customer perspective.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key for the post record |
| customer_id | uuid | NO | | Foreign key to customer_profiles.id, the customer who created the post |
| content | text | NO | | The text content of the post (max 500 characters) |
| image_url | text | YES | | URL to an image associated with the post |
| created_at | timestamptz | NO | now() | Timestamp when the post was created |
| updated_at | timestamptz | NO | now() | Timestamp when the post was last updated |
| city_slug | text | YES | | Slug version of the city name (for location filtering) |
| state_slug | text | YES | | Slug version of the state name (for location filtering) |
| locality_slug | text | YES | | Slug version of the locality name (for location filtering) |
| pincode | text | YES | | Pincode/ZIP code (for location filtering) |
| post_type | text | NO | 'general' | Type of post: 'general', 'review', 'recommendation', 'event', 'question' |
| mentioned_business_ids | uuid[] | YES | '{}' | Array of business IDs mentioned in the post |
| tags | text[] | YES | '{}' | Array of tags for categorization and discovery |

## Constraints

### Primary Key
- `customer_posts_pkey` - Primary key constraint on the `id` column

### Foreign Keys
- `customer_posts_customer_id_fkey` - Foreign key constraint linking `customer_id` to `customer_profiles.id`

### Check Constraints
- Content length constraint: `char_length(content) <= 500`
- Post type constraint: `post_type IN ('general', 'review', 'recommendation', 'event', 'question')`

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| customer_posts_pkey | UNIQUE | id | Primary key index |
| idx_customer_posts_customer_id | BTREE | customer_id | Index for faster customer lookups |
| idx_customer_posts_created_at | BTREE | created_at DESC | Index for sorting by creation time |
| idx_customer_posts_city_slug | BTREE | city_slug | Index for filtering by city |
| idx_customer_posts_locality_slug | BTREE | locality_slug | Index for filtering by locality |
| idx_customer_posts_pincode | BTREE | pincode | Index for filtering by pincode |
| idx_customer_posts_post_type | BTREE | post_type, created_at DESC | Index for filtering by post type |
| idx_customer_posts_smart_feed | BTREE | locality_slug, pincode, city_slug, state_slug, created_at DESC | Composite index for smart feed algorithm |
| idx_customer_posts_mentioned_businesses | GIN | mentioned_business_ids | Index for business mentions |
| idx_customer_posts_tags | GIN | tags | Index for tag-based searches |

## Row Level Security (RLS) Policies

### SELECT Policy
- **Name**: `customer_posts_select_policy`
- **Operation**: SELECT
- **Rule**: All posts are publicly readable (`true`)

### INSERT Policy
- **Name**: `customer_posts_insert_policy`
- **Operation**: INSERT
- **Rule**: Customers can only create posts for themselves (authenticated user ID must match customer_id)

### UPDATE Policy
- **Name**: `customer_posts_update_policy`
- **Operation**: UPDATE
- **Rule**: Customers can only update their own posts (authenticated user ID must match customer_id)

### DELETE Policy
- **Name**: `customer_posts_delete_policy`
- **Operation**: DELETE
- **Rule**: Customers can only delete their own posts (authenticated user ID must match customer_id)

## Triggers

### Update Timestamp Trigger
- **Name**: `update_customer_posts_updated_at_trigger`
- **Function**: `update_customer_posts_updated_at()`
- **Purpose**: Automatically updates the `updated_at` timestamp when a post is modified

### Location Population Trigger
- **Name**: `populate_customer_post_location_trigger`
- **Function**: `populate_customer_post_location()`
- **Purpose**: Automatically populates location fields from the customer's profile if not provided

## Post Types

The `post_type` field categorizes customer posts into different types:

1. **general** - General posts, thoughts, or updates
2. **review** - Reviews of businesses or services
3. **recommendation** - Recommendations for businesses or services
4. **event** - Local events or happenings
5. **question** - Questions to the community

## Usage Notes

1. **Community Engagement**:
   - The table enables customers to become active content creators
   - Posts can mention businesses to create connections between customer content and business profiles
   - Tags allow for better content discovery and categorization

2. **Content Limitations**:
   - Post content is limited to 500 characters to keep posts concise and readable
   - Image URLs are optional, allowing text-only or image+text posts

3. **Security**:
   - RLS policies ensure customers can only create, edit, and delete their own posts
   - All posts are publicly readable to support community features

4. **Location-Based Features**:
   - Location data is automatically populated from the customer's profile
   - Supports location-based filtering for local community features
   - Enables location-specific feeds and recommendations

5. **Business Integration**:
   - Posts can mention multiple businesses via the `mentioned_business_ids` array
   - This creates valuable connections between customer content and business profiles
   - Businesses can potentially see posts that mention them

6. **Performance Considerations**:
   - Indexes on customer_id, created_at, and location fields support efficient filtering and sorting
   - GIN indexes on arrays (mentioned_business_ids, tags) enable fast array-based queries
   - Smart feed algorithm uses composite indexes for optimal performance

7. **Content Discovery**:
   - Tags enable content categorization and discovery
   - Post types allow filtering by content category
   - Location-based filtering enables local community features

## Related Tables

### customer_profiles
The `customer_profiles` table is referenced by the `customer_id` foreign key and contains information about the customer who created the post.

### business_profiles (via mentioned_business_ids)
Business profiles can be mentioned in customer posts, creating connections between customer content and businesses.

## Future Enhancements

The table design supports future features like:
- Post reactions/likes (via related tables)
- Comments on posts (via related tables)
- Post sharing and reposting features
- Enhanced media support (multiple images, videos, etc.)
- Post analytics and engagement metrics
- Moderation and reporting features
