"use server";

import { cancelSubscription as razorpayCancelSubscription } from "@/lib/razorpay/services/subscription";
import { ActionResponse } from "../types";
import {
  getUserAndProfile,
  revalidateSubscriptionPaths,
  createErrorResponse,
  createSuccessResponse
} from "../utils";
import {
  SUBSCRIPTION_STATUS
} from "@/lib/razorpay/webhooks/handlers/subscription-constants";

/**
 * Cancel a subscription
 * @param immediate Whether to cancel immediately
 * @returns The cancellation result
 */
export async function cancelSubscription(immediate: boolean = false): Promise<ActionResponse> {
  // Get user and profile
  const { user, profile, error } = await getUserAndProfile("has_active_subscription");

  if (error) {
    return createErrorResponse(error);
  }

  // Get subscription from payment_subscriptions table
  const supabase = await import("@/utils/supabase/admin").then(mod => mod.createAdminClient());

  // First check for active subscriptions using centralized constants
  const { data: activeSubscription, error: activeSubscriptionError } = await supabase
    .from("payment_subscriptions")
    .select("razorpay_subscription_id, subscription_status")
    .eq("business_profile_id", user?.id || "")
    .eq("subscription_status", SUBSCRIPTION_STATUS.ACTIVE)
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (activeSubscriptionError) {
    console.error("Error fetching active subscription:", activeSubscriptionError);
    return createErrorResponse("Error fetching subscription details");
  }

  // If no active subscription, check for authenticated subscriptions
  let subscription = activeSubscription;

  if (!subscription) {
    const { data: authSubscription, error: authSubscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("razorpay_subscription_id, subscription_status")
      .eq("business_profile_id", user?.id || "")
      .eq("subscription_status", SUBSCRIPTION_STATUS.AUTHENTICATED)
      .order("created_at", { ascending: false })
      .limit(1)
      .maybeSingle();

    if (authSubscriptionError) {
      console.error("Error fetching authenticated subscription:", authSubscriptionError);
      return createErrorResponse("Error fetching subscription details");
    }

    subscription = authSubscription;
  }

  // If profile is null or user doesn't have a subscription, return error
  if (!profile || !subscription?.razorpay_subscription_id) {
    return createErrorResponse("User does not have an active or authenticated subscription");
  }

  // Check if this is an authenticated subscription first using centralized constants
  if (subscription.subscription_status === SUBSCRIPTION_STATUS.AUTHENTICATED) {
    console.log("[SUBSCRIPTION_DEBUG] Handling cancellation for authenticated subscription using pause endpoint");

    // For authenticated subscriptions, we'll use the pause endpoint which will cancel it
    // According to Razorpay docs: "If you pause a Subscription in the authenticated state, it goes to the cancelled state"
    const { pauseSubscription } = await import("@/lib/razorpay/services/subscription");
    const pauseResult = await pauseSubscription(subscription.razorpay_subscription_id, "now", true);

    if (!pauseResult.success) {
      console.error("[SUBSCRIPTION_DEBUG] Error pausing authenticated subscription:", pauseResult.error);

      // Even if the API call fails, we'll still update our database
      console.log("[SUBSCRIPTION_DEBUG] Proceeding with database update despite API failure");
    } else {
      console.log("[SUBSCRIPTION_DEBUG] Successfully paused/cancelled authenticated subscription");
    }

    // Only record that a cancellation was requested, but don't change the status
    // Let the webhook handler do all the status changes when it receives the official event from Razorpay
    const { error: updateError } = await supabase
      .from("payment_subscriptions")
      .update({
        cancellation_requested_at: new Date().toISOString()
      })
      .eq("razorpay_subscription_id", subscription.razorpay_subscription_id);

    if (updateError) {
      console.error("Error updating subscription status:", updateError);
      return createErrorResponse("Failed to update subscription status");
    }

    // FIXED: Don't immediately revoke access for authenticated subscription cancellations
    // The webhook handler will update has_active_subscription when Razorpay confirms the cancellation
    // This prevents premature access revocation and maintains consistency with webhook-driven flow
    console.log("[SUBSCRIPTION_CANCEL] Skipping immediate has_active_subscription update - will be handled by webhook");

    // Revalidate paths
    revalidateSubscriptionPaths();

    return createSuccessResponse({
      message: "Your future subscription has been cancelled successfully."
    });
  }

  // For active subscriptions, proceed with Razorpay API call
  const result = await razorpayCancelSubscription(
    subscription.razorpay_subscription_id,
    !immediate // true means cancel at cycle end, false means cancel immediately
  );

  if (!result.success) {

    // Check for specific error about no billing cycle
    if (typeof result.error === 'object' &&
        result.error !== null &&
        'error' in result.error &&
        typeof result.error.error === 'object' &&
        result.error.error !== null &&
        'description' in result.error.error &&
        result.error.error.description === 'Subscription cannot be cancelled since no billing cycle is going on') {

      console.log("[SUBSCRIPTION_DEBUG] Cannot cancel subscription - no billing cycle is going on.");

      // For other cases, return a more user-friendly error
      return createErrorResponse(
        "This subscription cannot be cancelled because no billing cycle is in progress. " +
        "If you're on a trial, your subscription will automatically expire at the end of the trial period."
      );
    }

    // For other errors, return the error message
    return createErrorResponse(
      typeof result.error === 'string'
        ? result.error
        : result.error
          ? JSON.stringify(result.error)
          : "Failed to cancel subscription"
    );
  }

  // Only record that a cancellation was requested, but don't change the status
  // Let the webhook handler do all the status changes when it receives the official event from Razorpay
  const { error: updateError } = await supabase
    .from("payment_subscriptions")
    .update({
      cancellation_requested_at: new Date().toISOString()
    })
    .eq("razorpay_subscription_id", subscription.razorpay_subscription_id);

  if (updateError) {
    console.error("Error updating subscription status:", updateError);
    // Continue anyway - the webhook handler will update the status
  }

  // Revalidate paths
  revalidateSubscriptionPaths();

  return createSuccessResponse({
    message: immediate
      ? "Your subscription has been cancelled immediately."
      : "Your subscription will be cancelled at the end of the current billing cycle."
  });
}
