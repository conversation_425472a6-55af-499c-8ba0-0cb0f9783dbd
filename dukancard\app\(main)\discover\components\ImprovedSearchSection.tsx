"use client";

import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useDiscoverContext } from "../context/DiscoverContext";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  combinedSearchSchema,
  CombinedSearchFormData,
} from "@/lib/schemas/locationSchemas";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  MapPin,
  Loader2,
  Building2,
  Search,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList
} from "@/components/ui/command";
import { getPincodeDetails } from "@/backend/supabase/services/location/location";
import { getCitySuggestionsClient, getPincodeDetailsClient } from "@/lib/client/locationUtils";
import CitySearchSkeleton from "./CitySearchSkeleton";

interface ImprovedSearchSectionProps {
  initialValues: {
    pincode?: string | null;
    city?: string | null;
    locality?: string | null;
  };
}

export default function ImprovedSearchSection({ initialValues }: ImprovedSearchSectionProps) {
  const { performSearch, isSearching } = useDiscoverContext();
  const [isPincodeLoading, setIsPincodeLoading] = useState(false);
  const [isLoadingCities, setIsLoadingCities] = useState(false);
  const [searchType, setSearchType] = useState<"pincode" | "city">(
    initialValues.city ? "city" : "pincode"
  );
  const [localities, setLocalities] = useState<string[]>([]);
  const [citySuggestions, setCitySuggestions] = useState<string[]>([]);
  const [showCitySuggestions, setShowCitySuggestions] = useState(false);

  // Form for combined search - initialize with only the relevant fields based on search type
  const form = useForm<CombinedSearchFormData>({
    resolver: zodResolver(combinedSearchSchema),
    defaultValues: {
      businessName: "", // Keep this for schema compatibility
      pincode: searchType === "pincode" ? initialValues.pincode || "" : "",
      city: searchType === "city" ? initialValues.city || "" : "",
      locality: searchType === "pincode" ? initialValues.locality || "_any" : "_any",
    },
    mode: "onChange",
  });

  // Handle form submission
  const onSubmit = (data: CombinedSearchFormData) => {
    console.log("Form submitted with data:", data, "Search type:", searchType);

    // Set loading state to show skeleton UI
    setIsLoadingCities(false); // Hide city suggestions skeleton
    setShowCitySuggestions(false); // Hide city suggestions

    // We don't need to set isSearching here as it's handled by the performSearch function
    // in the context, which will set isSearching to true

    // Create a new data object with only the relevant fields based on search type
    const searchData: CombinedSearchFormData = {
      businessName: data.businessName,
    };

    if (searchType === "pincode") {
      // For pincode search, include pincode and locality
      searchData.pincode = data.pincode;
      searchData.locality = data.locality;
      searchData.city = ""; // Explicitly set city to empty string
      console.log("Searching by pincode:", searchData.pincode);
    } else {
      // For city search, include only city
      searchData.city = data.city;
      searchData.pincode = ""; // Explicitly set pincode to empty string
      searchData.locality = ""; // Explicitly set locality to empty string
      console.log("Searching by city:", searchData.city);
    }

    console.log("Final search data:", searchData);

    // Add a small delay to ensure the form submission is processed
    setTimeout(() => {
      performSearch(searchData);
    }, 100);
  };

  // Toggle search type between pincode and city
  const toggleSearchType = () => {
    // First determine the new search type
    const newSearchType = searchType === "pincode" ? "city" : "pincode";
    setSearchType(newSearchType);

    // Reset form with only the relevant fields for the new search type
    form.reset({
      businessName: "",
      // If switching to pincode, keep pincode value, otherwise clear it
      pincode: newSearchType === "pincode" ? form.getValues("pincode") : "",
      // If switching to city, keep city value, otherwise clear it
      city: newSearchType === "city" ? form.getValues("city") : "",
      // Only set locality for pincode search
      locality: newSearchType === "pincode" ? "_any" : "",
    });

    // Clear UI state
    setLocalities([]);
    setCitySuggestions([]);
    setShowCitySuggestions(false);

    console.log("Toggled search type to:", newSearchType, "Form values:", form.getValues());
  };

  // Function to fetch localities for a pincode - wrapped in useCallback to prevent unnecessary re-renders
  const fetchLocalitiesForPincode = useCallback(async (pincode: string) => {
    if (!pincode || pincode.length !== 6) return;

    console.log("Fetching localities for pincode:", pincode);
    setIsPincodeLoading(true);

    try {
      // Try client-side function first for better performance
      const result = await getPincodeDetailsClient(pincode);
      if (result.localities) {
        // Extract unique localities
        const uniqueLocalities = result.localities.map(locality =>
          locality.replace(" B.O", "").trim()
        ).filter(Boolean);

        console.log("Found localities:", uniqueLocalities);
        setLocalities(uniqueLocalities);

        // If there's only one locality, auto-select it
        if (uniqueLocalities.length === 1) {
          form.setValue("locality", uniqueLocalities[0]);
        } else if (initialValues.locality && uniqueLocalities.includes(initialValues.locality)) {
          // If we have an initial locality value and it's in the list, select it
          form.setValue("locality", initialValues.locality);
        }
      } else {
        // Fall back to server action if client-side fails
        const serverResult = await getPincodeDetails(pincode);
        if (serverResult.data?.localities) {
          // Extract unique localities
          const uniqueLocalities = serverResult.data.localities
            .map(locality => locality.replace(" B.O", "").trim())
            .filter(Boolean);

          console.log("Found localities (server):", uniqueLocalities);
          setLocalities(uniqueLocalities);

          // If there's only one locality, auto-select it
          if (uniqueLocalities.length === 1) {
            form.setValue("locality", uniqueLocalities[0]);
          } else if (initialValues.locality && uniqueLocalities.includes(initialValues.locality)) {
            // If we have an initial locality value and it's in the list, select it
            form.setValue("locality", initialValues.locality);
          }
        } else {
          setLocalities([]);
          form.setValue("locality", "_any");
        }
      }
    } catch (error) {
      console.error("Error fetching localities:", error);
      setLocalities([]);
    } finally {
      setIsPincodeLoading(false);
    }
  }, [form, initialValues.locality, setIsPincodeLoading, setLocalities]);

  // Watch for pincode changes
  const pincodeValue = form.watch("pincode");

  // Fetch localities when pincode changes
  useEffect(() => {
    if (pincodeValue && pincodeValue.length === 6 && searchType === "pincode") {
      fetchLocalitiesForPincode(pincodeValue);
    } else {
      setLocalities([]);
    }
  }, [searchType, pincodeValue, fetchLocalitiesForPincode]);

  // Fetch localities when component mounts with a pincode parameter
  useEffect(() => {
    // Only run on initial mount
    if (initialValues.pincode && initialValues.pincode.length === 6 && searchType === "pincode") {
      console.log("Initial load with pincode:", initialValues.pincode);
      fetchLocalitiesForPincode(initialValues.pincode);
    }
  }, [initialValues.pincode, searchType, fetchLocalitiesForPincode]);

  // Watch for city input changes
  const cityValue = form.watch("city");

  // Fetch city suggestions when city input changes
  useEffect(() => {
    console.log("City input changed:", cityValue, "Search type:", searchType);

    if (cityValue && cityValue.length >= 2 && searchType === "city") {
      // Set loading state immediately to show skeleton
      setIsLoadingCities(true);
      console.log("Loading cities...");

      const fetchCitySuggestions = async () => {
        try {
          // Use client-side function for better performance - direct Supabase query
          console.log("Fetching city suggestions for:", cityValue);
          const result = await getCitySuggestionsClient(cityValue);
          console.log("City suggestions result:", result);

          if (result.cities) {
            console.log("Setting city suggestions:", result.cities);
            setCitySuggestions(result.cities);
            setShowCitySuggestions(true);
          } else {
            console.log("No city suggestions found");
            setCitySuggestions([]);
            setShowCitySuggestions(false);
          }
        } catch (error) {
          console.error("Error fetching city suggestions:", error);
          setCitySuggestions([]);
          setShowCitySuggestions(false);
        } finally {
          setIsLoadingCities(false);
        }
      };

      // Add a small delay to prevent too many requests while typing
      const timer = setTimeout(() => {
        fetchCitySuggestions();
      }, 300);

      return () => clearTimeout(timer);
    } else {
      setCitySuggestions([]);
      setShowCitySuggestions(false);
      setIsLoadingCities(false);
    }
  }, [searchType, cityValue]);

  // Handle city suggestion selection
  const handleCitySelect = (city: string) => {
    form.setValue("city", city);
    setShowCitySuggestions(false);
  };

  return (
    <div className="w-full py-6 mt-20">
      <div className="container mx-auto px-4">
        {/* Heading */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="text-center mb-6"
        >
          <h1 className="text-2xl md:text-3xl font-bold text-neutral-800 dark:text-neutral-100 mb-2">
            Discover Businesses and Products/Services Across India
          </h1>
          <p className="text-sm md:text-base text-neutral-600 dark:text-neutral-400">
            Search through our extensive database of local businesses and products/services
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="max-w-5xl mx-auto"
        >
          <form onSubmit={(e) => {
            e.preventDefault();
            console.log("Form submitted directly");

            // Get form data
            const formData = form.getValues();

            // Create a clean search data object based on search type
            const searchData: CombinedSearchFormData = {
              businessName: formData.businessName,
            };

            if (searchType === "pincode") {
              searchData.pincode = formData.pincode;
              searchData.locality = formData.locality;
            } else {
              searchData.city = formData.city;
            }

            onSubmit(searchData);
          }}>
            <div className="flex flex-col md:flex-row gap-3 items-center w-full">
              {/* Search type selector */}
              <div className="w-full md:w-auto">
                <Select
                  value={searchType}
                  onValueChange={(value) => {
                    setSearchType(value as "pincode" | "city");
                    toggleSearchType();
                  }}
                >
                <SelectTrigger className="w-full md:w-[140px] h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md px-4 flex items-center text-base leading-normal">
                  <SelectValue placeholder="Search by" className="text-base leading-normal" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pincode">
                    <div className="flex items-center">
                      <MapPin className="mr-2 h-4 w-4 text-[var(--brand-gold)]" />
                      <span>Pincode</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="city">
                    <div className="flex items-center">
                      <Building2 className="mr-2 h-4 w-4 text-[var(--brand-gold)]" />
                      <span>City</span>
                    </div>
                  </SelectItem>
                </SelectContent>
                </Select>
              </div>

              {/* Dynamic search inputs based on search type */}
              <div className="flex-1 w-full flex flex-col md:flex-row gap-3 items-center">
                {searchType === "pincode" ? (
                  <>
                    {/* Pincode input */}
                    <div className="relative w-full flex-1">
                      <Controller
                        name="pincode"
                        control={form.control}
                        render={({ field }) => (
                          <div className="relative">
                            <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-neutral-400" />
                            <Input
                              {...field}
                              placeholder="Enter 6-digit pincode"
                              className="pl-12 h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md text-base"
                              maxLength={6}
                              value={field.value || ""}
                              type="tel"
                              inputMode="numeric"
                              onKeyDown={(e) => {
                                // Allow only numbers, backspace, delete, tab, arrow keys, and enter
                                if (
                                  !/^\d$/.test(e.key) && // Allow digits
                                  e.key !== 'Backspace' &&
                                  e.key !== 'Delete' &&
                                  e.key !== 'Tab' &&
                                  e.key !== 'Enter' &&
                                  !e.key.includes('Arrow')
                                ) {
                                  e.preventDefault();
                                }
                              }}
                            />
                            {isPincodeLoading && (
                              <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-neutral-400" />
                            )}
                          </div>
                        )}
                      />
                    </div>

                    {/* Locality select */}
                    <div className="w-full md:w-[200px]">
                      <Controller
                        name="locality"
                        control={form.control}
                        render={({ field }) => (
                          <Select
                            value={field.value || "_any"}
                            onValueChange={field.onChange}
                            disabled={localities.length === 0 && !isPincodeLoading}
                          >
                            <SelectTrigger className="w-full h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md px-4 flex items-center text-base leading-normal">
                              <SelectValue placeholder="Select locality" className="text-base leading-normal" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="_any">Any Locality</SelectItem>
                              {localities.map((locality) => (
                                <SelectItem key={locality} value={locality}>
                                  {locality}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                  </>
                ) : (
                  <>
                    {/* City input with suggestions */}
                    <div className="relative w-full flex-1">
                      <Controller
                        name="city"
                        control={form.control}
                        render={({ field }) => (
                          <div className="relative">
                            <Building2 className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-neutral-400" />
                            <Input
                              {...field}
                              placeholder="Enter city name"
                              className="pl-12 h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md text-base"
                              autoComplete="off"
                              value={field.value || ""}
                              onChange={(e) => {
                                field.onChange(e);
                                console.log("City input changed directly:", e.target.value);
                              }}
                            />
                            {isLoadingCities && (
                              <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-neutral-400" />
                            )}
                          </div>
                        )}
                      />

                      {/* City suggestions */}
                      <AnimatePresence>
                        {isLoadingCities && form.watch("city") ? (
                          <CitySearchSkeleton />
                        ) : (
                          showCitySuggestions && citySuggestions.length > 0 && (
                            <motion.div
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -10 }}
                              className="absolute z-10 mt-1 w-full bg-white dark:bg-neutral-900 rounded-md border border-neutral-200 dark:border-neutral-800 shadow-lg"
                            >
                              <Command>
                                <CommandList>
                                  <CommandGroup>
                                    {citySuggestions.map((city) => (
                                      <CommandItem
                                        key={city}
                                        onSelect={() => handleCitySelect(city)}
                                        className="cursor-pointer"
                                      >
                                        <Building2 className="mr-2 h-4 w-4 text-neutral-400" />
                                        <span>{city}</span>
                                      </CommandItem>
                                    ))}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </motion.div>
                          )
                        )}
                      </AnimatePresence>
                    </div>
                  </>
                )}

                {/* Search button */}
                <Button
                  type="submit"
                  disabled={isSearching || (searchType === "pincode" ? !form.getValues("pincode") : !form.getValues("city"))}
                  className="h-12 min-h-[48px] bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-[var(--brand-gold-foreground)] w-full md:w-auto px-6 border border-[var(--brand-gold)] rounded-md flex items-center justify-center font-medium text-base"
                  onClick={() => {
                    console.log("Search button clicked, isSearching:", isSearching);
                    // This is just for debugging, the actual submission is handled by the form
                  }}
                >
                  {isSearching ? (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="mr-2"
                    >
                      <Loader2 className="h-4 w-4" />
                    </motion.div>
                  ) : (
                    <Search className="h-4 w-4 mr-2" />
                  )}
                  Search
                </Button>
              </div>
            </div>
          </form>
        </motion.div>
      </div>
    </div>
  );
}
