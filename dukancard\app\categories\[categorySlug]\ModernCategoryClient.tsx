"use client";

import { useSearchParams } from "next/navigation";
import { CategoryProvider } from "./context/CategoryContext";
import { PINCODE_PARAM, CITY_PARAM, LOCALITY_PARAM } from "@/app/(main)/discover/constants/urlParamConstants";
import { SerializableCategory } from "./context/types";
import { BusinessProfilePublicData } from "@/lib/actions/businessProfiles/types";

// Import components
import ImprovedSearchSection from "./components/ImprovedSearchSection";
import ErrorSection from "./components/ErrorSection";
import ModernResultsSection from "./components/ModernResultsSection";
import LocationIndicator from "./components/LocationIndicator";
import BreadcrumbNav from "./components/BreadcrumbNav";

export interface LocationInfo {
  state?: string | null;
  city?: string | null;
  pincode?: string | null;
  locality?: string | null;
}

interface ModernCategoryClientProps {
  category: SerializableCategory;
  initialBusinesses: BusinessProfilePublicData[];
  totalCount: number;
  locationInfo?: LocationInfo;
}

export default function ModernCategoryClient({
  category,
  initialBusinesses,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
  totalCount,
  locationInfo,
}: ModernCategoryClientProps) {
  const searchParams = useSearchParams();

  // Get initial values from URL params
  const initialPincode = searchParams.get(PINCODE_PARAM);
  const initialCity = searchParams.get(CITY_PARAM);
  const initialLocality = searchParams.get(LOCALITY_PARAM);

  return (
    <CategoryProvider
      category={category}
      initialBusinesses={initialBusinesses}
      locationInfo={locationInfo}
    >
      <div className="relative min-h-screen overflow-hidden bg-white dark:bg-black">
        {/* Breadcrumb navigation */}
        <BreadcrumbNav />

        {/* Improved search section - full width, one line for desktop/tablet */}
        <ImprovedSearchSection
          initialValues={{
            pincode: initialPincode,
            city: initialCity,
            locality: initialLocality,
          }}
        />

        {/* Error Display */}
        <div className="container mx-auto px-4 my-4">
          <ErrorSection />
        </div>

        {/* Location Indicator */}
        <LocationIndicator />

        {/* Results Section */}
        <ModernResultsSection />
      </div>
    </CategoryProvider>
  );
}
