"use client";

import { useState, useCallback } from "react";
import { toast } from "sonner";
import { uploadPostImage } from "@/lib/actions/shared/upload-post-media";
import { compressImageUltraAggressiveClient } from "@/lib/utils/client-image-compression";

export type UploadStatus = "idle" | "uploading" | "success" | "error";

export interface UsePostMediaUploadProps {
  onUploadSuccess?: (_url: string) => void;
  onUploadError?: (_error: string) => void;
  maxFileSize?: number; // in bytes, default 15MB
}

// Allowed file types for posts
const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
  'image/gif'
] as const;

const ALLOWED_FILE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.gif'] as const;

// Helper function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Helper function to validate file type
const isValidFileType = (file: File): boolean => {
  return ALLOWED_FILE_TYPES.includes(file.type as typeof ALLOWED_FILE_TYPES[number]);
};

// Helper function to validate file extension
const isValidFileExtension = (fileName: string): boolean => {
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return ALLOWED_FILE_EXTENSIONS.includes(extension as typeof ALLOWED_FILE_EXTENSIONS[number]);
};

export interface UsePostMediaUploadReturn {
  // State
  uploadStatus: UploadStatus;
  uploadError: string | null;
  previewUrl: string | null;
  isUploading: boolean;

  // Image cropping
  imageToCrop: string | null;
  originalFile: File | null;

  // Actions
  handleFileSelect: (_file: File | null) => void;
  handleUpload: (_postId: string, _file?: File, _postCreatedAt?: string) => Promise<string | null>;
  clearImage: () => void;

  // Cropping actions
  setImageToCrop: (_imageUrl: string | null) => void;
  handleCropComplete: (_croppedFile: File) => void;
  handleCropCancel: () => void;
}

export function usePostMediaUpload({
  onUploadSuccess,
  onUploadError,
  maxFileSize = 15 * 1024 * 1024, // 15MB default - matches industry standards
}: UsePostMediaUploadProps = {}): UsePostMediaUploadReturn {

  const [uploadStatus, setUploadStatus] = useState<UploadStatus>("idle");
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [imageToCrop, setImageToCrop] = useState<string | null>(null);
  const [originalFile, setOriginalFile] = useState<File | null>(null);
  const [fileToUpload, setFileToUpload] = useState<File | null>(null);

  // Handle file selection and validation
  const handleFileSelect = useCallback((file: File | null) => {
    // Clear previous state
    setUploadError(null);
    setUploadStatus("idle");
    setPreviewUrl(null);
    setImageToCrop(null);
    setOriginalFile(null);
    setFileToUpload(null);

    if (!file) return;

    // Validate file type by MIME type
    if (!isValidFileType(file)) {
      const error = "Invalid file type. Please upload JPEG, PNG, WebP, or GIF images only.";
      setUploadError(error);
      setUploadStatus("error");
      toast.error("Invalid File Type", {
        description: "Only JPEG, PNG, WebP, and GIF images are allowed."
      });
      return;
    }

    // Validate file extension as additional security
    if (!isValidFileExtension(file.name)) {
      const error = "Invalid file extension. Please upload files with .jpg, .jpeg, .png, .webp, or .gif extensions.";
      setUploadError(error);
      setUploadStatus("error");
      toast.error("Invalid File Extension", {
        description: "File extension doesn't match allowed types."
      });
      return;
    }

    // Validate file size with user-friendly message
    if (file.size > maxFileSize) {
      const maxSizeMB = Math.round(maxFileSize / (1024 * 1024));
      const fileSizeFormatted = formatFileSize(file.size);
      const error = `File size (${fileSizeFormatted}) exceeds the ${maxSizeMB}MB limit.`;
      setUploadError(error);
      setUploadStatus("error");
      toast.error("File Too Large", {
        description: `Your file is ${fileSizeFormatted}. Please choose a file smaller than ${maxSizeMB}MB.`
      });
      return;
    }

    // Store original file directly without cropping
    setOriginalFile(file);
    setFileToUpload(file);

    // Create preview URL for the original image
    const previewUrl = URL.createObjectURL(file);
    setPreviewUrl(previewUrl);
    setUploadStatus("idle");
  }, [maxFileSize]);

  // Handle crop completion
  const handleCropComplete = useCallback((croppedFile: File) => {
    setFileToUpload(croppedFile);
    setImageToCrop(null);

    // Create preview URL for the cropped image
    const previewUrl = URL.createObjectURL(croppedFile);
    setPreviewUrl(previewUrl);
    setUploadStatus("idle");
  }, []);

  // Handle crop cancellation
  const handleCropCancel = useCallback(() => {
    setImageToCrop(null);
    setOriginalFile(null);
    setUploadStatus("idle");
  }, []);

  // Handle image upload
  const handleUpload = useCallback(async (
    postId: string,
    file?: File,
    postCreatedAt?: string
  ): Promise<string | null> => {
    const uploadFile = file || fileToUpload;

    if (!uploadFile) {
      const error = "No file selected for upload.";
      setUploadError(error);
      setUploadStatus("error");
      onUploadError?.(error);
      return null;
    }

    setUploadStatus("uploading");
    setUploadError(null);

    try {
      // Compress image on client-side first
      const compressionResult = await compressImageUltraAggressiveClient(uploadFile, {
        maxDimension: 1200,
        targetSizeKB: 100
      });

      // Convert compressed blob to file
      const compressedFile = new File([compressionResult.blob], uploadFile.name, {
        type: compressionResult.blob.type
      });

      const formData = new FormData();
      formData.append("imageFile", compressedFile);

      const result = await uploadPostImage(formData, postId, postCreatedAt);

      if (result.success && result.url) {
        setUploadStatus("success");
        toast.success("Image uploaded successfully!");
        onUploadSuccess?.(result.url);
        return result.url;
      } else {
        const error = result.error || "Failed to upload image";
        setUploadError(error);
        setUploadStatus("error");
        toast.error("Upload failed", {
          description: error
        });
        onUploadError?.(error);
        return null;
      }
    } catch (error) {
      const errorMessage = "An unexpected error occurred during upload";
      console.error("Upload error:", error);
      setUploadError(errorMessage);
      setUploadStatus("error");
      toast.error("Upload failed", {
        description: errorMessage
      });
      onUploadError?.(errorMessage);
      return null;
    }
  }, [fileToUpload, onUploadSuccess, onUploadError]);

  // Clear image and reset state
  const clearImage = useCallback(() => {
    setUploadStatus("idle");
    setUploadError(null);
    setFileToUpload(null);
    setOriginalFile(null);
    setImageToCrop(null);

    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
  }, [previewUrl]);

  return {
    // State
    uploadStatus,
    uploadError,
    previewUrl,
    isUploading: uploadStatus === "uploading",

    // Image cropping
    imageToCrop,
    originalFile,

    // Actions
    handleFileSelect,
    handleUpload,
    clearImage,

    // Cropping actions
    setImageToCrop,
    handleCropComplete,
    handleCropCancel,
  };
}
