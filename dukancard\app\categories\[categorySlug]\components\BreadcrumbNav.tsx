"use client";

import { Home } from "lucide-react";
import { usePathname } from "next/navigation";
import { useCategoryContext } from "../context/CategoryContext";
import { toTitleCase } from "@/lib/utils";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

export default function BreadcrumbNav() {
  const pathname = usePathname();
  const { category, locationInfo } = useCategoryContext();

  // Parse the path to extract location information
  const pathParts = pathname.split('/').filter(Boolean);

  // Check if we're on a location-specific page
  const isLocationPage = pathParts.length > 2; // More than /categories/[categorySlug]

  // Extract location parts from the path
  const hasState = pathParts.length >= 3;
  const hasCity = pathParts.length >= 4;
  const hasPincode = pathParts.length >= 5;
  const hasLocality = pathParts.length >= 6;

  return (
    <div className="container mx-auto px-4 pt-1 pb-0">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/?view=home">
              <Home className="w-4 h-4" />
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/categories">
              Categories
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          {isLocationPage ? (
            <>
              <BreadcrumbItem>
                <BreadcrumbLink href={`/categories/${category.slug}`}>
                  {category.name}
                </BreadcrumbLink>
              </BreadcrumbItem>

              {/* State breadcrumb */}
              {hasState && locationInfo?.state && (
                <>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    {hasCity ? (
                      <BreadcrumbLink href={`/categories/${category.slug}/${pathParts[2]}`}>
                        {toTitleCase(locationInfo.state)}
                      </BreadcrumbLink>
                    ) : (
                      <BreadcrumbPage>{toTitleCase(locationInfo.state)}</BreadcrumbPage>
                    )}
                  </BreadcrumbItem>
                </>
              )}

              {/* City breadcrumb */}
              {hasCity && locationInfo?.city && (
                <>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    {hasPincode ? (
                      <BreadcrumbLink href={`/categories/${category.slug}/${pathParts[2]}/${pathParts[3]}`}>
                        {locationInfo.city}
                      </BreadcrumbLink>
                    ) : (
                      <BreadcrumbPage>{locationInfo.city}</BreadcrumbPage>
                    )}
                  </BreadcrumbItem>
                </>
              )}

              {/* Pincode breadcrumb */}
              {hasPincode && locationInfo?.pincode && (
                <>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    {hasLocality ? (
                      <BreadcrumbLink href={`/categories/${category.slug}/${pathParts[2]}/${pathParts[3]}/${pathParts[4]}`}>
                        {locationInfo.pincode}
                      </BreadcrumbLink>
                    ) : (
                      <BreadcrumbPage>{locationInfo.pincode}</BreadcrumbPage>
                    )}
                  </BreadcrumbItem>
                </>
              )}

              {/* Locality breadcrumb */}
              {hasLocality && locationInfo?.locality && (
                <>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>{locationInfo.locality}</BreadcrumbPage>
                  </BreadcrumbItem>
                </>
              )}
            </>
          ) : (
            <BreadcrumbItem>
              <BreadcrumbPage>{category.name}</BreadcrumbPage>
            </BreadcrumbItem>
          )}
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
}
