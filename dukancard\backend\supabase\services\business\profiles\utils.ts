import { BusinessSortBy } from "./types";

/**
 * Apply sorting to a Supabase query based on the provided sort option
 * Using 'any' type here is acceptable since we're working with Supabase query builder
 * which has a complex type structure that's difficult to represent precisely
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function applySorting(query: any, sortBy: BusinessSortBy): any {
  switch (sortBy) {
    case "name_asc":
      return query.order("business_name", { ascending: true });
    case "name_desc":
      return query.order("business_name", { ascending: false });
    case "created_asc":
      return query.order("created_at", { ascending: true });
    case "created_desc":
      return query.order("created_at", { ascending: false });
    case "likes_asc":
      return query.order("total_likes", { ascending: true });
    case "likes_desc":
      return query.order("total_likes", { ascending: false });
    case "subscriptions_asc":
      return query.order("total_subscriptions", { ascending: true });
    case "subscriptions_desc":
      return query.order("total_subscriptions", { ascending: false });
    case "rating_asc":
      return query.order("average_rating", { ascending: true });
    case "rating_desc":
      return query.order("average_rating", { ascending: false });
    default:
      return query.order("created_at", { ascending: false });
  }
}

/**
 * Get the current ISO timestamp
 */
export function getCurrentISOTimestamp(): string {
  return new Date().toISOString();
}

/**
 * Create a subscription map from subscription data
 */
export function createSubscriptionMap(subscriptionsData: Array<{
  business_profile_id: string;
  subscription_status: string | null;
  plan_id: string | null;
}> | null) {
  const subscriptionMap = new Map<string, {
    subscription_status: string | null;
    plan_id: string | null;
  }>();

  if (subscriptionsData) {
    // Group by business_profile_id and take the most recent one
    subscriptionsData.forEach(sub => {
      if (!subscriptionMap.has(sub.business_profile_id)) {
        subscriptionMap.set(sub.business_profile_id, {
          subscription_status: sub.subscription_status,
          plan_id: sub.plan_id
        });
      }
    });
  }

  return subscriptionMap;
}
