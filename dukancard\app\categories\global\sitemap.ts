import { MetadataRoute } from "next";
import { BUSINESS_CATEGORIES } from "@/lib/config/categories";

/**
 * Generate sitemap for global categories
 * This function returns the global categories without any location-specific entries
 */
export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";

  try {
    // Get all categories with slugs
    const categoriesWithSlugs = BUSINESS_CATEGORIES.filter(c => c.slug);
    console.log("Generating sitemap for global categories without locations");

    // Create base category entries (without location)
    const baseCategoryEntries = [
      // Categories index page
      {
        url: `${siteUrl}/categories`,
        lastModified: new Date(),
        changeFrequency: "daily" as const,
        priority: 1.0,
      },
      // Individual category pages
      ...categoriesWithSlugs.map(category => ({
        url: `${siteUrl}/categories/${category.slug}`,
        lastModified: new Date(),
        changeFrequency: "weekly" as const,
        priority: 0.8,
      }))
    ];

    console.log(`Generated sitemap with ${baseCategoryEntries.length} entries`);
    return baseCategoryEntries;
  } catch (error) {
    console.error("Error generating global category sitemap:", error);

    // Return a basic sitemap in case of error
    return [
      {
        url: `${siteUrl}/categories`,
        lastModified: new Date(),
        changeFrequency: "daily" as const,
        priority: 1.0,
      }
    ];
  }
}
