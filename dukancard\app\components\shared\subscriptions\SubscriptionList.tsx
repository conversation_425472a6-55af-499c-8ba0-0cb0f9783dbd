'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Compass } from 'lucide-react';
import SubscriptionCard, { SubscriptionData } from './SubscriptionCard';

interface SubscriptionListProps {
  initialSubscriptions: SubscriptionData[];
  onUnsubscribeSuccess?: (_subscriptionId: string) => void;
  showUnsubscribe?: boolean;
  variant?: 'default' | 'compact';
  emptyMessage?: string;
  emptyDescription?: string;
  showDiscoverButton?: boolean;
}

export default function SubscriptionList({
  initialSubscriptions,
  onUnsubscribeSuccess,
  showUnsubscribe = true,
  variant = 'default',
  emptyMessage = "No subscriptions found.",
  emptyDescription = "Subscribe to profiles to see them here.",
  showDiscoverButton = false
}: SubscriptionListProps) {
  const [subscriptions, setSubscriptions] = useState(initialSubscriptions);

  // Update subscriptions when initialSubscriptions changes
  useEffect(() => {
    setSubscriptions(initialSubscriptions);
  }, [initialSubscriptions]);

  const handleUnsubscribeSuccess = (subscriptionIdToRemove: string) => {
    setSubscriptions(currentSubscriptions =>
      currentSubscriptions.filter(sub => sub.id !== subscriptionIdToRemove)
    );

    // Call parent callback if provided
    if (onUnsubscribeSuccess) {
      onUnsubscribeSuccess(subscriptionIdToRemove);
    }
  };

  if (subscriptions.length === 0) {
    return (
      <div className="rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white/50 dark:bg-black/50 p-8 text-center">
        <p className="text-neutral-600 dark:text-neutral-400">{emptyMessage}</p>
        <p className="text-sm text-neutral-500 dark:text-neutral-500 mt-2">
          {emptyDescription}
        </p>
        {showDiscoverButton && (
          <div className="mt-6">
            <Link href="/discover" target="_blank" rel="noopener noreferrer">
              <Button
                variant="outline"
                className="inline-flex items-center gap-2 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 dark:hover:bg-blue-950/30 dark:hover:text-blue-400 dark:hover:border-blue-900/50 transition-all duration-200"
              >
                <Compass className="h-4 w-4" />
                Discover Businesses Nearby
              </Button>
            </Link>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {subscriptions.map((sub, _index) => {
        const profile = sub.profile;

        if (!profile) {
          return null; // Skip items with missing profiles
        }

        return (
          <SubscriptionCard
            key={sub.id}
            subscriptionId={sub.id}
            profile={profile}
            onUnsubscribeSuccess={showUnsubscribe ? handleUnsubscribeSuccess : undefined}
            showUnsubscribe={showUnsubscribe}
            variant={variant}
          />
        );
      })}
    </div>
  );
}
