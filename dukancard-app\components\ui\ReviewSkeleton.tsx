import React from 'react';
import { View } from 'react-native';
import { SkeletonLoader } from './SkeletonLoader';
import { createSkeletonLoaderStyles } from '@/styles/ui/SkeletonLoader-styles';
import { useColorScheme } from '@/hooks/useColorScheme';

// Single Review Card Skeleton - Matches exact ReviewCard design
export const ReviewCardSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  return (
    <View style={styles.reviewCard}>
      {/* Business Header - matches exact ReviewCard header */}
      <View style={styles.reviewHeader}>
        <View style={styles.reviewerInfo}>
          {/* Business Logo */}
          <SkeletonLoader width={40} height={40} borderRadius={20} />

          {/* Business Info */}
          <View style={styles.reviewerDetails}>
            {/* Business Name */}
            <SkeletonLoader width="70%" height={16} style={{ marginBottom: 2 }} />
            {/* Review Date */}
            <SkeletonLoader width="50%" height={12} />
          </View>
        </View>

        {/* Visit Button */}
        <SkeletonLoader width={28} height={28} borderRadius={14} />
      </View>

      {/* Review Content - matches exact padding and layout */}
      <View style={styles.reviewTextContainer}>
        {/* Rating Stars */}
        <View style={styles.reviewStars}>
          {[1, 2, 3, 4, 5].map((star) => (
            <SkeletonLoader
              key={star}
              width={20}
              height={20}
              borderRadius={10}
              style={{ marginRight: 4 }}
            />
          ))}
        </View>

        {/* Review Text */}
        <SkeletonLoader width="100%" height={14} style={{ marginTop: 12, marginBottom: 6 }} />
        <SkeletonLoader width="85%" height={14} style={{ marginBottom: 6 }} />
        <SkeletonLoader width="70%" height={14} style={{ marginBottom: 16 }} />

        {/* Action Buttons */}
        <View style={styles.reviewActions}>
          <SkeletonLoader width="45%" height={32} borderRadius={8} />
          <SkeletonLoader width="45%" height={32} borderRadius={8} />
        </View>
      </View>
    </View>
  );
};

// Review List Skeleton (for initial loading)
export const ReviewListSkeleton: React.FC<{ count?: number }> = ({ count = 3 }) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  return (
    <View style={styles.reviewList}>
      {Array.from({ length: count }).map((_, index) => (
        <ReviewCardSkeleton key={index} />
      ))}
    </View>
  );
};

// Review Loading More Skeleton (for infinite scroll)
export const ReviewLoadingMoreSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createSkeletonLoaderStyles(isDark);

  return (
    <View style={styles.loadMoreContainer}>
      <ReviewCardSkeleton />
    </View>
  );
};
