import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";

/**
 * GET handler for fetching the Razorpay key ID
 *
 * This API endpoint returns the Razorpay key ID for the client to use
 * in the checkout process. It verifies that the user is authenticated
 * before returning the key.
 *
 * Example response:
 * ```json
 * {
 *   "success": true,
 *   "key_id": "rzp_test_ksxy6FklIhV1xC"
 * }
 * ```
 */
export async function GET(_request: NextRequest) {
  try {
    // Verify authentication using Supabase
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get the Razorpay key ID based on the environment
    const keyId = process.env.NODE_ENV === 'production'
      ? process.env.RAZORPAY_LIVE_KEY_ID || 'rzp_live_WAmmYKACQauwBQ'
      : process.env.RAZORPAY_KEY_ID || 'rzp_test_ksxy6FklIhV1xC';

    // Return the key ID
    return NextResponse.json({
      success: true,
      key_id: keyId,
    });
  } catch (error) {
    console.error("Error fetching Razorpay key ID:", error);
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
