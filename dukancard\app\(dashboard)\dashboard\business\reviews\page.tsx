import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import BusinessReviewsPageClient from './components/BusinessReviewsPageClient';

export const metadata: Metadata = {
  title: "Reviews",
  robots: "noindex, nofollow",
};

export default async function BusinessReviewsPage() {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect('/login?message=Please log in to view your business reviews.');
  }

  // Check if the user has a business profile
  const { data: businessProfile, error: profileError } = await supabase
    .from('business_profiles')
    .select('id')
    .eq('id', user.id)
    .single();

  if (profileError || !businessProfile) {
    redirect('/dashboard/business?message=Please complete your business profile first.');
  }

  try {
    // Use admin client to get counts for both tabs
    const adminClient = createAdminClient();

    // Get count of reviews received by the business
    const { count: reviewsReceivedCount } = await adminClient
      .from('ratings_reviews')
      .select('*', { count: 'exact', head: true })
      .eq('business_profile_id', businessProfile.id)
      .neq('user_id', businessProfile.id);

    // Get count of reviews given by the business
    const { count: myReviewsCount } = await adminClient
      .from('ratings_reviews')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);

    return (
      <BusinessReviewsPageClient
        businessProfileId={businessProfile.id}
        reviewsReceivedCount={reviewsReceivedCount || 0}
        myReviewsCount={myReviewsCount || 0}
      />
    );
  } catch (_error) {
    // If there's an error fetching counts, still render the page with 0 counts
    return (
      <BusinessReviewsPageClient
        businessProfileId={businessProfile.id}
        reviewsReceivedCount={0}
        myReviewsCount={0}
      />
    );
  }
}
