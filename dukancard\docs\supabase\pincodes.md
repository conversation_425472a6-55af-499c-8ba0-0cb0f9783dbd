# Pincodes Table Documentation

## Table Overview 

The `pincodes` table in the Dukancard application stores comprehensive postal code (pincode) data for locations in India. It serves as a reference table for address validation, location-based searches, and generating URL-friendly slugs for geographic entities.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | bigint | NO | IDENTITY | Primary key, auto-incrementing identifier |
| created_at | timestamptz | NO | now() | Timestamp when the record was created |
| CircleName | text | NO | | Postal circle name (administrative division of India Post) |
| RegionName | text | NO | | Region name within the postal circle |
| DivisionName | text | NO | | Division name (typically corresponds to city) |
| OfficeName | text | NO | | Post office name (typically corresponds to locality) |
| Pincode | text | NO | | Postal code (6-digit code in India) |
| OfficeType | text | NO | | Type of post office (e.g., Head Office, Sub Office) |
| Delivery | text | NO | | Delivery status (e.g., 'Delivery', 'Non-Delivery') |
| District | text | NO | | District name |
| StateName | text | NO | | State name |
| Latitude | text | NO | | Latitude coordinate of the location |
| Longitude | text | NO | | Longitude coordinate of the location |
| city_slug | text | YES | | URL-friendly version of the city/division name |
| state_slug | text | YES | | URL-friendly version of the state name |
| locality_slug | text | YES | | URL-friendly version of the locality/office name |

## Constraints

### Primary Key
- `pincodes_pkey` - Primary key constraint on the `id` column

### Not Null Constraints
Multiple check constraints ensure that required fields are not null.

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| pincodes_pkey | UNIQUE | id | Primary key index |
| idx_pincodes_city_slug | BTREE | city_slug | Index for faster city slug lookups |
| idx_pincodes_state_slug | BTREE | state_slug | Index for faster state slug lookups |
| idx_pincodes_locality_slug | BTREE | locality_slug | Index for faster locality slug lookups |

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| Enable read access for all users | SELECT | true | |

This policy ensures that all users can read from the pincodes table, but no one can modify it (as there are no INSERT, UPDATE, or DELETE policies).

## Related Tables

### business_profiles
The `business_profiles` table references the `pincodes` table indirectly through the `update_business_profile_slugs` trigger function, which uses pincode data to set the city_slug, state_slug, and locality_slug fields.

## Usage Notes

1. **Reference Data**:
   - The table serves as a comprehensive reference of Indian postal codes and their associated geographic information
   - It contains data from the Indian Postal Service, including administrative divisions and coordinates

2. **Slug Generation**:
   - The slug fields (city_slug, state_slug, locality_slug) are URL-friendly versions of the geographic names
   - These are used in the business_profiles table for generating SEO-friendly URLs
   - The `update_business_profile_slugs` trigger function in the business_profiles table uses this data

3. **Address Validation**:
   - The table can be used to validate address information entered by users
   - It provides a way to standardize location names based on official postal data

4. **Location-Based Features**:
   - The geographic data enables location-based searches and filtering
   - Latitude and longitude coordinates can be used for distance calculations and mapping

5. **Security**:
   - The table has a read-only RLS policy, allowing all users to query the data
   - No policies exist for modifying the data, as it's likely managed through administrative processes

6. **Data Structure**:
   - The table follows the hierarchical structure of the Indian postal system:
     - State > District > Division > Office
   - This hierarchy is reflected in the naming of the columns

7. **Performance Optimization**:
   - Indexes on the slug fields optimize queries that filter or join on these columns
   - This is particularly important for the business profile slug generation process

8. **SEO Considerations**:
   - Based on project memory, the slugs are used in SEO-friendly URLs
   - For category pages, the format follows 'discover category name near me'
   - State names in category pages use title case instead of all uppercase

9. **Data Relationships**:
   - Each pincode record represents a unique post office location
   - Multiple pincodes can exist within the same city/division
   - The relationship between pincodes and business addresses is many-to-many (many businesses can share a pincode, and some businesses might have multiple locations with different pincodes)
