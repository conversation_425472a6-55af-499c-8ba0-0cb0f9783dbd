import { createClient } from '@/utils/supabase/server';
import { AdData, CustomAdResponse } from '@/types/ad';

/**
 * Fetches a custom ad for a specific pincode using the database function
 * @param pincode - The pincode to fetch ads for
 * @returns AdData object or null if no ads found
 */
export async function fetchCustomAdForPincode(pincode: string): Promise<AdData> {
  try {
    const supabase = await createClient();
    // Check if custom_ads table exists first
    const { count, error: tableCheckError } = await supabase
      .from('custom_ads')
      .select('*', { count: 'exact', head: true });

    if (tableCheckError || count === null) {
      console.log('Custom ads table not available, skipping ad fetch');
      return null;
    }

    // Use the get_ad_for_pincode function to find the appropriate ad
    const { data: adData, error: adError } = await supabase.rpc(
      'get_ad_for_pincode',
      { target_pincode: pincode }
    );

    if (adError) {
      console.error(`<PERSON><PERSON><PERSON> fetching ad for pincode ${pincode}:`, adError);
      return null;
    }

    if (adData && adData.length > 0) {
      const ad = adData[0] as CustomAdResponse;
      return {
        type: "custom",
        imageUrl: ad.ad_image_url,
        linkUrl: ad.ad_link_url,
      };
    }

    return null;
  } catch (error) {
    console.error('Error in fetchCustomAdForPincode:', error);
    return null;
  }
}

/**
 * Fallback function for older database schema (before migration)
 * @param pincode - The pincode to fetch ads for
 * @returns AdData object or null if no ads found
 */
export async function fetchCustomAdFallback(pincode?: string): Promise<AdData> {
  try {
    const supabase = await createClient();
    if (pincode) {
      // Try to find pincode-specific or global ads using old schema
      const { data: customAd } = await supabase
        .from('custom_ads')
        .select('ad_image_url, ad_link_url')
        .eq('is_active', true)
        .or(
          `targeting_locations.eq.'"global"',targeting_locations.cs.'["${pincode}"]'`
        )
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (customAd) {
        return {
          type: "custom",
          imageUrl: customAd.ad_image_url,
          linkUrl: customAd.ad_link_url,
        };
      }
    } else {
      // If no pincode, try to find global ads
      const { data: globalAd } = await supabase
        .from('custom_ads')
        .select('ad_image_url, ad_link_url')
        .eq('is_active', true)
        .eq('targeting_locations', '"global"')
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (globalAd) {
        return {
          type: "custom",
          imageUrl: globalAd.ad_image_url,
          linkUrl: globalAd.ad_link_url,
        };
      }
    }

    return null;
  } catch (error) {
    console.error('Error in fetchCustomAdFallback:', error);
    return null;
  }
}

/**
 * Main function to fetch custom ads with fallback support
 * @param pincode - The pincode to fetch ads for (optional)
 * @returns AdData object or null if no ads found
 */
export async function fetchCustomAd(pincode?: string): Promise<AdData> {
  const targetPincode = pincode || "999999"; // Use dummy pincode if none provided
  
  // Try the new function first
  const adData = await fetchCustomAdForPincode(targetPincode);
  
  if (adData) {
    return adData;
  }
  
  // Fallback to old schema if new function doesn't work
  return await fetchCustomAdFallback(pincode);
}
