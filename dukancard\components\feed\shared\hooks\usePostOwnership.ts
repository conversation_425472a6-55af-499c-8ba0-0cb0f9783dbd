"use client";

import { useState, useEffect } from "react";
import { createClient } from "@/utils/supabase/client";

export interface UsePostOwnershipProps {
  postBusinessId?: string;
}

export interface UsePostOwnershipReturn {
  isOwner: boolean;
  isLoading: boolean;
  currentUserId: string | null;
}

/**
 * Hook to determine if the current user owns a specific post
 */
export function usePostOwnership({ postBusinessId }: UsePostOwnershipProps): UsePostOwnershipReturn {
  const [isOwner, setIsOwner] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  useEffect(() => {
    const checkOwnership = async () => {
      try {
        const supabase = createClient();
        const { data: { user }, error } = await supabase.auth.getUser();

        if (error || !user) {
          setIsOwner(false);
          setCurrentUserId(null);
          setIsLoading(false);
          return;
        }

        setCurrentUserId(user.id);
        
        // Check if the current user's ID matches the post's business_id
        if (postBusinessId && user.id === postBusinessId) {
          setIsOwner(true);
        } else {
          setIsOwner(false);
        }
      } catch (error) {
        console.error("Error checking post ownership:", error);
        setIsOwner(false);
        setCurrentUserId(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkOwnership();
  }, [postBusinessId]);

  return {
    isOwner,
    isLoading,
    currentUserId,
  };
}

/**
 * Simple utility function to check if user owns a post
 */
export function checkPostOwnership(currentUserId: string | null, postBusinessId: string | undefined): boolean {
  return !!(currentUserId && postBusinessId && currentUserId === postBusinessId);
}
