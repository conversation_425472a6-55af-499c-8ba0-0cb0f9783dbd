# Ad Implementation in Dukancard

This document explains how ads are implemented in the Dukancard public card page.

## Types of Ads

Dukancard supports custom ads:

1. **Custom Ads**: Targeted ads based on pincode, managed through Supabase

## Ad Placements

Ads appear in two locations on the public card page:

1. **Top-Right Ad**: A display ad that appears in the top-right section of the card page
2. **In-Article Ads**: Ads that appear between products in the product listing

## Implementation Details

### Top-Right Ad

The top-right ad is implemented in `PublicCardPageClient.tsx` and follows this logic:

1. First, try to show a custom ad targeted to the business's pincode
2. If no custom ad is available, fall back to a Google AdSense display ad
3. If ads are not enabled for the user's plan, show nothing

```tsx
{
  topAdData?.type === "custom" && topAdData.imageUrl ? (
    <Link
      href={topAdData.linkUrl || "#"}
      target="_blank"
      rel="noopener noreferrer"
      className="block w-full aspect-video relative rounded-lg overflow-hidden"
    >
      <Image
        src={topAdData.imageUrl}
        alt="Advertisement"
        fill
        style={{ objectFit: "contain" }}
        className="rounded-lg"
      />
    </Link>
  ) : topAdData?.type === "google" ? (
    <div className="w-full aspect-video bg-neutral-100 dark:bg-neutral-800 rounded-lg flex items-center justify-center">
      <AdSlot adKey="public-card-top-right" />
    </div>
  ) : (
    <div className="border border-dashed rounded-lg p-4 flex items-center justify-center h-full w-full text-neutral-500 bg-neutral-50 dark:bg-neutral-800/50 aspect-video">
      {/* Placeholder */}
    </div>
  );
}
```

### In-Article Ads

In-article ads appear between products in the product listing. They are implemented with the following features:

1. **Dynamic Placement**: Ads appear at specific positions in the product list (defined by `AD_POSITIONS`)
2. **Full-Width Design**: Ads span the full width of the product grid
3. **Natural Integration**: Ads are styled to blend naturally with the content
4. **Plan-Based Display**: Only shown for basic and growth plan users

```tsx
const AD_POSITIONS = [3, 8, 15, 22, 30, 38, 46]; // Show ads at these positions

{
  products.map((product, index) => {
    const showAd =
      (userPlan === "basic" || userPlan === "growth") &&
      AD_POSITIONS.includes(index + 1);
    return (
      <Fragment key={product.id}>
        <ProductListItem product={product} />
        {showAd && (
          <div className="col-span-2 md:col-span-3 lg:col-span-4 my-4 py-4 border-t border-b border-neutral-200 dark:border-neutral-700">
            <div className="max-w-3xl mx-auto">
              <AdSlot adKey={`in-article-${index}`} className="min-h-[120px]" />
            </div>
          </div>
        )}
      </Fragment>
    );
  });
}
```

## Ad Components

### AdSlot Component

The `AdSlot` component is a wrapper that renders different types of ads based on the `adKey` prop:

```tsx
const AdSlot: React.FC<AdSlotProps> = ({ adKey, className }) => {
  // Top-right display ad
  if (adKey === "public-card-top-right") {
    return (
      <div className={className} data-ad-key={adKey}>
        <GoogleAdScript
          adClient={ADSENSE_CLIENT}
          adSlot={TOP_RIGHT_SLOT}
          adFormat="auto"
          fullWidthResponsive={true}
          className="w-full h-full"
        />
      </div>
    );
  }

  // In-article ad
  if (adKey.startsWith("in-article-")) {
    return (
      <div className={className} data-ad-key={adKey}>
        <GoogleAdScript
          adClient={ADSENSE_CLIENT}
          adSlot={IN_GRID_SLOT}
          adFormat="fluid"
          adLayout="in-article"
          className="w-full h-full"
        />
      </div>
    );
  }

  // Default placeholder
  return (
    <div
      className={`flex items-center justify-center w-full h-full bg-neutral-200 dark:bg-neutral-700 border border-dashed border-neutral-400 dark:border-neutral-600 rounded-lg text-neutral-500 dark:text-neutral-400 text-sm p-4 ${className}`}
      data-ad-key={adKey}
    >
      <div className="text-center">
        <p className="font-medium">Advertisement</p>
        <p className="text-xs">(Slot: {adKey})</p>
      </div>
    </div>
  );
};
```

### GoogleAdScript Component

The `GoogleAdScript` component handles the actual rendering of Google AdSense ads:

```tsx
export default function GoogleAdScript({
  adClient,
  adSlot,
  adFormat = "auto",
  fullWidthResponsive = true,
  adLayout,
  className = "",
}: GoogleAdScriptProps) {
  const adRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Only run in browser environment
    if (typeof window === "undefined") return;

    // Clear previous ad content if any (for dynamic rerenders)
    if (adRef.current) {
      adRef.current.innerHTML = "";

      // Create the ins element
      const ins = document.createElement("ins");
      ins.className = "adsbygoogle";
      ins.style.display = "block";
      if (adLayout === "in-article") {
        ins.style.textAlign = "center";
      }
      ins.setAttribute("data-ad-client", adClient);
      ins.setAttribute("data-ad-slot", adSlot);
      ins.setAttribute("data-ad-format", adFormat);
      ins.setAttribute(
        "data-full-width-responsive",
        fullWidthResponsive ? "true" : "false"
      );
      if (adLayout) {
        ins.setAttribute("data-ad-layout", adLayout);
      }

      // Append the ins element to the container
      adRef.current.appendChild(ins);
    }

    // Load the AdSense script if it's not already loaded
    const loadAdSenseScript = () => {
      if (!document.querySelector('script[src*="adsbygoogle.js"]')) {
        const script = document.createElement("script");
        script.async = true;
        script.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${adClient}`;
        script.crossOrigin = "anonymous";
        document.head.appendChild(script);

        // Wait for script to load before pushing ad
        script.onload = pushAd;
      } else {
        // Script already loaded, push ad directly
        pushAd();
      }
    };

    // Push the ad to AdSense
    const pushAd = () => {
      try {
        // @ts-expect-error: adsbygoogle is not typed on the window object
        (window.adsbygoogle = window.adsbygoogle || []).push({});
      } catch (e) {
        console.error("Error initializing AdSense ad:", e);
      }
    };

    // Start the process
    loadAdSenseScript();

    // Cleanup function
    return () => {
      if (adRef.current) {
        adRef.current.innerHTML = "";
      }
    };
  }, [adClient, adSlot, adFormat, adLayout, fullWidthResponsive]);

  return (
    <div ref={adRef} className={className} data-ad-container="true">
      {/* AdSense ins element will be created dynamically */}
    </div>
  );
}
```

## Ad Configuration

### Google AdSense Configuration

The Google AdSense configuration is defined in the `AdSlot` component:

```tsx
const ADSENSE_CLIENT = "ca-pub-3570662930292642";
const TOP_RIGHT_SLOT = "7675098009";
const IN_GRID_SLOT = "8358220755";
```

### Custom Ads Configuration

Custom ads are managed through the Supabase database. See the [Custom Ads Management](./custom-ads-management.md) document for details.

## Ad Display Logic

1. **Plan-Based Display**:

   - Basic and Growth plan users see both top-right and in-article ads
   - Pro and Enterprise plan users don't see ads

2. **Custom Ads Logic**:

   - First try to show custom ads targeted to the business's pincode
   - If no custom ads are available, show a placeholder

3. **In-Article Ad Placement**:
   - In-article ads appear at specific positions in the product list
   - The positions are defined by the `AD_POSITIONS` array
   - This creates a more natural reading experience compared to fixed intervals
