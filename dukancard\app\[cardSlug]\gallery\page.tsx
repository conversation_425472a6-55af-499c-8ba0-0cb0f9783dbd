import { notFound } from "next/navigation";
import { createClient } from "@/utils/supabase/server";
import { Metadata } from "next";
import { getSecureBusinessProfileBySlug } from "@/lib/actions/businessProfiles";
import { getBusinessGalleryImagesPaginated } from "@/lib/actions/gallery";
import GalleryPageClient from "./GalleryPageClient";
import OfflineBusinessMessage from "../components/OfflineBusinessMessage";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ cardSlug: string }>;
}): Promise<Metadata> {
  const { cardSlug } = await params;
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const pageUrl = `${siteUrl}/${cardSlug}/gallery`;

  // Use the secure method to fetch the business profile
  const { data: businessProfile, error: profileError } = await getSecureBusinessProfileBySlug(
    cardSlug
  );

  if (profileError || !businessProfile) {
    // Use notFound() to trigger the 404 page for non-existent business slugs
    notFound();
  }

  const businessName = businessProfile.business_name || "Business";

  return {
    title: `${businessName} - Photo Gallery | Dukancard`,
    description: `View the photo gallery for ${businessName}. Browse all images showcasing their products, services, and business.`,
    openGraph: {
      title: `${businessName} - Photo Gallery | Dukancard`,
      description: `View the photo gallery for ${businessName}. Browse all images showcasing their products, services, and business.`,
      url: pageUrl,
      siteName: "Dukancard",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: `${businessName} - Photo Gallery | Dukancard`,
      description: `View the photo gallery for ${businessName}. Browse all images showcasing their products, services, and business.`,
    },
  };
}

export default async function GalleryPage({
  params,
}: {
  params: Promise<{ cardSlug: string }>;
}) {
  const { cardSlug } = await params;
  const supabase = await createClient();

  // Use the secure method to fetch the business profile
  const { data: businessProfile, error: profileError } = await getSecureBusinessProfileBySlug(
    cardSlug
  );

  if (profileError || !businessProfile) {
    console.error(`Error fetching profile for slug ${cardSlug}:`, profileError);
    notFound();
  }

  // Check if the profile is online
  if (businessProfile.status !== "online") {
    console.log(
      `Business profile ${cardSlug} is not online (status: ${businessProfile.status}).`
    );
    // Show offline message instead of 404
    return <OfflineBusinessMessage />;
  }

  // Get the current user if authenticated
  const {
    data: { user },
  } = await supabase.auth.getUser();
  const currentUserId = user?.id || null;
  const isAuthenticated = !!user;

  // Get the business's current plan
  const { data: subscription } = await supabase
    .from("payment_subscriptions")
    .select("plan_id, status")
    .eq("business_profile_id", businessProfile.id)
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  // Default to free plan if no subscription found
  const planId = subscription?.plan_id || "free";
  const userPlan = planId as "free" | "basic" | "growth" | "pro" | "enterprise" | "trial";

  // Get initial gallery images (first page)
  const {
    images: galleryImages,
    totalCount,
    totalPages,
    currentPage,
    hasNextPage,
    hasPrevPage,
    error: galleryError
  } = await getBusinessGalleryImagesPaginated(cardSlug, 1, 20);

  if (galleryError) {
    console.error(`Error fetching gallery images for ${cardSlug}:`, galleryError);
  }

  // If there are no gallery images, redirect to the main card page
  if (!galleryImages || totalCount === 0) {
    return notFound();
  }

  return (
    <div className="min-h-screen flex flex-col">
      <GalleryPageClient
        businessProfile={{
          business_slug: businessProfile.business_slug || '',
          business_name: businessProfile.business_name || '',
          id: businessProfile.id
        }}
        galleryImages={galleryImages}
        totalCount={totalCount}
        totalPages={totalPages}
        currentPage={currentPage}
        hasNextPage={hasNextPage}
        hasPrevPage={hasPrevPage}
        userPlan={userPlan}
        isAuthenticated={isAuthenticated}
        currentUserId={currentUserId}
      />
    </div>
  );
}
