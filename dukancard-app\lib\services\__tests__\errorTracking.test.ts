/**
 * Error Tracking Tests
 * Tests for the error tracking functionality
 */

import { errorTracker } from '../errorTracking';
import { devErrorMonitor } from '../developmentErrorMonitoring';
import { productionErrorLogger } from '../productionErrorLogging';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(() => Promise.resolve()),
  removeItem: jest.fn(() => Promise.resolve()),
}));

// Mock Expo Constants
jest.mock('expo-constants', () => ({
  expoConfig: {
    extra: {
      appEnv: 'development',
      enableCrashReporting: false,
    },
    version: '1.0.0',
  },
  platform: {
    ios: false,
    android: true,
  },
  deviceName: 'Test Device',
}));

describe('Error Tracking Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Clear any stored errors
    errorTracker.clearStoredErrors();
  });

  describe('Basic Error Logging', () => {
    it('should log errors with context', () => {
      const testError = new Error('Test error');
      const context = { screen: 'TestScreen', action: 'testAction' };

      // Mock console.error to verify it's called
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      errorTracker.logError(testError, context);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error tracked:'),
        expect.objectContaining({
          error: expect.objectContaining({
            message: 'Test error',
          }),
        })
      );

      consoleSpy.mockRestore();
    });

    it('should store errors locally', () => {
      const testError = new Error('Test error');
      
      errorTracker.logError(testError);
      
      const storedErrors = errorTracker.getStoredErrors();
      expect(storedErrors).toHaveLength(1);
      expect(storedErrors[0].error.message).toBe('Test error');
    });

    it('should limit stored errors to maximum', () => {
      // Log more than the maximum number of errors
      for (let i = 0; i < 60; i++) {
        errorTracker.logError(new Error(`Error ${i}`));
      }

      const storedErrors = errorTracker.getStoredErrors();
      expect(storedErrors.length).toBeLessThanOrEqual(50); // maxStoredErrors
    });
  });

  describe('Development Error Monitoring', () => {
    it('should be active in development mode', () => {
      expect(devErrorMonitor.isDevelopmentMode()).toBe(true);
    });

    it('should log component errors with context', () => {
      const testError = new Error('Component error');
      const errorInfo = { componentStack: 'TestComponent' };
      const componentName = 'TestComponent';

      const consoleSpy = jest.spyOn(console, 'group').mockImplementation();
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
      const consoleGroupEndSpy = jest.spyOn(console, 'groupEnd').mockImplementation();

      devErrorMonitor.logComponentError(testError, errorInfo, componentName);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Component Error: TestComponent')
      );
      expect(consoleErrorSpy).toHaveBeenCalled();
      expect(consoleGroupEndSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
      consoleErrorSpy.mockRestore();
      consoleGroupEndSpy.mockRestore();
    });

    it('should track development errors', () => {
      const initialCount = devErrorMonitor.getDevErrors().length;
      
      devErrorMonitor.logNavigationError(new Error('Navigation error'), '/test');
      
      const devErrors = devErrorMonitor.getDevErrors();
      expect(devErrors.length).toBe(initialCount + 1);
      expect(devErrors[0].message).toContain('Navigation Error');
    });
  });

  describe('Production Error Logging', () => {
    it('should initialize without errors', async () => {
      await expect(productionErrorLogger.initialize()).resolves.not.toThrow();
    });

    it('should get error statistics', async () => {
      const stats = await productionErrorLogger.getErrorStats();
      
      expect(stats).toHaveProperty('total');
      expect(stats).toHaveProperty('sent');
      expect(stats).toHaveProperty('pending');
      expect(stats).toHaveProperty('recent');
      expect(typeof stats.total).toBe('number');
    });

    it('should clear old errors', async () => {
      await expect(productionErrorLogger.clearOldErrors(7)).resolves.not.toThrow();
    });
  });

  describe('Error Tracking Integration', () => {
    it('should handle custom events', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      errorTracker.logEvent('Test event', { data: 'test' });

      expect(consoleSpy).toHaveBeenCalledWith(
        'Event:',
        'Test event',
        { data: 'test' }
      );

      consoleSpy.mockRestore();
    });

    it('should log user actions', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      errorTracker.logUserAction('button_click', 'HomeScreen', { buttonId: 'test' });

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('User Action [HomeScreen]:'),
        'button_click',
        { buttonId: 'test' }
      );

      consoleSpy.mockRestore();
    });

    it('should enable/disable tracking', () => {
      errorTracker.setEnabled(false);
      expect(errorTracker.isTrackingEnabled()).toBe(false);

      errorTracker.setEnabled(true);
      expect(errorTracker.isTrackingEnabled()).toBe(true);
    });
  });
});

describe('Error Tracking Performance', () => {
  it('should handle rapid error logging without performance issues', () => {
    const startTime = Date.now();
    
    // Log many errors rapidly
    for (let i = 0; i < 100; i++) {
      errorTracker.logError(new Error(`Rapid error ${i}`));
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Should complete within reasonable time (less than 1 second)
    expect(duration).toBeLessThan(1000);
  });

  it('should not block the main thread', async () => {
    const promises = [];
    
    // Log errors asynchronously
    for (let i = 0; i < 50; i++) {
      promises.push(
        productionErrorLogger.logProductionError(
          new Error(`Async error ${i}`),
          { screen: 'TestScreen' }
        )
      );
    }
    
    // All promises should resolve without throwing
    await expect(Promise.all(promises)).resolves.not.toThrow();
  });
});
