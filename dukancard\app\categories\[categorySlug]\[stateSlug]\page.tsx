import { Metadata } from "next";
import { Suspense } from "react";
import { getCategoryBySlug, BUSINESS_CATEGORIES } from "@/lib/config/categories";
import { notFound } from "next/navigation";
import ModernCategoryClient from "../ModernCategoryClient";
import ModernResultsSkeleton from "@/app/(main)/discover/ModernResultsSkeleton";
import { INDIAN_STATE_DATA } from "@/lib/config/states";
import { fetchBusinessesByLocation } from "@/lib/actions/categories/locationBasedFetching";

// Force dynamic rendering to prevent build-time generation
export const dynamic = 'force-dynamic';

interface CategoryStatePageProps {
  params: Promise<{
    categorySlug: string;
    stateSlug: string;
  }>;
}

export async function generateMetadata({ params }: CategoryStatePageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const { categorySlug, stateSlug } = resolvedParams;

  // Get the category by slug
  const category = getCategoryBySlug(categorySlug);
  if (!category) {
    return {
      title: "Category Not Found",
      description: "The requested category could not be found."
    };
  }

  // Find the matching state using the state_slug directly
  const stateData = INDIAN_STATE_DATA.find(state => state.slug === stateSlug);
  const matchingState = stateData?.name;

  if (!matchingState) {
    return {
      title: "State Not Found",
      description: "The requested state could not be found."
    };
  }

  // State names are already in title case format in INDIAN_STATES
  const formattedState = matchingState;

  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const pageUrl = `${siteUrl}/categories/${categorySlug}/${stateSlug}`;

  return {
    title: `${category.name} in ${formattedState} | Best ${category.name} in ${formattedState}`,
    description: `Find ${category.name.toLowerCase()} in ${formattedState}. Browse local ${category.name.toLowerCase()} businesses, products, and services in ${formattedState} with Dukancard.`,
    openGraph: {
      title: `${category.name} in ${formattedState} | Best ${category.name} in ${formattedState}`,
      description: `Find ${category.name.toLowerCase()} in ${formattedState}. Browse local ${category.name.toLowerCase()} businesses, products, and services in ${formattedState} with Dukancard.`,
      url: pageUrl,
      siteName: "Dukancard",
      locale: "en_IN",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: `${category.name} in ${formattedState} | Best ${category.name} in ${formattedState}`,
      description: `Find ${category.name.toLowerCase()} in ${formattedState}. Browse local ${category.name.toLowerCase()} businesses, products, and services in ${formattedState} with Dukancard.`,
    },
    alternates: {
      canonical: pageUrl,
    },
  };
}

// Server Component - Handles initial rendering
async function CategoryStatePageContent({ params }: CategoryStatePageProps) {
  const resolvedParams = await params;
  const { categorySlug, stateSlug } = resolvedParams;

  // Get the category by slug
  const category = getCategoryBySlug(categorySlug);
  if (!category) {
    notFound();
  }

  // Find the matching state using the state_slug directly
  const stateData = INDIAN_STATE_DATA.find(state => state.slug === stateSlug);
  const matchingState = stateData?.name;

  if (!matchingState) {
    notFound();
  }

  // Create a serializable version of the category without methods
  const serializableCategory = {
    name: category.name,
    slug: category.slug,
    description: category.description,
    isPopular: category.isPopular,
    iconName: category.icon.name // Include the icon name for rendering on the client
  };

  // Fetch businesses in this category filtered by state slug
  const { data: businesses, count } = await fetchBusinessesByLocation({
    categoryName: category.name,
    state: matchingState,
    stateSlug: stateSlug,
    page: 1,
    limit: 20
  });

  return (
    <ModernCategoryClient
      category={serializableCategory}
      initialBusinesses={businesses || []}
      totalCount={count || 0}
      locationInfo={{
        state: matchingState
      }}
    />
  );
}

// Main Page Component using Suspense for streaming
export default async function CategoryStatePage({ params }: CategoryStatePageProps) {
  return (
    <div className="min-h-screen bg-white dark:bg-black">
      <Suspense fallback={<ModernResultsSkeleton />}>
        <CategoryStatePageContent params={params} />
      </Suspense>
    </div>
  );
}

// Generate static params for all category-state combinations
export async function generateStaticParams() {
  const params = [];

  // Get all categories with slugs
  const categoriesWithSlugs = BUSINESS_CATEGORIES
    .filter(category => category.slug);

  // Generate params for all category-state combinations
  for (const category of categoriesWithSlugs) {
    for (const stateData of INDIAN_STATE_DATA) {
      // Use the state slug directly from the data
      params.push({
        categorySlug: category.slug,
        stateSlug: stateData.slug,
      });
    }
  }

  return params;
}
