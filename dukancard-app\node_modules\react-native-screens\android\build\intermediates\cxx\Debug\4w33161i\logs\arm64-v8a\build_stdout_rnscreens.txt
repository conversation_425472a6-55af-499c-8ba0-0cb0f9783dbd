ninja: Entering directory `C:\Users\<USER>\Desktop\Dukancard Web App\dukancard-app\node_modules\react-native-screens\android\.cxx\Debug\4w33161i\arm64-v8a'
[1/6] Building CXX object CMakeFiles/rnscreens.dir/5126a7643ca8c24f96e2827f80013d4e/react-native-screens/cpp/RNScreensTurboModule.cpp.o
[2/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o
[3/6] Building CXX object CMakeFiles/rnscreens.dir/5126a7643ca8c24f96e2827f80013d4e/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o
[4/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o
[5/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o
[6/6] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\4w33161i\obj\arm64-v8a\librnscreens.so
