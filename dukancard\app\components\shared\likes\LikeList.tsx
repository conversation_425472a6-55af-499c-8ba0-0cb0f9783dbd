'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Compass } from 'lucide-react';
import LikeCard, { LikeData } from './LikeCard';

interface LikeListProps {
  initialLikes: LikeData[];
  onUnlikeSuccess?: (_likeId: string) => void;
  showUnlike?: boolean;
  variant?: 'default' | 'compact';
  emptyMessage?: string;
  emptyDescription?: string;
  showDiscoverButton?: boolean;
  showVisitButton?: boolean;
  showAddress?: boolean;
  showRedirectIcon?: boolean;
}

export default function LikeList({
  initialLikes,
  onUnlikeSuccess,
  showUnlike = true,
  variant = 'default',
  emptyMessage = "No likes found.",
  emptyDescription = "Like profiles to see them here.",
  showDiscoverButton = false,
  showVisitButton = true,
  showAddress = true,
  showRedirectIcon = false
}: LikeListProps) {
  const [likes, setLikes] = useState(initialLikes);

  // Update likes when initialLikes changes
  useEffect(() => {
    setLikes(initialLikes);
  }, [initialLikes]);

  // Handle successful unlike
  const handleUnlikeSuccess = (likeId: string) => {
    setLikes((prevLikes) => prevLikes.filter((like) => like.id !== likeId));
    if (onUnlikeSuccess) {
      onUnlikeSuccess(likeId);
    }
  };

  // If there are no likes, show empty state
  if (likes.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <div className="mb-4">
            <div className="w-16 h-16 mx-auto bg-neutral-100 dark:bg-neutral-800 rounded-full flex items-center justify-center">
              <span className="text-2xl">💝</span>
            </div>
          </div>
          <h3 className="text-lg font-medium text-neutral-800 dark:text-neutral-100 mb-2">
            {emptyMessage}
          </h3>
          <p className="text-neutral-500 dark:text-neutral-400 mb-6">
            {emptyDescription}
          </p>
          {showDiscoverButton && (
            <Button asChild variant="outline" className="gap-2">
              <Link href="/businesses" target="_blank" rel="noopener noreferrer">
                <Compass className="w-4 h-4" />
                Discover Businesses
              </Link>
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {likes.map((like, _index) => {
        const profile = like.profile;

        if (!profile) {
          return null; // Skip items with missing profiles
        }

        return (
          <LikeCard
            key={like.id}
            likeId={like.id}
            profile={profile}
            onUnlikeSuccess={showUnlike ? handleUnlikeSuccess : undefined}
            showUnlike={showUnlike}
            variant={variant}
            showVisitButton={showVisitButton}
            showAddress={showAddress}
            showRedirectIcon={showRedirectIcon}
          />
        );
      })}
    </div>
  );
}
