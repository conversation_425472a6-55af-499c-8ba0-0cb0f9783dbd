"use client";

import React, { useState, useEffect } from "react";
import { Container } from "../components/ui/container";
import { motion } from "framer-motion";
import Link from "next/link";
import { Input } from "@/components/ui/input";
import { Search, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { CATEGORY_GROUPS, BusinessCategory } from "@/lib/config/categories";

interface _CategoryGroup {
  name: string;
  categories: BusinessCategory[];
}

export default function CategoriesPageClient() {
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryGroups, setCategoryGroups] = useState<typeof CATEGORY_GROUPS>([]);
  const [filteredGroups, setFilteredGroups] = useState<typeof CATEGORY_GROUPS>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Load categories on component mount
  useEffect(() => {
    setCategoryGroups(CATEGORY_GROUPS);
    setFilteredGroups(CATEGORY_GROUPS);
    setIsLoading(false);
  }, []);

  // Filter categories based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredGroups(categoryGroups);
      return;
    }

    const query = searchQuery.toLowerCase().trim();

    const filtered = categoryGroups.map((group) => {
      return {
        ...group,
        categories: group.categories.filter((category) =>
          category.name.toLowerCase().includes(query) ||
          (category.description && category.description.toLowerCase().includes(query))
        )
      };
    }).filter((group) => group.categories.length > 0);

    setFilteredGroups(filtered);
  }, [searchQuery, categoryGroups]);

  // Function to clear search
  const clearSearch = () => {
    setSearchQuery("");
  };

  return (
    <Container className="py-8 md:py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-8"
      >
        <h1 className="text-3xl md:text-4xl font-bold mb-4">
          Browse Business Categories
        </h1>
        <p className="text-muted-foreground max-w-2xl mx-auto mb-8">
          Explore local businesses, products, and services by category across India with Dukancard.
        </p>

        {/* Search input */}
        <div className="relative max-w-md mx-auto">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="h-5 w-5 text-muted-foreground" />
          </div>
          <Input
            type="text"
            placeholder="Search categories..."
            className="pl-10 pr-16 py-6 h-12 bg-white dark:bg-neutral-900 border-neutral-200 dark:border-neutral-800 focus:border-[var(--brand-gold)] focus:ring-[var(--brand-gold)]/20"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 -translate-y-1/2 h-8 px-2 text-muted-foreground hover:text-foreground"
              onClick={clearSearch}
            >
              Clear
            </Button>
          )}
        </div>
      </motion.div>

      {isLoading ? (
        <div className="flex justify-center items-center py-20">
          <Loader2 className="h-10 w-10 text-[var(--brand-gold)] animate-spin" />
        </div>
      ) : filteredGroups.length === 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <p className="text-lg text-muted-foreground">No categories found matching &quot;{searchQuery}&quot;</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={clearSearch}
          >
            Clear Search
          </Button>
        </motion.div>
      ) : (
        filteredGroups.map((group, groupIndex) => (
          <div key={groupIndex} className="mb-12">
            <motion.div
              className="flex justify-center mb-6"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: groupIndex * 0.1 }}
            >
              <div className="relative">
                {/* Decorative elements */}
                <div className="absolute -left-8 top-1/2 -translate-y-1/2 w-6 h-[2px] bg-gradient-to-r from-transparent to-[var(--brand-gold)] opacity-70 hidden md:block"></div>
                <div className="absolute -right-8 top-1/2 -translate-y-1/2 w-6 h-[2px] bg-gradient-to-l from-transparent to-[var(--brand-gold)] opacity-70 hidden md:block"></div>

                <h2 className="text-xl md:text-2xl font-bold text-neutral-800 dark:text-neutral-100 text-center px-4">
                  <span className="relative z-10 inline-block">
                    {group.name}
                    <motion.span
                      className="absolute -bottom-1 left-0 right-0 h-[6px] bg-[var(--brand-gold)]/20 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: "100%" }}
                      transition={{ duration: 0.8, delay: 0.3 + groupIndex * 0.1 }}
                    />
                  </span>
                </h2>
              </div>
            </motion.div>

            <motion.div
              className="grid grid-cols-3 md:grid-cols-5 gap-4 md:gap-5 lg:gap-6"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {group.categories.map((category, index) => (
                <motion.div
                  key={category.name}
                  className="flex flex-col items-center"
                  variants={itemVariants}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  whileHover={{ y: -5, transition: { duration: 0.2 } }}
                >
                  <Link href={`/categories/${category.slug}`} className="w-full">
                    <div className="relative group cursor-pointer">
                      {/* Hover glow effect */}
                      <motion.div
                        className="absolute -inset-1 rounded-xl bg-[var(--brand-gold)]/10 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        initial={{ scale: 0.9 }}
                        whileHover={{ scale: 1.1 }}
                      />

                      {/* Icon container */}
                      <div className="relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 lg:w-20 lg:h-20 mx-auto bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-xl group-hover:border-[var(--brand-gold)] transition-all duration-300 shadow-sm group-hover:shadow-md">
                        <motion.div
                          initial={{ scale: 1 }}
                          whileHover={{ scale: 1.1 }}
                          transition={{ type: "spring", stiffness: 400, damping: 10 }}
                        >
                          {React.createElement(category.icon, {
                            className: "w-7 h-7 md:w-8 md:h-8 lg:w-9 lg:h-9 text-[var(--brand-gold)]"
                          })}
                        </motion.div>
                      </div>
                    </div>

                    <span className="mt-3 text-sm md:text-sm lg:text-sm text-center font-medium text-neutral-700 dark:text-neutral-300 truncate max-w-full block">
                      {category.name}
                    </span>
                  </Link>
                </motion.div>
              ))}
            </motion.div>
          </div>
        ))
      )}
    </Container>
  );
}
