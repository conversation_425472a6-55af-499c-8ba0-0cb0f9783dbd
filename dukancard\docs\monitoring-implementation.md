# Razorpay Subscription Flow Monitoring Implementation

## Overview

This document describes the comprehensive monitoring and health check system implemented for the Razorpay subscription flow. The system provides real-time monitoring, automated alerting, and consistency checking to ensure the subscription system operates reliably.

## Architecture

### Components

1. **Database Functions** - Core monitoring functions in Supabase
2. **Monitoring Utilities** - TypeScript utilities for health checks and alerting
3. **API Endpoints** - REST APIs for health checks and administrative operations
4. **Transaction Utilities** - Ensures database consistency across operations
5. **Enhanced Validation** - Comprehensive webhook validation
6. **Frontend Improvements** - Timeout handling and error recovery

## Database Functions

### Core Monitoring Functions

#### `find_subscription_inconsistencies()`
- **Purpose**: Detects inconsistencies between `payment_subscriptions` and `business_profiles` tables
- **Returns**: Table with inconsistency details
- **Usage**: Called by monitoring utilities and health check APIs

#### `get_webhook_error_stats()`
- **Purpose**: Provides webhook processing statistics and error rates
- **Returns**: Comprehensive webhook metrics
- **Usage**: Performance monitoring and alerting

#### `get_subscription_health_metrics()`
- **Purpose**: Overall subscription system health metrics
- **Returns**: Health score and subscription counts
- **Usage**: System health monitoring

#### `fix_subscription_inconsistency(target_business_profile_id)`
- **Purpose**: Fixes subscription inconsistencies for specific business profiles
- **Returns**: Description of action taken
- **Usage**: Administrative operations and automated fixes

### System Alerts Table

```sql
CREATE TABLE system_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  alert_type TEXT NOT NULL,
  severity TEXT NOT NULL CHECK (severity IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
  message TEXT NOT NULL,
  entity_id TEXT,
  subscription_id TEXT,
  metadata JSONB,
  resolved BOOLEAN DEFAULT FALSE,
  resolved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Monitoring Utilities

### Location: `lib/razorpay/webhooks/monitoring.ts`

#### Key Functions

1. **`getWebhookMetrics(hours)`** - Retrieves webhook processing metrics
2. **`checkSubscriptionConsistencies()`** - Identifies subscription inconsistencies
3. **`checkWebhookHealth()`** - Monitors webhook failure rates
4. **`logCriticalAlert(alert)`** - Logs critical alerts to the system
5. **`runHealthCheck()`** - Comprehensive health check

#### Alert Types

- `SUBSCRIPTION_INCONSISTENCY` - Data consistency issues
- `HIGH_FAILURE_RATE` - Webhook processing failures
- `PAYMENT_FAILURE` - Payment processing issues
- `TERMINAL_STATE_VIOLATION` - State management violations

#### Severity Levels

- `LOW` - Minor issues, informational
- `MEDIUM` - Issues affecting some users
- `HIGH` - Issues affecting many users or core functionality
- `CRITICAL` - System-wide failures or data corruption risks

## API Endpoints

### Health Check API: `/api/health/subscription`

#### GET Request
- **Purpose**: Comprehensive health check with detailed metrics
- **Authentication**: Requires `x-api-key` header
- **Response**: Health status, metrics, alerts, and recommendations

#### HEAD Request
- **Purpose**: Quick health status check
- **Authentication**: Requires `x-api-key` header
- **Response**: HTTP status code indicating health

#### Example Response
```json
{
  "healthy": true,
  "timestamp": "2024-01-15T10:30:00Z",
  "metrics": {
    "webhook": {
      "total_events": 1250,
      "successful_events": 1200,
      "failed_events": 30,
      "retrying_events": 20,
      "success_rate": 96.00
    },
    "subscriptions": {
      "total_subscriptions": 500,
      "active_subscriptions": 350,
      "inconsistent_subscriptions": 2,
      "health_score": 99.60
    }
  },
  "alerts": [],
  "recommendations": []
}
```

### Admin API: `/api/admin/fix-subscription-inconsistency`

#### GET Request
- **Purpose**: List all subscription inconsistencies
- **Authentication**: Requires `x-api-key` header
- **Response**: Array of inconsistencies

#### POST Request
- **Purpose**: Fix subscription inconsistencies
- **Authentication**: Requires `x-api-key` header
- **Body**: `{ "business_profile_id": "uuid", "dry_run": false }`
- **Response**: Fix operation results

## Transaction Utilities

### Location: `lib/razorpay/webhooks/handlers/transactionUtils.ts`

#### Key Functions

1. **`updateSubscriptionWithBusinessProfile(data)`**
   - Ensures atomic updates across subscription and business profile tables
   - Implements rollback on failure
   - Prevents data inconsistencies

2. **`verifySubscriptionConsistency(subscription_id)`**
   - Validates consistency between related tables
   - Returns detailed consistency report

## Enhanced Webhook Validation

### Location: `lib/razorpay/webhooks/validation.ts`

#### Features

1. **Timestamp Validation** - Prevents replay attacks
2. **Payload Structure Validation** - Ensures webhook integrity
3. **Account ID Verification** - Validates webhook source
4. **Event-Specific Validation** - Tailored validation for different event types

#### Integration

```typescript
// In webhook route
const validationResult = validateWebhook(payload, expectedAccountId);
if (!validationResult.valid) {
  return NextResponse.json({
    success: false,
    errors: validationResult.errors
  }, { status: 400 });
}
```

## Frontend Improvements

### Payment Timeout Handling

#### Location: `lib/razorpay/utils/loadRazorpaySDK.ts`

#### Features

1. **Configurable Timeout** - Default 10 minutes, customizable
2. **Safe Resolution** - Prevents race conditions
3. **Proper Cleanup** - Clears timeouts and prevents memory leaks

#### Usage

```typescript
const response = await openRazorpaySubscriptionCheckout(
  keyId,
  subscriptionId,
  options,
  5 * 60 * 1000 // 5 minute timeout
);
```

## Environment Variables

Add these environment variables to your deployment:

```bash
# Health check API key for monitoring endpoints
HEALTH_CHECK_API_KEY=your_health_check_api_key_here

# Admin API key for administrative operations
ADMIN_API_KEY=your_admin_api_key_here

# Razorpay account ID for webhook validation
RAZORPAY_ACCOUNT_ID=your_razorpay_account_id_here
```

## Middleware Enhancements

### Location: `utils/supabase/middleware.ts`

#### Features

1. **Consistency Verification** - Checks subscription status consistency
2. **Warning Logging** - Logs inconsistencies for monitoring
3. **Non-Blocking** - Doesn't interrupt user experience

## Critical Fixes Implemented

### 1. Database Consistency Protection
- **Problem**: Business profile updates could fail silently
- **Solution**: Transaction utilities with rollback capability
- **Impact**: Prevents subscription/profile inconsistencies

### 2. Plan Switch Race Condition Prevention
- **Problem**: Old subscriptions might not be cancelled during plan switches
- **Solution**: Made old subscription cancellation blocking
- **Impact**: Prevents double billing

### 3. Enhanced Webhook Validation
- **Problem**: Missing validation for replay attacks and malformed payloads
- **Solution**: Comprehensive validation with timestamp and structure checks
- **Impact**: Improved security and reliability

### 4. Frontend Payment Timeout
- **Problem**: No timeout handling for payment modals
- **Solution**: Configurable timeout with proper cleanup
- **Impact**: Better user experience and resource management

### 5. Subscription Status Consistency
- **Problem**: Middleware didn't verify cross-table consistency
- **Solution**: Added consistency checks with warning logging
- **Impact**: Early detection of data inconsistencies

## Monitoring Best Practices

### 1. Regular Health Checks
- Run health checks every 5-15 minutes
- Set up alerts for health score below 95%
- Monitor webhook success rates

### 2. Alert Management
- Resolve alerts promptly
- Investigate patterns in alert types
- Set up escalation for critical alerts

### 3. Data Consistency
- Run consistency checks daily
- Fix inconsistencies during low-traffic periods
- Monitor for recurring inconsistency patterns

### 4. Performance Monitoring
- Track webhook processing times
- Monitor database function performance
- Set up alerts for degraded performance

## Troubleshooting Guide

### High Inconsistency Count
1. Check webhook processing logs
2. Verify database triggers are working
3. Review recent subscription changes
4. Run consistency fix for affected profiles

### Low Webhook Success Rate
1. Check network connectivity
2. Verify webhook endpoint availability
3. Review error logs in `processed_webhook_events`
4. Check Razorpay webhook configuration

### Performance Issues
1. Monitor function execution times
2. Check database indexes
3. Review query performance
4. Consider scaling database resources

## Deployment Checklist

- [ ] Database functions created in Supabase
- [ ] Environment variables configured
- [ ] API keys generated and secured
- [ ] Health check endpoint accessible
- [ ] Monitoring alerts configured
- [ ] Documentation updated
- [ ] Team trained on new monitoring tools

## Future Enhancements

1. **Real-time Dashboards** - Visual monitoring interfaces
2. **Automated Recovery** - Self-healing capabilities
3. **Predictive Alerting** - ML-based anomaly detection
4. **Integration Testing** - Automated end-to-end tests
5. **Performance Optimization** - Query and function optimization
