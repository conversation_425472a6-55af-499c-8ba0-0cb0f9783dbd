"use client";

import { useRef, useEffect } from "react";
import { Loader2 } from "lucide-react";
import { motion } from "framer-motion";
import { BusinessProfilePublicData } from "@/lib/actions/businessProfiles/types";
import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import ModernBusinessFilterGrid from "./ModernBusinessFilterGrid";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";

interface ModernBusinessResultsProps {
  businesses: (BusinessProfilePublicData | BusinessCardData)[];
  isAuthenticated: boolean;
  totalCount: number;
  hasMore: boolean;
  isLoadingMore: boolean;
  onLoadMore: () => void;
  onSortChange: (_sortBy: BusinessSortBy) => void;
  onSearch: (_searchTerm: string) => void;
  currentSortBy: BusinessSortBy;
  isLoading: boolean;
  initialSearchTerm?: string | null;
}

export default function ModernBusinessResults({
  businesses,
  isAuthenticated,
  hasMore,
  isLoadingMore,
  onLoadMore,
  onSortChange,
  onSearch,
  currentSortBy,
  isLoading,
  initialSearchTerm,
}: ModernBusinessResultsProps) {
  const observerTarget = useRef<HTMLDivElement>(null);

  // Set up intersection observer for infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoadingMore) {
          onLoadMore();
        }
      },
      { threshold: 0.1, rootMargin: "100px" }
    );

    const currentTarget = observerTarget.current;
    if (currentTarget) {
      observer.observe(currentTarget);
    }

    return () => {
      if (currentTarget) {
        observer.unobserve(currentTarget);
      }
    };
  }, [hasMore, isLoadingMore, onLoadMore]);

  // We'll always show the filter grid, regardless of loading state or empty results
  return (
    <div>
      <ModernBusinessFilterGrid
        businesses={businesses}
        isAuthenticated={isAuthenticated}
        onSortChange={onSortChange}
        onSearch={onSearch}
        currentSortBy={currentSortBy}
        isLoading={isLoading}
        initialSearchTerm={initialSearchTerm}
      />

      {/* Loading More Indicator - Always render the observer target */}
      {!isLoading && (
        <div
          ref={observerTarget}
          className="flex justify-center items-center py-8"
        >
          {isLoadingMore ? (
            <motion.div
              className="flex flex-col items-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <Loader2 className="h-6 w-6 animate-spin text-[var(--brand-gold)]" />
              <span className="mt-2 text-sm text-neutral-600 dark:text-neutral-400">
                Loading more businesses...
              </span>
            </motion.div>
          ) : hasMore ? (
            <span className="text-xs text-neutral-500 dark:text-neutral-500">
              Scroll to load more
            </span>
          ) : businesses.length > 0 ? (
            <span className="text-xs text-neutral-500 dark:text-neutral-500">
              You&apos;ve reached the end
            </span>
          ) : null}
        </div>
      )}
    </div>
  );
}
