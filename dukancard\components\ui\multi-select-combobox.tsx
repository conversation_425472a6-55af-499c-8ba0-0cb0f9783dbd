"use client";

import * as React from "react";
import { Check, ChevronsUpDown, PlusCircle, X } from "lucide-react";

import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
// Removed unused import: import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area"; // Import ScrollArea

export type SelectItem = {
  id: string;
  name: string;
};

interface MultiSelectComboboxProps {
  options: SelectItem[];
  selected: string[]; // Array of selected IDs
  onChange: (_selected: string[]) => void; // Prefixed unused 'selected'
  onCreate?: (_name: string) => Promise<SelectItem | null>; // Prefixed unused 'name'
  placeholder?: string;
  searchPlaceholder?: string;
  emptyPlaceholder?: string;
  createPlaceholder?: string;
  className?: string;
  isLoading?: boolean; // To show loading state
  disabled?: boolean; // To disable the component
}

export function MultiSelectCombobox({
  options,
  selected,
  onChange,
  onCreate,
  placeholder = "Select items...",
  searchPlaceholder = "Search items...",
  emptyPlaceholder = "No items found.",
  createPlaceholder = 'Create "{value}"',
  className,
  isLoading = false,
  disabled = false,
}: MultiSelectComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [searchTerm, setSearchTerm] = React.useState("");

  const handleSelect = (itemId: string) => {
    const newSelected = selected.includes(itemId)
      ? selected.filter((id) => id !== itemId)
      : [...selected, itemId];
    onChange(newSelected);
  };

  const handleCreate = async () => {
    if (!onCreate || !searchTerm) return;

    const newItem = await onCreate(searchTerm);
    if (newItem) {
      // Add the new item's ID to the selected list
      onChange([...selected, newItem.id]);
      setSearchTerm(""); // Clear search after creation
    }
    // Error handling for create failure should be handled within the onCreate function (e.g., showing a toast)
  };

  const selectedItems = selected
    .map((id) => options.find((option) => option.id === id))
    .filter((item): item is SelectItem => !!item); // Filter out undefined if an ID doesn't match

  const filteredOptions = options.filter(
    (option) =>
      !selected.includes(option.id) && // Exclude already selected
      option.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const showCreateOption =
    onCreate &&
    searchTerm &&
    !options.some(
      (option) => option.name.toLowerCase() === searchTerm.toLowerCase()
    );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild disabled={disabled || isLoading}>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between h-auto min-h-[2.5rem] bg-white dark:bg-neutral-800 border-neutral-300 dark:border-neutral-600 text-neutral-900 dark:text-white",
            selectedItems.length === 0 && "text-muted-foreground",
            className
          )}
          onClick={() => setOpen(!open)}
        >
          <div className="flex gap-1 flex-wrap">
            {isLoading ? (
              <span className="text-muted-foreground text-sm">Loading...</span>
            ) : selectedItems.length > 0 ? (
              selectedItems.map((item) => (
                <Badge
                  key={item.id}
                  variant="secondary"
                  className="mr-1 mb-1"
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent popover trigger
                    handleSelect(item.id);
                  }}
                >
                  {item.name}
                  <X className="ml-1 h-3 w-3 cursor-pointer" />
                </Badge>
              ))
            ) : (
              <span className="text-muted-foreground text-sm">
                {placeholder}
              </span>
            )}
          </div>
          <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
        <Command shouldFilter={false}>
          {" "}
          {/* Disable default filtering */}
          <CommandInput
            placeholder={searchPlaceholder}
            value={searchTerm}
            onValueChange={setSearchTerm}
            disabled={isLoading}
          />
          <CommandList>
            <ScrollArea className="max-h-[200px]">
              {" "}
              {/* Add ScrollArea */}
              <CommandEmpty>
                {showCreateOption ? " " : emptyPlaceholder}{" "}
                {/* Hide default empty when create is shown */}
              </CommandEmpty>
              <CommandGroup>
                {filteredOptions.map((option) => (
                  <CommandItem
                    key={option.id}
                    value={option.name} // Use name for Command's internal value/search
                    onSelect={() => {
                      handleSelect(option.id);
                      setOpen(true); // Keep popover open after selection
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selected.includes(option.id)
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                    {option.name}
                  </CommandItem>
                ))}
              </CommandGroup>
              {showCreateOption && (
                <>
                  <CommandSeparator />
                  <CommandGroup>
                    <CommandItem
                      value={searchTerm}
                      onSelect={handleCreate}
                      className="text-muted-foreground cursor-pointer"
                    >
                      <PlusCircle className="mr-2 h-4 w-4" />
                      {createPlaceholder.replace("{value}", searchTerm)}
                    </CommandItem>
                  </CommandGroup>
                </>
              )}
            </ScrollArea>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
