/**
 * Public API Keys Configuration
 *
 * This file contains hardcoded public keys that are safe to expose in the client.
 * These keys have built-in security restrictions (RLS policies, OAuth scopes, etc.)
 * and do not provide admin access to the database.
 *
 * SECURITY NOTE:
 * - Supabase anon key: Protected by Row Level Security (RLS) policies
 * - Google OAuth keys: Restricted to specific domains and scopes
 * - Supabase URL: Public endpoint with no sensitive data
 *
 * DO NOT add admin keys, service role keys, or any sensitive credentials here.
 */

// Supabase Configuration - Hardcoded Public Keys
export const SUPABASE_CONFIG = {
  url: 'https://rnjolcoecogzgglnblqn.supabase.co',
  anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o',
} as const;

// Google OAuth Configuration - Environment-specific
export const GOOGLE_OAUTH_CONFIG = {
  iosClientId: '110991972471-9g5hbuj78s88b5fi0m97i4k0823v7430.apps.googleusercontent.com',
  webClientId: '110991972471-ek9ra016ca12ucaobil2s8oid86k6to4.apps.googleusercontent.com',
  // Android Client ID - Environment specific (development vs production)
  androidClientId: __DEV__
    ? '110991972471-jskkg6qg8g33mk1qrv9el5u0bj0f8cql.apps.googleusercontent.com' // Development
    : '110991972471-bauq8cmll9nlrdl6ok7svvt5rgfuhno7.apps.googleusercontent.com', // Production
} as const;

// App Configuration - Environment-specific
export const APP_CONFIG = {
  environment: __DEV__ ? 'development' : 'production',
  enableCrashReporting: !__DEV__, // Enable crash reporting in production only
  notificationMode: __DEV__ ? 'development' : 'production',
  privacyPolicyUrl: 'https://dukancard.in/privacy',
} as const;

// Next.js Backend Configuration
export const BACKEND_CONFIG = {
  // This will be the base URL for API calls to Next.js backend
  // In development: http://localhost:3000
  // In production: https://dukancard.in
  baseUrl: __DEV__ ? 'http://localhost:3000' : 'https://dukancard.in',
  apiPrefix: '/api', // Use unified API routes with JWT authentication
  // No hardcoded API keys - using secure JWT authentication instead
} as const;

// Type exports for better TypeScript support
export type SupabaseConfig = typeof SUPABASE_CONFIG;
export type GoogleOAuthConfig = typeof GOOGLE_OAUTH_CONFIG;
export type AppConfig = typeof APP_CONFIG;
export type BackendConfig = typeof BACKEND_CONFIG;
