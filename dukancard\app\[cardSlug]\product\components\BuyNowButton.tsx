"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ShoppingCart } from "lucide-react";
import { useState } from "react";

export default function BuyNowButton() {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div className="relative group">
      {/* Button glow effect */}
      <div className="absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/20 rounded-xl blur-md opacity-75 group-hover:opacity-100 transition-opacity duration-300"></div>

      <Button
        className="relative w-full bg-gradient-to-br from-[var(--brand-gold)] to-[var(--brand-gold-dark)] hover:from-[var(--brand-gold-dark)] hover:to-[var(--brand-gold)] text-[var(--brand-gold-foreground)] font-medium flex items-center justify-center gap-3 py-6 cursor-not-allowed opacity-90 rounded-xl border border-[var(--brand-gold)]/20 transition-all duration-300"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        disabled
      >
        {/* Shimmer effect */}
        <div className="absolute inset-0 w-full h-full overflow-hidden rounded-xl">
          <div className="absolute inset-0 w-[200%] h-full bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer"></div>
        </div>

        <ShoppingCart className={`w-5 h-5 ${isHovered ? 'animate-pulse' : ''}`} />
        <span className="text-base font-semibold tracking-wide">Buy Now</span>
      </Button>
    </div>
  );
}
