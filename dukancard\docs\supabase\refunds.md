# Refunds Table Documentation

## Table Overview

The `refunds` table in the Dukancard application tracks refund requests and their processing status for payments made through the platform. It provides a comprehensive record of refund transactions, enabling tracking, management, and reporting of refund activities.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key for the refund record |
| user_id | uuid | NO | | Foreign key to users.id, the user who requested the refund |
| payment_id | text | NO | | ID of the original payment being refunded (from payment gateway) |
| refund_id | text | YES | | ID of the refund transaction (from payment gateway) |
| amount | integer | NO | | Amount to be refunded (in smallest currency unit, e.g., paise) |
| currency | text | NO | 'INR' | Currency code (default: Indian Rupee) |
| status | text | NO | 'requested' | Current status of the refund |
| speed_requested | text | NO | 'normal' | Requested processing speed ('normal' or 'optimum') |
| speed_processed | text | YES | | Actual processing speed used by the payment gateway |
| requested_at | timestamptz | NO | now() | Timestamp when the refund was requested |
| processed_at | timestamptz | YES | | Timestamp when the refund was processed by the payment gateway |
| notes | jsonb | YES | | Additional notes or metadata about the refund |
| created_at | timestamptz | NO | now() | Timestamp when the record was created |
| updated_at | timestamptz | NO | now() | Timestamp when the record was last updated |
| is_latest_attempt | boolean | YES | false | Flag indicating if this is the latest refund attempt for the payment |

## Constraints

### Primary Key
- `refunds_pkey` - Primary key constraint on the `id` column

### Foreign Keys
- `refunds_user_id_fkey` - Foreign key constraint linking `user_id` to `users.id`

### Unique Constraints
- `refunds_refund_id_unique` - Ensures refund_id is unique when provided

### Not Null Constraints
Multiple check constraints ensure that required fields are not null.

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| refunds_pkey | UNIQUE | id | Primary key index |
| refunds_refund_id_unique | UNIQUE | refund_id | Unique constraint index for refund_id |
| idx_refunds_user_id | BTREE | user_id | Index for faster user lookups |
| idx_refunds_payment_id | BTREE | payment_id | Index for faster payment lookups |
| idx_refunds_refund_id | BTREE | refund_id | Index for faster refund ID lookups |
| idx_refunds_status | BTREE | status | Index for filtering by status |

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| Users can view their own refunds | SELECT | (auth.uid() = user_id) | |
| Users can insert their own refunds | INSERT | | |
| Users can update their own refunds | UPDATE | (auth.uid() = user_id) | |
| Users can delete their own refunds | DELETE | (auth.uid() = user_id) | |

These policies ensure that users can only access and manage their own refund records.

## Related Tables

### users
The `users` table is referenced by the `user_id` foreign key and contains information about the user who requested the refund.

## Usage Notes

1. **Refund Workflow**:
   - The table tracks the complete lifecycle of a refund request
   - Initial status is 'requested' when a user initiates a refund
   - Status is updated as the refund progresses through the payment gateway
   - Common status values might include 'requested', 'processing', 'completed', 'failed'

2. **Payment Integration**:
   - The table integrates with Razorpay payment gateway
   - The `payment_id` field references the original payment in Razorpay
   - The `refund_id` field stores the ID assigned by Razorpay when the refund is processed
   - Webhook events likely update the refund status and add the refund_id when processed

3. **Refund Processing Speeds**:
   - The `speed_requested` field indicates the desired processing speed
   - 'normal' is the default speed, likely with standard processing time
   - 'optimum' might be a faster processing option, possibly with different fees
   - The `speed_processed` records the actual speed used by the payment gateway

4. **Multiple Refund Attempts**:
   - The `is_latest_attempt` flag suggests that multiple refund attempts might be tracked
   - This allows for a history of refund attempts while identifying the most recent one
   - Useful for handling cases where initial refund attempts fail

5. **Security**:
   - RLS policies ensure users can only view and manage their own refunds
   - This prevents unauthorized access to sensitive financial information
   - The INSERT policy allows users to create refund requests, but they can only affect their own records

6. **Audit Trail**:
   - The `requested_at` and `processed_at` timestamps provide an audit trail
   - The `notes` field can store additional context or reasons for the refund
   - The `created_at` and `updated_at` fields track record changes

7. **Performance Considerations**:
   - Multiple indexes support efficient querying by user, payment, refund ID, and status
   - These optimize common query patterns for refund management and reporting

8. **Financial Tracking**:
   - The `amount` field stores the refund amount in the smallest currency unit (paise for INR)
   - This avoids floating-point precision issues with financial calculations
   - The `currency` field defaults to 'INR' but allows for other currencies if needed
