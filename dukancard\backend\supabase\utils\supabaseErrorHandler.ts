import { AuthError } from "@supabase/supabase-js";

/**
 * User-friendly error messages for Supabase auth error codes
 */
const AUTH_ERROR_MESSAGES: Record<string, string> = {
  // Rate limiting errors
  over_email_send_rate_limit: "Email rate limit exceeded. Please wait before requesting another OTP.",
  over_request_rate_limit: "Too many requests. Please wait a few minutes before trying again.",
  over_sms_send_rate_limit: "Too many SMS messages sent. Please wait before requesting another OTP.",

  // OTP related errors
  otp_expired: "OTP has expired. Please request a new one.",
  otp_disabled: "OTP authentication is currently disabled.",

  // Token/JWT errors
  bad_jwt: "Invalid authentication token. Please sign in again.",
  session_expired: "Your session has expired. Please sign in again.",
  session_not_found: "Session not found. Please sign in again.",
  refresh_token_not_found: "Authentication expired. Please sign in again.",
  refresh_token_already_used: "Authentication expired. Please sign in again.",

  // User/account errors
  user_not_found: "User account not found.",
  user_banned: "Your account has been temporarily suspended.",
  email_not_confirmed: "Please verify your email address before signing in.",
  phone_not_confirmed: "Please verify your phone number before signing in.",
  invalid_credentials: "Invalid email or password.",

  // Signup/registration errors
  signup_disabled: "New account registration is currently disabled.",
  email_exists: "An account with this email already exists.",
  phone_exists: "An account with this phone number already exists.",
  weak_password: "Password does not meet security requirements.",
  email_address_invalid: "Please enter a valid email address.",
  email_address_not_authorized: "This email address is not authorized for registration.",

  // Provider/OAuth errors
  provider_disabled: "This sign-in method is currently disabled.",
  oauth_provider_not_supported: "This sign-in provider is not supported.",
  provider_email_needs_verification: "Please verify your email address to complete sign-in.",

  // Validation errors
  validation_failed: "Please check your input and try again.",
  bad_json: "Invalid request format. Please try again.",

  // MFA errors
  mfa_challenge_expired: "MFA challenge expired. Please try again.",
  mfa_verification_failed: "Invalid MFA code. Please try again.",
  insufficient_aal: "Additional authentication required.",

  // CAPTCHA errors
  captcha_failed: "CAPTCHA verification failed. Please try again.",

  // General errors
  conflict: "A conflict occurred. Please try again.",
  request_timeout: "Request timed out. Please try again.",
  unexpected_failure: "An unexpected error occurred. Please try again.",
  same_password: "New password must be different from your current password.",

  // Flow state errors
  flow_state_expired: "Authentication session expired. Please start over.",
  flow_state_not_found: "Authentication session not found. Please start over.",

  // Reauthentication errors
  reauthentication_needed: "Please verify your identity to continue.",
  reauthentication_not_valid: "Identity verification failed. Please try again.",
};

/**
 * Default error message for unknown error codes
 */
const DEFAULT_ERROR_MESSAGE = "An error occurred. Please try again.";

/**
 * Handles Supabase auth errors and returns user-friendly messages
 * @param error - The error object from Supabase
 * @returns User-friendly error message
 */
export function handleSupabaseAuthError(error: AuthError | Error | null): string {
  if (!error) {
    return DEFAULT_ERROR_MESSAGE;
  }

  // Check if it's an AuthError with a code property
  if ('code' in error && error.code) {
    const userMessage = AUTH_ERROR_MESSAGES[error.code];
    if (userMessage) {
      return userMessage;
    }
  }

  // Check if it's an AuthError with a message we can parse for specific cases
  if (error.message) {
    const message = error.message.toLowerCase();
    
    // Handle some common message patterns that might not have specific codes
    if (message.includes('token has expired') || message.includes('expired')) {
      return "Your session has expired. Please sign in again.";
    }
    
    if (message.includes('invalid token') || message.includes('invalid otp')) {
      return "Invalid code. Please check and try again.";
    }
    
    if (message.includes('rate limit') || message.includes('too many')) {
      return "Too many attempts. Please wait before trying again.";
    }
    
    if (message.includes('email already exists') || message.includes('user already exists')) {
      return "An account with this email already exists.";
    }
    
    if (message.includes('invalid email')) {
      return "Please enter a valid email address.";
    }
    
    if (message.includes('weak password')) {
      return "Password does not meet security requirements.";
    }
  }

  // Return default message for unknown errors
  return DEFAULT_ERROR_MESSAGE;
}

/**
 * Checks if an error is a rate limit error
 * @param error - The error object from Supabase
 * @returns True if it's a rate limit error
 */
export function isRateLimitError(error: AuthError | Error | null): boolean {
  if (!error) return false;

  if ('code' in error && error.code) {
    return [
      'over_email_send_rate_limit',
      'over_request_rate_limit',
      'over_sms_send_rate_limit'
    ].includes(error.code);
  }

  return false;
}

/**
 * Checks if an error is specifically an email rate limit error
 * This indicates Supabase is trying to send verification emails instead of OTP
 * @param error - The error object from Supabase
 * @returns True if it's an email rate limit error
 */
export function isEmailRateLimitError(error: AuthError | Error | null): boolean {
  if (!error) return false;

  if ('code' in error && error.code) {
    return error.code === 'over_email_send_rate_limit';
  }

  return false;
}

/**
 * Checks if an error is a temporary/retryable error
 * @param error - The error object from Supabase
 * @returns True if the error is temporary and user should retry
 */
export function isTemporaryError(error: AuthError | Error | null): boolean {
  if (!error) return false;
  
  if ('code' in error && error.code) {
    return [
      'over_email_send_rate_limit',
      'over_request_rate_limit',
      'over_sms_send_rate_limit',
      'request_timeout',
      'conflict',
      'unexpected_failure'
    ].includes(error.code);
  }
  
  return false;
}

/**
 * Gets the retry delay in seconds for rate limit errors
 * @param error - The error object from Supabase
 * @returns Suggested retry delay in seconds, or null if not applicable
 */
export function getRetryDelay(error: AuthError | Error | null): number | null {
  if (!error || !isRateLimitError(error)) return null;
  
  if ('code' in error && error.code) {
    switch (error.code) {
      case 'over_email_send_rate_limit':
        return 60; // 1 minute for email rate limits
      case 'over_sms_send_rate_limit':
        return 60; // 1 minute for SMS rate limits
      case 'over_request_rate_limit':
        return 300; // 5 minutes for general rate limits
      default:
        return 60;
    }
  }
  
  return null;
}
