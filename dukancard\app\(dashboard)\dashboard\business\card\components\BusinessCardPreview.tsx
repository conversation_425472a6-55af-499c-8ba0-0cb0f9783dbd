"use client";

import { useMemo } from "react";
import { motion } from "framer-motion";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { BusinessCardData, defaultBusinessCardData } from "../schema";
import QRCode from "react-qr-code";
import { cn } from "@/lib/utils";
import {
  Phone,
  ShoppingBag,
  Star,
  Heart,
  UserPlus,
  QrCode, // Added for fake QR code in demo mode
} from "lucide-react";
import WhatsAppIcon from "@/app/components/icons/WhatsAppIcon";
import InstagramIcon from "@/app/components/icons/InstagramIcon";
import FacebookIcon from "@/app/components/icons/FacebookIcon";
import { ProductServiceData } from "@/backend/supabase/services/products/types";
import {
  maskEmail,
  maskPhoneNumber,
  formatIndianNumberShort,
} from "@/lib/utils";
import {
  formatWhatsAppUrl,
  formatTelUrl,
  formatPrice,
} from "./utils/cardUtils";
import {
  generateCustomBrandingStyles,
  getPrimaryThemeColor,
} from "@/lib/utils/customBranding";
import CardBackgroundEffects from "./CardBackgroundEffects";
import CardCornerDecorations from "./CardCornerDecorations";
import CardGlowEffects from "./CardGlowEffects";
import CardHeader from "./CardHeader";
import CardProfile from "./CardProfile";
import CardDivider from "./CardDivider";
import CardBusinessInfo from "./CardBusinessInfo";


type LogoUploadStatus = "idle" | "uploading" | "success" | "error";

interface BusinessCardPreviewProps {
  data: BusinessCardData & {
    products_services?: ProductServiceData[];
  };
  totalLikes?: number;
  totalSubscriptions?: number;
  averageRating?: number;
  isSubscribed?: boolean;
  hasLiked?: boolean;
  isLoadingInteraction?: boolean;
  userPlan?: "free" | "basic" | "growth" | "pro" | "enterprise" | "trial";
  logoUploadStatus?: LogoUploadStatus;
  localPreviewUrl?: string | null;
  isAuthenticated?: boolean;
  isCurrentUserBusiness?: boolean;
  isDemo?: boolean; // New prop to indicate if this is a demo card for homepage
}

export default function BusinessCardPreview({
  data,
  userPlan,
  logoUploadStatus = "idle",
  localPreviewUrl = null,
  isAuthenticated = false,
  totalLikes = 0,
  totalSubscriptions = 0,
  averageRating = 0,
  isDemo = false, // New prop with default value
}: BusinessCardPreviewProps) {
  const {
    logo_url = data.logo_url ?? defaultBusinessCardData.logo_url,
    member_name = defaultBusinessCardData.member_name || "Your Name",
    business_name = defaultBusinessCardData.business_name ||
      "Your Business Name",
    about_bio = defaultBusinessCardData.about_bio,
    address_line = defaultBusinessCardData.address_line,
    locality = data.locality ?? defaultBusinessCardData.locality,
    city = defaultBusinessCardData.city,
    state = defaultBusinessCardData.state,
    established_year = data.established_year ?? defaultBusinessCardData.established_year,
    pincode = defaultBusinessCardData.pincode,
    phone = defaultBusinessCardData.phone,
    instagram_url = defaultBusinessCardData.instagram_url,
    facebook_url = defaultBusinessCardData.facebook_url,
    whatsapp_number = defaultBusinessCardData.whatsapp_number,
    theme_color = data.theme_color,
    business_hours = data.business_hours,
    delivery_info = data.delivery_info,
    business_slug = defaultBusinessCardData.business_slug || "",
    products_services = [],
    contact_email = defaultBusinessCardData.contact_email,
    title = data.title || "",
  } = data;

  const whatsappUrl = formatWhatsAppUrl(whatsapp_number);
  const telUrl = formatTelUrl(phone);
  const mailtoUrl = contact_email ? `mailto:${contact_email}` : undefined;

  const fullAddress = [address_line, locality, city, state, pincode]
    .filter(Boolean)
    .join(", ");

  const displayPhone = isAuthenticated ? phone : maskPhoneNumber(phone);
  const displayEmail = isAuthenticated
    ? contact_email
    : maskEmail(contact_email);
  // Always show address_line regardless of authentication status
  const displayAddressLine = address_line?.trim() || "";
  const displayCityStatePin = `${city || ""}, ${state || ""} ${
    pincode ? `- ${pincode}` : ""
  }`.trim();

  const qrValue = business_slug
    ? `https://dukancard.in/${business_slug}`
    : null;
  const qrDisplayUrl = business_slug
    ? `dukancard.in/${business_slug}`
    : "Set Slug to activate";

  // Updated theme color logic - use custom branding colors if available and user is Pro/Enterprise
  const finalThemeColor = useMemo(() => {
    return getPrimaryThemeColor(userPlan, data.custom_branding, theme_color);
  }, [theme_color, userPlan, data.custom_branding]);

  // Generate card styles with custom branding support
  const cardStyle = useMemo(() => {
    return generateCustomBrandingStyles(userPlan, data.custom_branding, theme_color);
  }, [userPlan, data.custom_branding, theme_color]);

  return (
    <motion.div
      data-card-element
      className={cn(`
        relative w-full max-w-sm
        rounded-xl overflow-hidden
        transition-all duration-500

        bg-gradient-to-br from-neutral-100 to-white dark:from-neutral-900 dark:to-neutral-950
        shadow-xl
        transform-gpu
        border-0
      `)}
      style={cardStyle}
    >
      <CardBackgroundEffects finalThemeColor={finalThemeColor} />

      <CardCornerDecorations />

      {/* Content container */}
      <div className="relative p-3 xs:p-4 sm:p-5 flex flex-col justify-between text-neutral-800 dark:text-white z-10">
        <CardHeader
          userPlan={userPlan}
          establishedYear={established_year}
          customBranding={data.custom_branding}
        />

        {/* Floating interaction buttons are rendered at the bottom */}

        {/* Main content - Profile section */}
        <div className="mt-1">
          <CardProfile
            logo_url={logo_url}
            localPreviewUrl={localPreviewUrl}
            logoUploadStatus={logoUploadStatus}
            member_name={member_name}
            business_name={business_name}
            title={title}
            about_bio={about_bio}
            finalThemeColor={finalThemeColor}
          />

          <CardDivider />

          <CardBusinessInfo
            fullAddress={fullAddress}
            displayAddressLine={displayAddressLine}
            locality={locality}
            displayCityStatePin={displayCityStatePin}
            phone={phone}
            displayPhone={displayPhone}
            isAuthenticated={isAuthenticated}
            telUrl={telUrl}
            displayEmail={displayEmail}
            mailtoUrl={mailtoUrl}
            business_hours={business_hours}
            delivery_info={delivery_info}
          />

          {/* Products & Services section - Full width */}
          {products_services && products_services.length > 0 && (
            <div className="mt-3 max-w-xs mx-auto">
              <div className="flex items-center text-xs uppercase font-bold tracking-wider text-[--theme-color] mb-2 justify-center">
                <ShoppingBag
                  className="w-3 h-3 mr-1.5"
                  color={finalThemeColor}
                />
                Products & Services
              </div>

              <div className="space-y-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 p-2.5 rounded-lg">
                {products_services.slice(0, 3).map((item) => (
                  <div
                    key={item.id}
                    className="text-neutral-700 dark:text-neutral-200"
                  >
                    <div className="flex justify-between items-baseline gap-2">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="font-medium text-xs truncate cursor-default">
                              {item.name}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{item.name}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <span className="text-xs font-bold bg-[var(--theme-color-10)] dark:bg-[var(--theme-color-20)] py-0.5 px-2 rounded-full flex-shrink-0">
                        {formatPrice(item.base_price)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* QR Code section */}
          <div className="mt-3 max-w-xs mx-auto">
            {isDemo ? (
              // Demo mode - show fake QR code
              <div className="flex items-center justify-between gap-3 bg-neutral-800/5 dark:bg-neutral-300/10 p-2.5 rounded-lg border border-neutral-300/60 dark:border-neutral-700/60">
                <div className="flex-1 min-w-0">
                  <p className="text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1">
                    Scan for Dukancard Profile
                  </p>
                  <p className="text-xs font-mono text-[--theme-color] font-semibold line-clamp-1 cursor-default">
                    dukancard.in/demo-business
                  </p>
                </div>
                <div className="bg-white p-1.5 rounded-lg shadow-md flex-shrink-0">
                  <QrCode
                    className="w-14 h-14 text-neutral-800"
                    strokeWidth={1.5}
                  />
                </div>
              </div>
            ) : qrValue ? (
              // Real QR code for actual business cards
              <div className="flex items-center justify-between gap-3 bg-neutral-800/5 dark:bg-neutral-300/10 p-2.5 rounded-lg border border-neutral-300/60 dark:border-neutral-700/60">
                <div className="flex-1 min-w-0">
                  <p className="text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1">
                    Scan for Dukancard Profile
                  </p>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <p className="text-xs font-mono text-[--theme-color] font-semibold line-clamp-1 cursor-default">
                          {qrDisplayUrl}
                        </p>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{qrDisplayUrl}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <div
                  id="business-card-qrcode"
                  className="bg-white p-1.5 rounded-lg shadow-md flex-shrink-0"
                >
                  <QRCode
                    value={qrValue}
                    size={60}
                    level="M"
                    bgColor="#FFFFFF"
                    fgColor="#000000"
                  />
                </div>
              </div>
            ) : (
              // Placeholder for when no slug is set
              <div className="flex items-center justify-between gap-3 bg-neutral-800/5 dark:bg-neutral-300/10 p-2.5 rounded-lg border border-neutral-300/60 dark:border-neutral-700/60">
                <div className="flex-1 min-w-0">
                  <p className="text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1">
                    Scan for Dukancard Profile
                  </p>
                  <p className="text-xs font-mono text-neutral-500 dark:text-neutral-500 line-clamp-1">
                    {qrDisplayUrl}
                  </p>
                </div>
                <div className="bg-white p-1.5 rounded-lg shadow-md opacity-50 flex-shrink-0">
                  <svg
                    className="w-14 h-14 text-[--theme-color]"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"
                    />
                  </svg>
                </div>
              </div>
            )}
          </div>

          {/* Interaction metrics */}
          <div className="flex justify-center items-center gap-2 sm:gap-4 text-xs text-neutral-500 dark:text-neutral-400 mt-3 mb-2 flex-wrap">
            <div className="flex items-center gap-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 py-1.5 px-3 rounded-full">
              <Heart className="w-4 h-4 text-red-500" />
              <span className="font-medium">
                {formatIndianNumberShort(totalLikes)}
              </span>
            </div>
            <div className="flex items-center gap-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 py-1.5 px-3 rounded-full">
              <UserPlus className="w-4 h-4 text-blue-500" />
              <span className="font-medium">
                {formatIndianNumberShort(totalSubscriptions)}
              </span>
            </div>
            <div className="flex items-center gap-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 py-1.5 px-3 rounded-full">
              <Star className="w-4 h-4 text-amber-500 fill-current" />
              <span className="font-medium">{averageRating.toFixed(1)}</span>
            </div>
          </div>

          {/* Interaction buttons have been moved outside the card */}
        </div>

        {/* Social Links */}
        <div className="pt-3 pb-2">
          <TooltipProvider>
            <div className="flex justify-center items-center space-x-2">
              {instagram_url && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    {isDemo ? (
                      // Demo mode - non-clickable button
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default">
                        <InstagramIcon className="w-4 h-4 text-[--theme-color]" />
                      </div>
                    ) : (
                      // Real mode - clickable link
                      <a
                        href={instagram_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all hover:scale-110 hover:shadow-md"
                      >
                        <InstagramIcon className="w-4 h-4 text-[--theme-color]" />
                      </a>
                    )}
                  </TooltipTrigger>
                  <TooltipContent className="bg-neutral-800 text-xs text-white border-neutral-700">
                    {isDemo ? "Demo Instagram Button" : "Instagram"}
                  </TooltipContent>
                </Tooltip>
              )}

              {facebook_url && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    {isDemo ? (
                      // Demo mode - non-clickable button
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default">
                        <FacebookIcon className="w-4 h-4 text-[--theme-color]" />
                      </div>
                    ) : (
                      // Real mode - clickable link
                      <a
                        href={facebook_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all hover:scale-110 hover:shadow-md"
                      >
                        <FacebookIcon className="w-4 h-4 text-[--theme-color]" />
                      </a>
                    )}
                  </TooltipTrigger>
                  <TooltipContent className="bg-neutral-800 text-xs text-white border-neutral-700">
                    {isDemo ? "Demo Facebook Button" : "Facebook"}
                  </TooltipContent>
                </Tooltip>
              )}

              {whatsappUrl && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    {isDemo ? (
                      // Demo mode - non-clickable button
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default">
                        <WhatsAppIcon className="w-4 h-4 text-[--theme-color]" />
                      </div>
                    ) : (
                      // Real mode - clickable link
                      <a
                        href={whatsappUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all hover:scale-110 hover:shadow-md"
                      >
                        <WhatsAppIcon className="w-4 h-4 text-[--theme-color]" />
                      </a>
                    )}
                  </TooltipTrigger>
                  <TooltipContent className="bg-neutral-800 text-xs text-white border-neutral-700">
                    {isDemo ? "Demo WhatsApp Button" : "Chat on WhatsApp"}
                  </TooltipContent>
                </Tooltip>
              )}

              {displayPhone && telUrl && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    {isDemo ? (
                      // Demo mode - non-clickable button
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default">
                        <Phone className="w-4 h-4" color={finalThemeColor} />
                      </div>
                    ) : (
                      // Real mode - clickable or non-clickable based on authentication
                      <a
                        href={isAuthenticated ? telUrl : "#"}
                        className={`w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all ${
                          isAuthenticated
                            ? "hover:scale-110 hover:shadow-md"
                            : "cursor-default opacity-70"
                        }`}
                      >
                        <Phone className="w-4 h-4" color={finalThemeColor} />
                      </a>
                    )}
                  </TooltipTrigger>
                  <TooltipContent className="bg-neutral-800 text-xs text-white border-neutral-700">
                    {isDemo ? "Demo Call Button" : "Call directly"}
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
          </TooltipProvider>
        </div>

        {/* Bottom accent bar */}
        <div
          className="absolute bottom-0 left-0 right-0 h-1.5 mt-2"
          style={{
            background: `linear-gradient(to right, var(--theme-color), var(--theme-accent-end), var(--theme-color))`,
          }}
        ></div>
      </div>

      <CardGlowEffects />
    </motion.div>
  );
}