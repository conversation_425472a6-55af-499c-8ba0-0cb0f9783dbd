import { createClient } from '../../../../../utils/supabase/server';
import { createAdminClient } from '../../../../../utils/supabase/admin';
import { NextRequest, NextResponse } from 'next/server';

// Define interfaces for the expected data structure
interface BusinessProfileDataForReview {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
}

interface ReviewWithProfile {
  id: string;
  rating: number;
  review_text: string | null;
  created_at: string;
  updated_at: string;
  business_profile_id: string;
  user_id: string;
  business_profiles: BusinessProfileDataForReview | null;
}



// Constants
const REVIEWS_PER_PAGE = 10;

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Get user authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const sortBy = searchParams.get('sort') || 'newest';

    // For business-given reviews, we don't need to check the business profile
    // since we're filtering by user_id directly

    // Calculate pagination
    const from = (page - 1) * REVIEWS_PER_PAGE;
    const to = from + REVIEWS_PER_PAGE - 1;

    // Simple query without join for better performance
    let baseQuery = supabase
      .from('ratings_reviews')
      .select(`
        id,
        rating,
        review_text,
        created_at,
        updated_at,
        business_profile_id,
        user_id
      `, { count: 'exact' })
      .eq('user_id', user.id);

    // Apply sorting to the base query
    switch (sortBy) {
      case "oldest":
        baseQuery = baseQuery.order("created_at", { ascending: true });
        break;
      case "highest_rating":
        baseQuery = baseQuery.order("rating", { ascending: false });
        break;
      case "lowest_rating":
        baseQuery = baseQuery.order("rating", { ascending: true });
        break;
      case "newest":
      default:
        baseQuery = baseQuery.order("created_at", { ascending: false });
        break;
    }

    // Get count first (without pagination)
    const { count: totalCount, error: countError } = await baseQuery;

    if (countError) {
      return NextResponse.json(
        { error: 'Failed to count reviews' },
        { status: 500 }
      );
    }

    // Now get the actual data with pagination
    const { data: reviews, error: reviewsError } = await baseQuery.range(from, to);

    if (reviewsError) {
      return NextResponse.json(
        { error: 'Failed to fetch reviews' },
        { status: 500 }
      );
    }

    // Process the reviews - fetch business profiles separately for better performance
    let typedReviews: ReviewWithProfile[] = [];

    if (reviews && reviews.length > 0) {
      // Fetch business profiles separately for the paginated reviews only
      const businessProfileIds = reviews.map(review => review.business_profile_id);
      const supabaseAdmin = createAdminClient();
      const { data: businessProfiles } = await supabaseAdmin
        .from('business_profiles')
        .select('id, business_name, business_slug, logo_url')
        .in('id', businessProfileIds);

      typedReviews = reviews.map(review => {
        const businessProfile = businessProfiles?.find(profile => profile.id === review.business_profile_id) || null;
        return {
          ...review,
          business_profiles: businessProfile
        };
      });
    }

    // Calculate total pages
    const totalPages = Math.ceil((totalCount || 0) / REVIEWS_PER_PAGE);

    return NextResponse.json({
      reviews: typedReviews,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        perPage: REVIEWS_PER_PAGE
      }
    });

  } catch (_error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
