  Context android.content  cacheDir android.content.Context  Bitmap android.graphics  Matrix android.graphics  CompressFormat android.graphics.Bitmap  allocationByteCount android.graphics.Bitmap  compress android.graphics.Bitmap  createBitmap android.graphics.Bitmap  createScaledBitmap android.graphics.Bitmap  height android.graphics.Bitmap  width android.graphics.Bitmap  JPEG &android.graphics.Bitmap.CompressFormat  PNG &android.graphics.Bitmap.CompressFormat  WEBP &android.graphics.Bitmap.CompressFormat  FlipType android.graphics.Matrix  apply android.graphics.Matrix  flipType android.graphics.Matrix  
postRotate android.graphics.Matrix  	postScale android.graphics.Matrix  rotation android.graphics.Matrix  BitmapDrawable android.graphics.drawable  Drawable android.graphics.drawable  bitmap (android.graphics.drawable.BitmapDrawable  Uri android.net  fromFile android.net.Uri  toString android.net.Uri  Base64 android.util  NO_WRAP android.util.Base64  encodeToString android.util.Base64  Base64 expo.modules.imagemanipulator  Bitmap expo.modules.imagemanipulator  BitmapDrawable expo.modules.imagemanipulator  Boolean expo.modules.imagemanipulator  ByteArrayOutputStream expo.modules.imagemanipulator  CodedException expo.modules.imagemanipulator  Context expo.modules.imagemanipulator  	Coroutine expo.modules.imagemanipulator  CoroutineScope expo.modules.imagemanipulator  CropRect expo.modules.imagemanipulator  CropTransformer expo.modules.imagemanipulator  DecoratedException expo.modules.imagemanipulator  Deferred expo.modules.imagemanipulator  Double expo.modules.imagemanipulator  Drawable expo.modules.imagemanipulator  
EitherOfThree expo.modules.imagemanipulator  
EitherType expo.modules.imagemanipulator  
Enumerable expo.modules.imagemanipulator  
Exceptions expo.modules.imagemanipulator  Field expo.modules.imagemanipulator  File expo.modules.imagemanipulator  FileOutputStream expo.modules.imagemanipulator  	FileUtils expo.modules.imagemanipulator  FlipTransformer expo.modules.imagemanipulator  FlipType expo.modules.imagemanipulator  Float expo.modules.imagemanipulator  IOException expo.modules.imagemanipulator  ImageFormat expo.modules.imagemanipulator  ImageInvalidCropException expo.modules.imagemanipulator  ImageLoaderNotFoundException expo.modules.imagemanipulator  ImageLoadingFailedException expo.modules.imagemanipulator  ImageManipulatorContext expo.modules.imagemanipulator  ImageManipulatorModule expo.modules.imagemanipulator  ImageRef expo.modules.imagemanipulator  ImageTransformer expo.modules.imagemanipulator  ImageWriteFailedException expo.modules.imagemanipulator  Int expo.modules.imagemanipulator  JPEG expo.modules.imagemanipulator  JPG expo.modules.imagemanipulator  ManipulateOptions expo.modules.imagemanipulator  ManipulatorResult expo.modules.imagemanipulator  ManipulatorTask expo.modules.imagemanipulator  Module expo.modules.imagemanipulator  OptIn expo.modules.imagemanipulator  PNG expo.modules.imagemanipulator  Record expo.modules.imagemanipulator  
ResizeOptions expo.modules.imagemanipulator  ResizeTransformer expo.modules.imagemanipulator  ResultListener expo.modules.imagemanipulator  RotateTransformer expo.modules.imagemanipulator  RuntimeContext expo.modules.imagemanipulator  SharedObject expo.modules.imagemanipulator  	SharedRef expo.modules.imagemanipulator  String expo.modules.imagemanipulator  	Throwable expo.modules.imagemanipulator  Throws expo.modules.imagemanipulator  UUID expo.modules.imagemanipulator  Uri expo.modules.imagemanipulator  WEBP expo.modules.imagemanipulator  
appContext expo.modules.imagemanipulator  apply expo.modules.imagemanipulator  async expo.modules.imagemanipulator  context expo.modules.imagemanipulator  createManipulatorContext expo.modules.imagemanipulator  generateRandomOutputPath expo.modules.imagemanipulator  loader expo.modules.imagemanipulator  mapOf expo.modules.imagemanipulator  requireNotNull expo.modules.imagemanipulator  resume expo.modules.imagemanipulator  resumeWithException expo.modules.imagemanipulator  runtimeContext expo.modules.imagemanipulator  suspend expo.modules.imagemanipulator  suspendCancellableCoroutine expo.modules.imagemanipulator  to expo.modules.imagemanipulator  toCodedException expo.modules.imagemanipulator  toKClass expo.modules.imagemanipulator  use expo.modules.imagemanipulator  CompressFormat $expo.modules.imagemanipulator.Bitmap  height &expo.modules.imagemanipulator.CropRect  originX &expo.modules.imagemanipulator.CropRect  originY &expo.modules.imagemanipulator.CropRect  width &expo.modules.imagemanipulator.CropRect  File 'expo.modules.imagemanipulator.FileUtils  IOException 'expo.modules.imagemanipulator.FileUtils  ImageWriteFailedException 'expo.modules.imagemanipulator.FileUtils  UUID 'expo.modules.imagemanipulator.FileUtils  ensureDirExists 'expo.modules.imagemanipulator.FileUtils  generateRandomOutputPath 'expo.modules.imagemanipulator.FileUtils  
HORIZONTAL &expo.modules.imagemanipulator.FlipType  VERTICAL &expo.modules.imagemanipulator.FlipType  Bitmap )expo.modules.imagemanipulator.ImageFormat  JPEG )expo.modules.imagemanipulator.ImageFormat  JPG )expo.modules.imagemanipulator.ImageFormat  PNG )expo.modules.imagemanipulator.ImageFormat  WEBP )expo.modules.imagemanipulator.ImageFormat  compressFormat )expo.modules.imagemanipulator.ImageFormat  
fileExtension )expo.modules.imagemanipulator.ImageFormat  addTransformer 5expo.modules.imagemanipulator.ImageManipulatorContext  apply 5expo.modules.imagemanipulator.ImageManipulatorContext  render 5expo.modules.imagemanipulator.ImageManipulatorContext  reset 5expo.modules.imagemanipulator.ImageManipulatorContext  task 5expo.modules.imagemanipulator.ImageManipulatorContext  Base64 4expo.modules.imagemanipulator.ImageManipulatorModule  ByteArrayOutputStream 4expo.modules.imagemanipulator.ImageManipulatorModule  	Coroutine 4expo.modules.imagemanipulator.ImageManipulatorModule  CropTransformer 4expo.modules.imagemanipulator.ImageManipulatorModule  
Exceptions 4expo.modules.imagemanipulator.ImageManipulatorModule  File 4expo.modules.imagemanipulator.ImageManipulatorModule  FileOutputStream 4expo.modules.imagemanipulator.ImageManipulatorModule  	FileUtils 4expo.modules.imagemanipulator.ImageManipulatorModule  FlipTransformer 4expo.modules.imagemanipulator.ImageManipulatorModule  ImageLoaderNotFoundException 4expo.modules.imagemanipulator.ImageManipulatorModule  ImageLoadingFailedException 4expo.modules.imagemanipulator.ImageManipulatorModule  ImageManipulatorContext 4expo.modules.imagemanipulator.ImageManipulatorModule  ImageRef 4expo.modules.imagemanipulator.ImageManipulatorModule  ManipulateOptions 4expo.modules.imagemanipulator.ImageManipulatorModule  ManipulatorTask 4expo.modules.imagemanipulator.ImageManipulatorModule  ModuleDefinition 4expo.modules.imagemanipulator.ImageManipulatorModule  ResizeTransformer 4expo.modules.imagemanipulator.ImageManipulatorModule  RotateTransformer 4expo.modules.imagemanipulator.ImageManipulatorModule  Uri 4expo.modules.imagemanipulator.ImageManipulatorModule  
appContext 4expo.modules.imagemanipulator.ImageManipulatorModule  async 4expo.modules.imagemanipulator.ImageManipulatorModule  context 4expo.modules.imagemanipulator.ImageManipulatorModule  createManipulatorContext 4expo.modules.imagemanipulator.ImageManipulatorModule  generateRandomOutputPath 4expo.modules.imagemanipulator.ImageManipulatorModule  mapOf 4expo.modules.imagemanipulator.ImageManipulatorModule  resume 4expo.modules.imagemanipulator.ImageManipulatorModule  resumeWithException 4expo.modules.imagemanipulator.ImageManipulatorModule  runtimeContext 4expo.modules.imagemanipulator.ImageManipulatorModule  suspend 4expo.modules.imagemanipulator.ImageManipulatorModule  suspendCancellableCoroutine 4expo.modules.imagemanipulator.ImageManipulatorModule  to 4expo.modules.imagemanipulator.ImageManipulatorModule  toCodedException 4expo.modules.imagemanipulator.ImageManipulatorModule  toKClass 4expo.modules.imagemanipulator.ImageManipulatorModule  use 4expo.modules.imagemanipulator.ImageManipulatorModule  ref &expo.modules.imagemanipulator.ImageRef  ImageFormat /expo.modules.imagemanipulator.ManipulateOptions  base64 /expo.modules.imagemanipulator.ManipulateOptions  compress /expo.modules.imagemanipulator.ManipulateOptions  format /expo.modules.imagemanipulator.ManipulateOptions  ManipulatorResult /expo.modules.imagemanipulator.ManipulatorResult  error /expo.modules.imagemanipulator.ManipulatorResult  get /expo.modules.imagemanipulator.ManipulatorResult  map /expo.modules.imagemanipulator.ManipulatorResult  requireNotNull /expo.modules.imagemanipulator.ManipulatorResult  toCodedException /expo.modules.imagemanipulator.ManipulatorResult  value /expo.modules.imagemanipulator.ManipulatorResult  ManipulatorResult -expo.modules.imagemanipulator.ManipulatorTask  addTransformer -expo.modules.imagemanipulator.ManipulatorTask  async -expo.modules.imagemanipulator.ManipulatorTask  cancel -expo.modules.imagemanipulator.ManipulatorTask  coroutineScope -expo.modules.imagemanipulator.ManipulatorTask  launchLoader -expo.modules.imagemanipulator.ManipulatorTask  loader -expo.modules.imagemanipulator.ManipulatorTask  render -expo.modules.imagemanipulator.ManipulatorTask  reset -expo.modules.imagemanipulator.ManipulatorTask  task -expo.modules.imagemanipulator.ManipulatorTask  toCodedException -expo.modules.imagemanipulator.ManipulatorTask  height +expo.modules.imagemanipulator.ResizeOptions  width +expo.modules.imagemanipulator.ResizeOptions  Bitmap *expo.modules.imagemanipulator.transformers  CropRect *expo.modules.imagemanipulator.transformers  CropTransformer *expo.modules.imagemanipulator.transformers  FlipTransformer *expo.modules.imagemanipulator.transformers  FlipType *expo.modules.imagemanipulator.transformers  Float *expo.modules.imagemanipulator.transformers  FunctionalInterface *expo.modules.imagemanipulator.transformers  ImageInvalidCropException *expo.modules.imagemanipulator.transformers  ImageTransformer *expo.modules.imagemanipulator.transformers  Matrix *expo.modules.imagemanipulator.transformers  
ResizeOptions *expo.modules.imagemanipulator.transformers  ResizeTransformer *expo.modules.imagemanipulator.transformers  RotateTransformer *expo.modules.imagemanipulator.transformers  apply *expo.modules.imagemanipulator.transformers  flipType *expo.modules.imagemanipulator.transformers  rotation *expo.modules.imagemanipulator.transformers  Bitmap :expo.modules.imagemanipulator.transformers.CropTransformer  ImageInvalidCropException :expo.modules.imagemanipulator.transformers.CropTransformer  rect :expo.modules.imagemanipulator.transformers.CropTransformer  Bitmap :expo.modules.imagemanipulator.transformers.FlipTransformer  FlipType :expo.modules.imagemanipulator.transformers.FlipTransformer  Matrix :expo.modules.imagemanipulator.transformers.FlipTransformer  apply :expo.modules.imagemanipulator.transformers.FlipTransformer  flipType :expo.modules.imagemanipulator.transformers.FlipTransformer  rotationMatrix :expo.modules.imagemanipulator.transformers.FlipTransformer  	transform ;expo.modules.imagemanipulator.transformers.ImageTransformer  Bitmap <expo.modules.imagemanipulator.transformers.ResizeTransformer  
resizeOptions <expo.modules.imagemanipulator.transformers.ResizeTransformer  Bitmap <expo.modules.imagemanipulator.transformers.RotateTransformer  Matrix <expo.modules.imagemanipulator.transformers.RotateTransformer  apply <expo.modules.imagemanipulator.transformers.RotateTransformer  rotation <expo.modules.imagemanipulator.transformers.RotateTransformer  rotationMatrix <expo.modules.imagemanipulator.transformers.RotateTransformer  ImageLoaderInterface #expo.modules.interfaces.imageloader  ResultListener 8expo.modules.interfaces.imageloader.ImageLoaderInterface  loadImageForManipulationFromURL 8expo.modules.interfaces.imageloader.ImageLoaderInterface  
AppContext expo.modules.kotlin  RuntimeContext expo.modules.kotlin  backgroundCoroutineScope expo.modules.kotlin.AppContext  imageLoader expo.modules.kotlin.AppContext  reactContext expo.modules.kotlin.AppContext  
EitherType expo.modules.kotlin.apifeatures  ClassComponentBuilder "expo.modules.kotlin.classcomponent  
AsyncFunction 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  Base64 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  ByteArrayOutputStream 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  Constructor 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  	Coroutine 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  CropTransformer 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  File 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  FileOutputStream 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  	FileUtils 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  FlipTransformer 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  Function 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  ImageRef 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  ManipulateOptions 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  Property 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  ResizeTransformer 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  RotateTransformer 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  Uri 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  
appContext 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  async 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  context 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  createManipulatorContext 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  generateRandomOutputPath 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  mapOf 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  runtimeContext 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  to 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  use 8expo.modules.kotlin.classcomponent.ClassComponentBuilder  CodedException expo.modules.kotlin.exception  DecoratedException expo.modules.kotlin.exception  
Exceptions expo.modules.kotlin.exception  toCodedException expo.modules.kotlin.exception  CodedException ,expo.modules.kotlin.exception.CodedException  	Companion ,expo.modules.kotlin.exception.CodedException  String ,expo.modules.kotlin.exception.CodedException  IllegalArgument (expo.modules.kotlin.exception.Exceptions  ReactContextLost (expo.modules.kotlin.exception.Exceptions  AsyncFunctionBuilder expo.modules.kotlin.functions  AsyncFunctionComponent expo.modules.kotlin.functions  BaseAsyncFunctionComponent expo.modules.kotlin.functions  	Coroutine expo.modules.kotlin.functions  FunctionBuilder expo.modules.kotlin.functions  SuspendFunctionComponent expo.modules.kotlin.functions  SyncFunctionComponent expo.modules.kotlin.functions  	Coroutine 2expo.modules.kotlin.functions.AsyncFunctionBuilder  Module expo.modules.kotlin.modules  ModuleDefinition expo.modules.kotlin.modules  ModuleDefinitionBuilder expo.modules.kotlin.modules  ModuleDefinitionData expo.modules.kotlin.modules  Class ;expo.modules.kotlin.modules.InternalModuleDefinitionBuilder  Name ;expo.modules.kotlin.modules.InternalModuleDefinitionBuilder  
appContext "expo.modules.kotlin.modules.Module  runtimeContext "expo.modules.kotlin.modules.Module  Base64 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  ByteArrayOutputStream 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  Class 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  	Coroutine 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  CropTransformer 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  
Exceptions 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  File 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  FileOutputStream 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  	FileUtils 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  FlipTransformer 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  Function 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  ImageRef 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  ManipulateOptions 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  Name 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  ResizeTransformer 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  RotateTransformer 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  Uri 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  
appContext 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  async 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  context 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  createManipulatorContext 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  generateRandomOutputPath 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  mapOf 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  runtimeContext 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  to 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  toKClass 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  use 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  PropertyComponentBuilder expo.modules.kotlin.objects   PropertyComponentBuilderWithThis expo.modules.kotlin.objects  
AsyncFunction 3expo.modules.kotlin.objects.ObjectDefinitionBuilder  Function 3expo.modules.kotlin.objects.ObjectDefinitionBuilder  Field expo.modules.kotlin.records  Record expo.modules.kotlin.records  SharedObject !expo.modules.kotlin.sharedobjects  	SharedRef !expo.modules.kotlin.sharedobjects  ref +expo.modules.kotlin.sharedobjects.SharedRef  
EitherOfThree expo.modules.kotlin.types  
Enumerable expo.modules.kotlin.types  toKClass expo.modules.kotlin.types  get 'expo.modules.kotlin.types.EitherOfThree  is 'expo.modules.kotlin.types.EitherOfThree  ByteArrayOutputStream java.io  File java.io  FileOutputStream java.io  IOException java.io  toByteArray java.io.ByteArrayOutputStream  use java.io.ByteArrayOutputStream  isDirectory java.io.File  mkdirs java.io.File  path java.io.File  	separator java.io.File  use java.io.FileOutputStream  FunctionalInterface 	java.lang  Context 	java.util  File 	java.util  IOException 	java.util  ImageFormat 	java.util  ImageWriteFailedException 	java.util  String 	java.util  Throws 	java.util  UUID 	java.util  
randomUUID java.util.UUID  Enum kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  	Throwable kotlin  apply kotlin  requireNotNull kotlin  suspend kotlin  to kotlin  use kotlin  not kotlin.Boolean  	compareTo 
kotlin.Double  div 
kotlin.Double  times 
kotlin.Double  toInt 
kotlin.Double  Bitmap kotlin.Enum  	Companion kotlin.Enum  JPEG kotlin.Enum  JPG kotlin.Enum  PNG kotlin.Enum  String kotlin.Enum  WEBP kotlin.Enum  CompressFormat kotlin.Enum.Bitmap  Bitmap kotlin.Enum.Companion  JPEG kotlin.Enum.Companion  JPG kotlin.Enum.Companion  PNG kotlin.Enum.Companion  WEBP kotlin.Enum.Companion  
unaryMinus kotlin.Float  div 
kotlin.Int  times 
kotlin.Int  toDouble 
kotlin.Int  to 
kotlin.String  toCodedException kotlin.Throwable  Map kotlin.collections  mapOf kotlin.collections  SuspendFunction0 kotlin.coroutines  SuspendFunction1 kotlin.coroutines  SuspendFunction2 kotlin.coroutines  resume kotlin.coroutines  resumeWithException kotlin.coroutines  invoke "kotlin.coroutines.SuspendFunction0  use 	kotlin.io  Throws 
kotlin.jvm  KClass kotlin.reflect  CancellableContinuation kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Deferred kotlinx.coroutines  async kotlinx.coroutines  suspendCancellableCoroutine kotlinx.coroutines  resume *kotlinx.coroutines.CancellableContinuation  resumeWithException *kotlinx.coroutines.CancellableContinuation  Base64 !kotlinx.coroutines.CoroutineScope  ByteArrayOutputStream !kotlinx.coroutines.CoroutineScope  FileOutputStream !kotlinx.coroutines.CoroutineScope  ManipulatorResult !kotlinx.coroutines.CoroutineScope  async !kotlinx.coroutines.CoroutineScope  loader !kotlinx.coroutines.CoroutineScope  toCodedException !kotlinx.coroutines.CoroutineScope  use !kotlinx.coroutines.CoroutineScope  await kotlinx.coroutines.Deferred  cancel kotlinx.coroutines.Deferred  cancel kotlinx.coroutines.Job                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          