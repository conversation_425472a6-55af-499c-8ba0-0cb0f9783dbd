"use client";

import { motion } from "framer-motion";
import { ShoppingBag, MessageSquare, Images } from "lucide-react";
import { Button } from "@/components/ui/button";

interface EnhancedTabsToggleProps {
  activeTab: string;
  onTabChange: (_tab: string) => void;
  galleryCount?: number;
}

export default function EnhancedTabsToggle({
  activeTab,
  onTabChange,
  galleryCount = 0,
}: EnhancedTabsToggleProps) {
  return (
    <motion.div
      className="flex flex-col items-center mb-8"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.h2
        className="text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-4 text-center"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        Products, Gallery & Reviews
      </motion.h2>

      <motion.div
        className="relative bg-white/90 dark:bg-neutral-800/90 p-1 sm:p-1.5 rounded-xl border border-neutral-200/70 dark:border-neutral-700/70 flex gap-1 backdrop-blur-md overflow-hidden shadow-sm"
        whileHover={{ boxShadow: "0 8px 30px rgba(0, 0, 0, 0.06)" }}
      >
        {/* Decorative elements */}
        <div className="absolute -top-6 -right-6 w-12 h-12 bg-[var(--brand-gold)]/10 blur-xl rounded-full"></div>
        <div className="absolute -bottom-6 -left-6 w-12 h-12 bg-purple-500/10 blur-xl rounded-full"></div>

        {/* Products Tab */}
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="relative z-10"
        >
          <Button
            variant="ghost"
            onClick={() => onTabChange("products")}
            className={`
              relative px-2 sm:px-3 py-1 sm:py-1.5 h-8 sm:h-9 rounded-lg transition-all duration-300
              ${
                activeTab === "products"
                  ? "text-black dark:text-white font-medium"
                  : "text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300"
              }
            `}
          >
            <div className="flex items-center">
              <div className="relative">
                <ShoppingBag className="mr-1 sm:mr-1.5 h-3.5 w-3.5 sm:h-4 sm:w-4" />
              </div>
              <span className="text-xs sm:text-xs md:text-sm font-medium">Products</span>
            </div>

            {/* Active indicator */}
            {activeTab === "products" && (
              <motion.div
                layoutId="activeTabIndicator"
                className="absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/20 dark:from-[var(--brand-gold)]/15 dark:to-[var(--brand-gold)]/25 border border-[var(--brand-gold)]/30 rounded-lg -z-10 shadow-inner"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              />
            )}
          </Button>
        </motion.div>

        {/* Gallery Tab */}
        {galleryCount > 0 && (
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="relative z-10"
          >
            <Button
              variant="ghost"
              onClick={() => onTabChange("gallery")}
              className={`
                relative px-2 sm:px-3 py-1 sm:py-1.5 h-8 sm:h-9 rounded-lg transition-all duration-300
                ${
                  activeTab === "gallery"
                    ? "text-black dark:text-white font-medium"
                    : "text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300"
                }
              `}
            >
              <div className="flex items-center">
                <div className="relative">
                  <Images className="mr-1 sm:mr-1.5 h-3.5 w-3.5 sm:h-4 sm:w-4" />
                </div>
                <span className="text-xs sm:text-xs md:text-sm font-medium">Gallery</span>
                {galleryCount > 0 && (
                  <span className="ml-1 text-xs text-neutral-400 dark:text-neutral-500">
                    ({galleryCount})
                  </span>
                )}
              </div>

              {/* Active indicator */}
              {activeTab === "gallery" && (
                <motion.div
                  layoutId="activeTabIndicator"
                  className="absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/20 dark:from-[var(--brand-gold)]/15 dark:to-[var(--brand-gold)]/25 border border-[var(--brand-gold)]/30 rounded-lg -z-10"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                />
              )}
            </Button>
          </motion.div>
        )}

        {/* Reviews Tab */}
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="relative z-10"
        >
          <Button
            variant="ghost"
            onClick={() => onTabChange("reviews")}
            className={`
              relative px-2 sm:px-3 py-1 sm:py-1.5 h-8 sm:h-9 rounded-lg transition-all duration-300
              ${
                activeTab === "reviews"
                  ? "text-black dark:text-white font-medium"
                  : "text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300"
              }
            `}
          >
            <div className="flex items-center">
              <div className="relative">
                <MessageSquare className="mr-1 sm:mr-1.5 h-3.5 w-3.5 sm:h-4 sm:w-4" />
              </div>
              <span className="text-xs sm:text-xs md:text-sm font-medium">
                Reviews
              </span>
            </div>

            {/* Active indicator */}
            {activeTab === "reviews" && (
              <motion.div
                layoutId="activeTabIndicator"
                className="absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/20 dark:from-[var(--brand-gold)]/15 dark:to-[var(--brand-gold)]/25 border border-[var(--brand-gold)]/30 rounded-lg -z-10"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              />
            )}
          </Button>
        </motion.div>
      </motion.div>
    </motion.div>
  );
}
