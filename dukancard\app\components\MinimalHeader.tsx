"use client";

import React from "react";
import Link from "next/link";
// Removed unused imports: useRout<PERSON>, createClient
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { LogOut } from "lucide-react";
// Removed unused ThemeToggle import
import { signOutUser } from "@/app/auth/actions"; // Import the server action
import { useCustomerProfile, useBusinessProfile } from "@/contexts/UserDataContext";
// Removed unused Sheet import

interface MinimalHeaderProps {
  children?: React.ReactNode;
  // Updated props
  businessName?: string | null; // For business context or fallback
  logoUrl?: string | null;
  userName?: string | null; // Added for user's actual name
}

// Helper to get initials - prioritize userName if available, else businessName
const getInitials = (userName?: string | null, businessName?: string | null): string => {
  const nameToUse = userName || businessName; // Use user name first for avatar
  if (!nameToUse) return "?";
  const names = nameToUse.trim().split(/\s+/);
  if (names.length === 1) return names[0].charAt(0).toUpperCase();
  return (
    names[0].charAt(0).toUpperCase() +
    names[names.length - 1].charAt(0).toUpperCase()
  );
};

const MinimalHeader: React.FC<MinimalHeaderProps> = ({
  children,
  businessName: propBusinessName,
  logoUrl: propLogoUrl,
  userName: propUserName, // Added prop
}) => {
  // Use context data with fallback to props
  const { customerProfile } = useCustomerProfile();
  const { businessProfile } = useBusinessProfile();

  // Determine actual data to use (context first, then props)
  const businessName = businessProfile?.business_name || propBusinessName;
  const logoUrl = businessProfile?.logo_url || customerProfile?.avatar_url || propLogoUrl;
  const userName = businessProfile?.member_name || customerProfile?.name || propUserName;

  // Initials logic updated to prioritize userName
  const initials = getInitials(userName, businessName);

  // Determine display name for the dropdown
  let displayName = "User"; // Default fallback
  if (userName && businessName) {
    displayName = `${businessName} (${userName})`; // Business context
  } else if (userName) {
    displayName = userName; // Customer context
  } else if (businessName) {
    displayName = businessName; // Fallback if only business name exists
  }


  // Access children assuming specific order from layout: [Sheet, Button, ThemeToggle]
  const childArray = React.Children.toArray(children);
  const mobileSheetTrigger = childArray[0]; // Assumes Sheet is the first child
  const desktopToggleButton = childArray[1]; // Assumes Button is the second child
  const themeToggleElement = childArray[2]; // Assumes ThemeToggle is the third child

  return (
    <header className="sticky top-0 z-40 w-full border-b border-border/40 bg-white dark:bg-black backdrop-blur supports-[backdrop-filter]:bg-white/95 dark:supports-[backdrop-filter]:bg-black/95">
      {/* Added horizontal padding */}
      <div className="container flex h-16 max-w-screen-2xl items-center justify-between px-4 md:px-6 lg:px-8">
        {/* Left Section: Mobile Toggle -> Logo -> Desktop Toggle */}
        <div className="flex items-center space-x-2 md:space-x-4">
          {/* Render Mobile Sheet Trigger First */}
          {mobileSheetTrigger}

          {/* Logo immediately after mobile toggle */}
          <Link href="/?view=home" className="flex items-center group">
            <div className="flex flex-col">
              <span className="font-bold text-xl text-[var(--brand-gold)]">
                Dukan<span className="text-foreground">card</span>{" "}
                {/* Use text-foreground for adaptability */}
              </span>
              <span className="text-xs text-[var(--brand-gold)]/80 -mt-1">
                {businessName ? "Your Business, Online in Seconds" : "Your Neighborhood, Digitally Connected"}
              </span>
            </div>
          </Link>

          {/* Render Desktop Toggle Button after logo */}
          {desktopToggleButton}
        </div>

        {/* Right Section: User Menu + Theme Toggle */}
        <div className="flex items-center space-x-2 md:space-x-4">
          {/* User Profile Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              {/* Added cursor-pointer here */}
              <Button
                variant="ghost"
                className="cursor-pointer relative h-9 w-9 rounded-full focus-visible:ring-0 focus-visible:ring-offset-0"
              >
                <Avatar className="h-9 w-9 border border-[var(--brand-gold)]/30 hover:border-[var(--brand-gold)]/50 transition-colors">
                  {/* Use logoUrl for avatar image only if it exists */}
                  {logoUrl ? (
                    <AvatarImage
                      src={logoUrl}
                      alt={userName || businessName || "User"}
                    />
                  ) : null}
                  <AvatarFallback className={`${userName && !businessName ? 'bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-900 text-blue-800 dark:text-blue-200 border border-[var(--brand-gold)]/30' : 'bg-muted text-muted-foreground border border-[var(--brand-gold)]/30'}`}>
                    {initials}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-64" align="end" forceMount> {/* Increased width slightly */}
              {/* Updated Dropdown Label */}
              <DropdownMenuLabel className="font-normal">
                <p className="text-sm font-medium leading-none truncate py-2">
                  {displayName} {/* Use the combined/correct display name */}
                </p>
                {/* Optionally add email back if needed, maybe only for customers? */}
                {/* {userName && !businessName && userEmail && (
                  <p className="text-xs leading-none text-muted-foreground truncate pt-1">
                    {userEmail}
                  </p>
                )} */}
              </DropdownMenuLabel>
              {/* Removed email display and extra separator */}
              {/* <DropdownMenuSeparator /> */}
              {/* Add links to profile/settings if needed */}
              {/* <DropdownMenuItem asChild>
                <Link href="/dashboard/profile">
                  <UserIcon className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </Link>
              </DropdownMenuItem> */}
              <DropdownMenuSeparator />
              {/* Logout Button using Server Action */}
              <form action={signOutUser} className="w-full px-2 py-1.5">
                <Button
                  type="submit"
                  variant="ghost"
                  className="w-full justify-start font-normal text-sm h-auto py-1 cursor-pointer"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </Button>
              </form>
            </DropdownMenuContent>
          </DropdownMenu>
          {themeToggleElement} {/* Render theme toggle last */}
        </div>
      </div>
    </header>
  );
};

export default MinimalHeader;
