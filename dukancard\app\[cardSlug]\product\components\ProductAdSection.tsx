"use client";

import { motion, Variants } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { AdData } from "@/types/ad";

interface ProductAdSectionProps {
  topAdData: AdData | null;
  itemVariants: Variants;
  businessCustomAd?: {
    enabled?: boolean;
    image_url?: string;
    link_url?: string;
  } | null;
  userPlan?: "free" | "basic" | "growth" | "pro" | "enterprise";
}

// Simple URL validation utility
const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export default function ProductAdSection({ topAdData, itemVariants, businessCustomAd, userPlan }: ProductAdSectionProps) {
  // Check if business owner has Pro/Enterprise access for custom ads
  const hasProEnterpriseAccess = userPlan === "pro" || userPlan === "enterprise";

  // Validate custom ad data - must have Pro/Enterprise plan, enabled=true, and valid image URL
  const isCustomAdValid = businessCustomAd &&
    typeof businessCustomAd === 'object' &&
    businessCustomAd.enabled === true &&
    businessCustomAd.image_url &&
    typeof businessCustomAd.image_url === 'string' &&
    businessCustomAd.image_url.trim() !== "" &&
    isValidUrl(businessCustomAd.image_url);

  // Validate custom ad link URL if provided
  const hasValidLinkUrl = businessCustomAd?.link_url &&
    typeof businessCustomAd.link_url === 'string' &&
    businessCustomAd.link_url.trim() !== "" &&
    isValidUrl(businessCustomAd.link_url);

  // Determine which ad to show - business custom ad takes priority (only for Pro/Enterprise with valid data and enabled=true)
  const shouldShowBusinessAd = hasProEnterpriseAccess && isCustomAdValid;
  const shouldShowTopAd = !shouldShowBusinessAd && topAdData && topAdData.imageUrl;



  return (
    <motion.div
      variants={itemVariants}
      className="w-full"
    >
      {/* Ad Slot - No card container */}
      <div className="flex-shrink-0 w-full overflow-hidden">
        {shouldShowBusinessAd ? (
          /* Business Custom Ad */
          hasValidLinkUrl ? (
            <Link
              href={businessCustomAd!.link_url!}
              target="_blank"
              rel="noopener noreferrer"
              className="block w-full overflow-hidden"
            >
              <div className="relative w-full">
                <Image
                  src={businessCustomAd!.image_url!}
                  alt="Business Advertisement"
                  width={1200}
                  height={675} // 16:9 aspect ratio
                  className="w-full h-auto object-contain max-w-full"
                  unoptimized
                />
                {/* Business ad indicator */}
                <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                  Sponsored
                </div>
              </div>
            </Link>
          ) : (
            /* Non-clickable Business Custom Ad (no link URL) */
            <div className="block w-full overflow-hidden">
              <div className="relative w-full">
                <Image
                  src={businessCustomAd!.image_url!}
                  alt="Business Advertisement"
                  width={1200}
                  height={675} // 16:9 aspect ratio
                  className="w-full h-auto object-contain max-w-full"
                  unoptimized
                />
                {/* Business ad indicator */}
                <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                  Sponsored
                </div>
              </div>
            </div>
          )
        ) : shouldShowTopAd ? (
          <Link
            href={topAdData!.linkUrl || "#"}
            target="_blank"
            rel="noopener noreferrer"
            className="block w-full overflow-hidden"
          >
            <div className="relative w-full">
              <Image
                src={topAdData!.imageUrl}
                alt="Advertisement"
                width={1200}
                height={675} // 16:9 aspect ratio
                className="w-full h-auto object-contain max-w-full"
                unoptimized
              />
            </div>
          </Link>
        ) : (
          <div className="border border-dashed rounded-lg p-4 flex items-center justify-center w-full text-neutral-500 bg-neutral-50 dark:bg-neutral-800/50 min-h-[300px]">
            {/* Placeholder */}
          </div>
        )}
      </div>
    </motion.div>
  );
}
