"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { ArrowRight, Loader2 } from "lucide-react";

// Email schema for step 1
const emailSchema = z.object({
  email: z
    .string()
    .min(1, { message: "Email is required" })
    .email({ message: "Please enter a valid email address" }),
});

// OTP schema for step 2
const otpSchema = z.object({
  otp: z
    .string()
    .min(6, { message: "OTP must be 6 digits" })
    .max(6, { message: "OTP must be 6 digits" })
    .regex(/^\d{6}$/, { message: "OTP must be 6 digits" }),
});

interface EmailOTPFormProps {
  step: 'email' | 'otp';
  email: string;
  countdown: number;
  isPending: boolean;
  onEmailSubmit: (_values: z.infer<typeof emailSchema>) => void;
  onOTPSubmit: (_values: z.infer<typeof otpSchema>) => void;
  onResendOTP: () => void;
  onBackToEmail: () => void;
}

export function EmailOTPForm({
  step,
  email,
  countdown,
  isPending,
  onEmailSubmit,
  onOTPSubmit,
  onResendOTP,
  onBackToEmail,
}: EmailOTPFormProps) {
  const emailForm = useForm<z.infer<typeof emailSchema>>({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: "",
    },
  });

  const otpForm = useForm<z.infer<typeof otpSchema>>({
    resolver: zodResolver(otpSchema),
    defaultValues: {
      otp: "",
    },
  });

  if (step === 'email') {
    return (
      <Form {...emailForm}>
        <form
          onSubmit={emailForm.handleSubmit(onEmailSubmit)}
          className="space-y-4 sm:space-y-6"
        >
          <FormField
            control={emailForm.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-foreground text-sm sm:text-base">
                  Email Address
                </FormLabel>
                <FormControl>
                  <Input
                    id="email-login-field"
                    placeholder="<EMAIL>"
                    type="email"
                    {...field}
                    className="bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            type="submit"
            className="cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base"
            disabled={isPending}
          >
            {isPending ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                Sending OTP...
              </>
            ) : (
              <>
                Continue <ArrowRight className="w-5 h-5 ml-2" />
              </>
            )}
          </Button>

          <div className="text-center mt-4 text-xs sm:text-sm">
            <span className="text-muted-foreground">
              New to Dukancard? No worries! We&apos;ll create your account automatically.
            </span>
          </div>
        </form>
      </Form>
    );
  }

  return (
    <Form {...otpForm}>
      <form
        onSubmit={otpForm.handleSubmit(onOTPSubmit)}
        className="space-y-4 sm:space-y-6"
      >
        <div className="text-center mb-4">
          <p className="text-sm text-muted-foreground">
            We&apos;ve sent a 6-digit code to
          </p>
          <p className="text-sm font-medium text-foreground">{email}</p>
          <p className="text-xs text-muted-foreground mt-2">
            Code expires in 24 hours
          </p>
        </div>

        <FormField
          control={otpForm.control}
          name="otp"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-foreground text-sm sm:text-base text-center block">
                Enter Verification Code
              </FormLabel>
              <FormControl>
                <div className="flex justify-center">
                  <InputOTP
                    id="otp-login-field"
                    maxLength={6}
                    {...field}
                    className="gap-2"
                  >
                    <InputOTPGroup>
                      <InputOTPSlot
                        index={0}
                        className="w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"
                      />
                      <InputOTPSlot
                        index={1}
                        className="w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"
                      />
                      <InputOTPSlot
                        index={2}
                        className="w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"
                      />
                      <InputOTPSlot
                        index={3}
                        className="w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"
                      />
                      <InputOTPSlot
                        index={4}
                        className="w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"
                      />
                      <InputOTPSlot
                        index={5}
                        className="w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"
                      />
                    </InputOTPGroup>
                  </InputOTP>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base"
          disabled={isPending}
        >
          {isPending ? (
            <>
              <Loader2 className="w-5 h-5 mr-2 animate-spin" />
              Verifying...
            </>
          ) : (
            <>
              Verify & Sign In <ArrowRight className="w-5 h-5 ml-2" />
            </>
          )}
        </Button>

        <div className="flex flex-col items-center space-y-3 mt-4 text-xs sm:text-sm">
          <button
            type="button"
            onClick={onResendOTP}
            disabled={countdown > 0}
            className={`${
              countdown > 0
                ? "text-muted-foreground cursor-not-allowed"
                : "text-primary dark:text-[var(--brand-gold)] hover:underline cursor-pointer"
            }`}
          >
            {countdown > 0 ? `Resend OTP in ${countdown}s` : "Resend OTP"}
          </button>

          <button
            type="button"
            onClick={onBackToEmail}
            className="text-muted-foreground hover:text-foreground cursor-pointer"
          >
            ← Change email address
          </button>
        </div>
      </form>
    </Form>
  );
}
