/**
 * Utility functions for posts
 */

import { PostData } from '@/lib/types/posts';
import { UserProfile } from './types';

/**
 * Calculate post score based on base score and time decay
 */
export function calculatePostScore(post: PostData, baseScore: number, _userProfile?: UserProfile): number {
  const now = new Date();
  const postDate = new Date(post.created_at);
  const daysDiff = (now.getTime() - postDate.getTime()) / (1000 * 60 * 60 * 24);

  let timeDecayFactor = 1.0;
  if (daysDiff <= 1) {
    timeDecayFactor = 1.0;
  } else if (daysDiff <= 7) {
    timeDecayFactor = 0.8;
  } else if (daysDiff <= 28) {
    timeDecayFactor = 0.6;
  } else {
    timeDecayFactor = 0.4;
  }

  return Math.round(baseScore * timeDecayFactor);
}

/**
 * Get the business post select query with business profile
 */
export function getBusinessPostSelectQuery(): string {
  return `
    *,
    business_profiles!inner (
      id,
      business_name,
      logo_url,
      business_slug,
      phone,
      whatsapp_number,
      city,
      state
    )
  `;
}

/**
 * Build exclusion filter for business IDs
 */
export function buildBusinessIdExclusionFilter(businessIds: string[]): string {
  return businessIds.length > 0 ? `(${businessIds.join(',')})` : 'null';
}
