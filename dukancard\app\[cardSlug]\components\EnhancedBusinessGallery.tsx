"use client";

import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { motion, AnimatePresence, useScroll, useTransform } from "framer-motion";
import { X, Maximize, ExternalLink } from "lucide-react";
// import { ChevronLeft, ChevronRight } from "lucide-react"; // Currently not used
import { GalleryImage } from "@/lib/actions/gallery";
import { Button } from "@/components/ui/button";
import { useInView } from "react-intersection-observer";

interface EnhancedBusinessGalleryProps {
  images: GalleryImage[];
  businessName: string;
  businessSlug: string; // Added to create link to gallery page
}

export default function EnhancedBusinessGallery({
  images,
  businessName,
  businessSlug,
}: EnhancedBusinessGalleryProps) {
  const [lightboxImage, setLightboxImage] = useState<string | null>(null);
  const [_currentPage, setCurrentPage] = useState(0);
  const [isClient, setIsClient] = useState(false);
  const [autoplay, setAutoplay] = useState(true);
  const [_showAllImages, _setShowAllImages] = useState(false);

  const carouselRef = useRef<HTMLDivElement>(null);

  // For intersection observer to pause autoplay when not in view
  const { ref: inViewRef, inView } = useInView({
    threshold: 0.3,
  });

  // For parallax effect
  const { scrollYProgress } = useScroll({
    target: carouselRef,
    offset: ["start end", "end start"],
  });

  const y1 = useTransform(scrollYProgress, [0, 1], [0, -50]);
  const y2 = useTransform(scrollYProgress, [0, 1], [0, -30]);

  // Always show exactly 3 images in the preview
  const maxPreviewImages = 3;
  const hasMoreImages = images.length > maxPreviewImages;

  // Get the first 3 images for preview, regardless of how many are available
  // This ensures we always show exactly 3 images in the preview
  const previewImages = images.slice(0, Math.min(maxPreviewImages, images.length));

  // Calculate rows and items per row for the preview
  const itemsPerRow = 3; // Show 3 images in a single row
  const _totalRows = 1; // Only one row in preview
  const rows = [0]; // Just one row index

  // Calculate total pages (only used for carousel mode)
  const totalPages = 1; // No pagination in preview

  // Set up autoplay
  useEffect(() => {
    setIsClient(true);

    let interval: NodeJS.Timeout;

    if (autoplay && inView && images.length > itemsPerRow * 2) {
      interval = setInterval(() => {
        setCurrentPage((prev) => (prev + 1) % totalPages);
      }, 5000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoplay, inView, images.length, itemsPerRow, totalPages]);

  // Prevent body scroll when lightbox is open
  useEffect(() => {
    if (lightboxImage) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [lightboxImage]);

  // Handle navigation
  const _handlePrevPage = () => {
    setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages);
  };

  const _handleNextPage = () => {
    setCurrentPage((prev) => (prev + 1) % totalPages);
  };

  if (images.length === 0) {
    return null;
  }

  return (
    <motion.div
      className="w-full mt-12 mb-8"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      ref={inViewRef}
    >
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold">Gallery</h2>
        {hasMoreImages && (
          <Link href={`/${businessSlug}/gallery`} passHref>
            <Button
              variant="outline"
              size="sm"
              className="gap-1 text-sm"
            >
              <span>View all {images.length} photos</span>
              <ExternalLink className="h-3.5 w-3.5 ml-1" />
            </Button>
          </Link>
        )}
      </div>

      <div
        className="relative overflow-hidden"
        ref={carouselRef}
        onMouseEnter={() => setAutoplay(false)}
        onMouseLeave={() => setAutoplay(true)}
      >
        {rows.map((rowIndex) => (
          <motion.div
            key={`row-${rowIndex}`}
            className="grid grid-cols-2 sm:grid-cols-4 gap-3 mb-3"
            style={{ y: rowIndex === 0 ? y1 : y2 }}
            initial={{ opacity: 0, x: rowIndex % 2 === 0 ? -20 : 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: rowIndex * 0.2 }}
          >
            {previewImages.map((image) => (
                <motion.div
                  key={image.id}
                  className="aspect-square relative overflow-hidden rounded-xl cursor-pointer group"
                  onClick={() => setLightboxImage(image.url)}
                  whileHover={{ scale: 1.03 }}
                  transition={{ duration: 0.3 }}
                >
                  <Image
                    src={image.url}
                    alt={`${businessName} gallery image`}
                    fill
                    className="object-cover transition-transform duration-500"
                    sizes="(max-width: 640px) 50vw, 25vw"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="absolute bottom-2 right-2 bg-black/50 text-white p-1.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <Maximize className="w-4 h-4" />
                    </div>
                  </div>
                </motion.div>
              ))}
          </motion.div>
        ))}

        {/* View All Button */}
        {hasMoreImages && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mt-2"
          >
            <Link href={`/${businessSlug}/gallery`} passHref>
              <button
                className="w-full py-3 px-4 bg-black/5 hover:bg-black/10 dark:bg-white/5 dark:hover:bg-white/10 rounded-xl text-center transition-colors duration-200"
              >
                <span className="text-sm font-medium flex items-center justify-center">
                  View all {images.length} photos
                  <ExternalLink className="h-3.5 w-3.5 ml-2" />
                </span>
              </button>
            </Link>
          </motion.div>
        )}
      </div>

      {/* Lightbox */}
      <AnimatePresence>
        {lightboxImage && isClient && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4"
            onClick={() => setLightboxImage(null)}
          >
            <motion.button
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="absolute top-4 right-4 text-white bg-black/50 rounded-full p-2 hover:bg-black/70 transition-colors duration-200 z-50"
              onClick={(e) => {
                e.stopPropagation();
                setLightboxImage(null);
              }}
            >
              <X className="h-6 w-6" />
            </motion.button>
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="relative max-w-4xl max-h-[80vh] w-full h-full"
              onClick={(e) => e.stopPropagation()}
            >
              <Image
                src={lightboxImage}
                alt={`${businessName} gallery image`}
                fill
                className="object-contain"
                sizes="100vw"
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
