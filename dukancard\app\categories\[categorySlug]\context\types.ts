import { NearbyProduct } from "@/app/(main)/discover/actions/types";
import type { BusinessSortBy, BusinessProfilePublicData } from "@/lib/actions/businessProfiles/types";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";

// Define the product sort options for the category page
export type ProductSortOption =
  | "newest"
  | "price_low"
  | "price_high"
  | "name_asc"
  | "name_desc";

// Define the business sort options for the category page
export type BusinessSortOption =
  | "newest"
  | "name_asc"
  | "name_desc"
  | "most_liked"
  | "most_subscribed"
  | "highest_rated";

// Define the product filter options
export type ProductFilterOption = "all" | "physical" | "service";

// Define the view types
export type ViewType = "cards" | "products";

// Define the location structure
export type LocationData = {
  state?: string;
  city?: string;
  pincode?: string;
  locality?: string;
};

// Define the search result structure
export type CategorySearchResult = {
  location?: LocationData | null;
  businesses?: (BusinessProfilePublicData | BusinessCardData)[];
  products?: NearbyProduct[];
  isAuthenticated: boolean;
  totalCount: number;
  hasMore: boolean;
  nextPage: number | null;
};

// Define the combined search form data
export type CombinedSearchFormData = {
  businessName?: string | null;
  state?: string | null;
  pincode?: string | null;
  city?: string | null;
  locality?: string | null;
  stateSlug?: string | null;
  citySlug?: string | null;
  localitySlug?: string | null;
};

// Define the serializable category
export interface SerializableCategory {
  name: string;
  slug?: string;
  description?: string;
  isPopular?: boolean;
  iconName?: string;
}

// Import LocationInfo type
import { LocationInfo } from "../ModernCategoryClient";

// Define the context type
export type CategoryContextType = {
  // State
  category: SerializableCategory;
  viewType: ViewType;
  sortBy: BusinessSortBy;
  isSearching: boolean;
  isPending: boolean;
  isLoadingMore: boolean;
  searchError: string | null;
  productFilterBy: ProductFilterOption;
  productSortBy: ProductSortOption;
  searchResult: CategorySearchResult | null;
  businesses: (BusinessProfilePublicData | BusinessCardData)[];
  products: NearbyProduct[];
  currentPage: number;
  hasMore: boolean;
  totalCount: number;
  isAuthenticated: boolean;
  locationInfo?: LocationInfo;

  // Functions
  performSearch: (_data: CombinedSearchFormData) => void;
  handleViewChange: (_view: ViewType) => void;
  handleBusinessSortChange: (_sortOption: BusinessSortBy) => void;
  handleBusinessSearch: (_term: string) => void;
  handleProductSearch: (_term: string) => void;
  handleProductSortChange: (_sortOption: ProductSortOption) => void;
  handleProductFilterChange: (_filter: ProductFilterOption) => void;
  loadMore: () => Promise<void>;
};
