[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: arm64-v8a", "file_": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\arm64-v8a\\android_gradle_build.json due to:", "file_": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Microsoft\\\\jdk-17.0.15.6-hotspot\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  arm64-v8a ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  27 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging18036412476606270791\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\6e9cabc4b841ecb8bc48e2ce54799a1d\\\\transformed\\\\react-android-0.79.3-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\8050d15875717ad3c035882deb89d68f\\\\transformed\\\\fbjni-0.7.0\\\\prefab\"\n", "file_": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\arm64-v8a'", "file_": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\arm64-v8a'", "file_": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\Users\\\\<USER>\\\\Desktop\\\\Dukancard Web App\\\\dukancard-app\\\\node_modules\\\\expo-modules-core\\\\android\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dukancard Web App\\\\dukancard-app\\\\node_modules\\\\expo-modules-core\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\6b4c83s4\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dukancard Web App\\\\dukancard-app\\\\node_modules\\\\expo-modules-core\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\6b4c83s4\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dukancard Web App\\\\dukancard-app\\\\node_modules\\\\expo-modules-core\\\\android\\\\.cxx\\\\Debug\\\\6b4c83s4\\\\prefab\\\\arm64-v8a\\\\prefab\" ^\n  \"-BC:\\\\Users\\\\<USER>\\\\Desktop\\\\Dukancard Web App\\\\dukancard-app\\\\node_modules\\\\expo-modules-core\\\\android\\\\.cxx\\\\Debug\\\\6b4c83s4\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DREACT_NATIVE_DIR=C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dukancard Web App\\\\dukancard-app\\\\node_modules\\\\react-native\" ^\n  \"-DREACT_NATIVE_TARGET_VERSION=79\" ^\n  \"-DUSE_HERMES=false\" ^\n  \"-DIS_NEW_ARCHITECTURE_ENABLED=true\" ^\n  \"-DUNIT_TEST=false\"\n", "file_": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\Users\\\\<USER>\\\\Desktop\\\\Dukancard Web App\\\\dukancard-app\\\\node_modules\\\\expo-modules-core\\\\android\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dukancard Web App\\\\dukancard-app\\\\node_modules\\\\expo-modules-core\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\6b4c83s4\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dukancard Web App\\\\dukancard-app\\\\node_modules\\\\expo-modules-core\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\6b4c83s4\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dukancard Web App\\\\dukancard-app\\\\node_modules\\\\expo-modules-core\\\\android\\\\.cxx\\\\Debug\\\\6b4c83s4\\\\prefab\\\\arm64-v8a\\\\prefab\" ^\n  \"-BC:\\\\Users\\\\<USER>\\\\Desktop\\\\Dukancard Web App\\\\dukancard-app\\\\node_modules\\\\expo-modules-core\\\\android\\\\.cxx\\\\Debug\\\\6b4c83s4\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DREACT_NATIVE_DIR=C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dukancard Web App\\\\dukancard-app\\\\node_modules\\\\react-native\" ^\n  \"-DREACT_NATIVE_TARGET_VERSION=79\" ^\n  \"-DUSE_HERMES=false\" ^\n  \"-DIS_NEW_ARCHITECTURE_ENABLED=true\" ^\n  \"-DUNIT_TEST=false\"\n", "file_": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\arm64-v8a\\compile_commands.json.bin normally", "file_": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\arm64-v8a\\compile_commands.json to C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\tools\\debug\\arm64-v8a\\compile_commands.json", "file_": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]