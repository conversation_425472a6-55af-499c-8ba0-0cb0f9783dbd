# Business Posts Table Documentation

## Table Overview

The `business_posts` table in the Dukancard application stores posts created by businesses. It enables a social feed feature where businesses can share updates, announcements, and other content with their subscribers and local customers. In the future, a separate `customer_posts` table may be added to support posts from customers.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key for the post record |
| business_id | uuid | NO | | Foreign key to business_profiles.id, the business that created the post |
| content | text | NO | | The text content of the post (max 500 characters) |
| image_url | text | YES | | URL to an image associated with the post |
| created_at | timestamptz | NO | now() | Timestamp when the post was created |
| updated_at | timestamptz | NO | now() | Timestamp when the post was last updated |
| city_slug | text | YES | | Slug version of the city name (for location filtering) |
| state_slug | text | YES | | Slug version of the state name (for location filtering) |
| locality_slug | text | YES | | Slug version of the locality name (for location filtering) |
| pincode | text | YES | | Pincode/ZIP code (for location filtering) |
| product_ids | uuid[] | YES | '{}' | Array of product IDs linked to the post |
| mentioned_business_ids | uuid[] | YES | '{}' | Array of business IDs mentioned in the post |

## Constraints

### Primary Key
- `business_posts_pkey` - Primary key constraint on the `id` column

### Foreign Keys
- `business_posts_business_id_fkey` - Foreign key constraint linking `business_id` to `business_profiles.id`

### Check Constraints
- `business_posts_content_length_check` - Ensures content is no longer than 500 characters
  ```sql
  CHECK (length(content) <= 500)
  ```

## Triggers

### update_business_posts_updated_at_trigger
- **Event**: UPDATE
- **Function**: update_business_posts_updated_at()
- **Description**: Automatically updates the `updated_at` field to the current timestamp whenever a post is updated

### Trigger Function Definition
```sql
CREATE OR REPLACE FUNCTION update_business_posts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| business_posts_pkey | UNIQUE | id | Primary key index |
| idx_business_posts_business_id | BTREE | business_id | Index for faster business lookups |
| idx_business_posts_city_slug | BTREE | city_slug | Index for filtering by city |
| idx_business_posts_created_at | BTREE | created_at | Index for sorting by creation time |

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| business_posts_select_policy | SELECT | true | |
| business_posts_insert_policy | INSERT | (auth.role() = 'authenticated') | (auth.uid() = business_id) |
| business_posts_update_policy | UPDATE | (auth.role() = 'authenticated' AND auth.uid() = business_id) | |
| business_posts_delete_policy | DELETE | (auth.role() = 'authenticated' AND auth.uid() = business_id) | |

These policies ensure that:
1. Anyone can view posts (public read access)
2. Only authenticated business users can create posts, and only for their own business
3. Business users can only update or delete their own posts

## Related Tables

### business_profiles
The `business_profiles` table is referenced by the `business_id` foreign key and contains information about the business that created the post.

### subscriptions
The `subscriptions` table is used to determine which posts should appear in a user's feed when filtering by subscribed businesses.

## Usage Notes

1. **Post Creation and Management**:
   - Only business users can create posts
   - Posts are created directly from the Feed page in the business dashboard using a Facebook-style post creation interface
   - The post creation form appears at the top of the feed, above all existing posts
   - The Posts management page is used for updating and deleting existing posts
   - The direct foreign key relationship to business_profiles ensures data integrity
   - In the future, a separate customer_posts table can be created for customer posts

2. **Feed Display and Filtering**:
   - Posts are displayed using a smart algorithm that combines location proximity and user interests
   - Available filter types:
     - **Smart Feed** (default): Proximity + interest scoring algorithm
     - **Subscribed Only**: Only posts from subscribed businesses
     - **Locality**: Posts from same locality only
     - **Pincode**: Posts from same pincode only
     - **City**: Posts from same city only
     - **State**: Posts from same state only
     - **All**: All posts (no filtering)

3. **Smart Feed Algorithm**:
   - **Implementation**: Uses direct database queries with OR conditions for optimal performance
   - **Priority Logic**: Combines subscriptions and location-based filtering
   - **Location Hierarchy**: locality → pincode → city → state → all India
   - **Query Structure**: Uses Supabase `.or()` method to combine multiple conditions
   - **Performance**: Database-level filtering and pagination for efficient infinite scroll

4. **Location Data**:
   - Location fields (city_slug, state_slug, locality_slug, pincode) are copied from the business's profile
   - This denormalization enables efficient filtering by location without joins

5. **Content Limitations**:
   - Post content is limited to 500 characters to keep posts concise
   - Image URLs are optional, allowing text-only or image+text posts

6. **Security**:
   - RLS policies ensure businesses can only create, edit, and delete their own posts
   - All posts are publicly readable to support the feed feature

7. **Performance Considerations**:
   - Indexes on business_id, city_slug, and created_at support efficient filtering and sorting
   - The denormalized location data avoids expensive joins when filtering by location
   - Smart feed algorithm uses direct queries with OR conditions for optimal performance
   - Database-level pagination and sorting for efficient infinite scroll

8. **Address Display**:
   - Posts display the address from the business_posts table (locality_slug, city_slug, state_slug, pincode)
   - This shows the location where the post was created, not the current business address
   - Address is formatted and displayed with a map pin icon for better UX
   - Business name links to public card page using URL structure: domain.com/business_slug

9. **Contact Integration**:
   - Each post includes WhatsApp and Call buttons positioned below the image and products
   - WhatsApp button is disabled if the whatsapp_number field is null or empty
   - Call button is disabled if the phone field is null or empty
   - Contact information is fetched from the business_profiles table (phone, whatsapp_number)
   - WhatsApp opens with a pre-filled message mentioning the post
   - Buttons take equal width and are styled consistently

10. **Product Linking**:
    - Businesses can link up to 10 products to posts using the `product_ids` array
    - Linked products are displayed in the post using the standard product card component
    - Products show name, image, base price, and discount information
    - Clicking on a linked product opens the product page in a new tab
    - Businesses can search for their products by name when creating posts
    - Businesses can only link their own products to their posts
    - A database constraint ensures the maximum limit of 10 products per post
    - This feature helps businesses promote their products directly in their social feed

11. **Future Expansion**:
   - The table design supports future features like:
     - Post reactions/comments (via related tables)
     - Enhanced media support (multiple images, videos, etc.)
   - A separate customer_posts table can be added when customer posting functionality is needed
