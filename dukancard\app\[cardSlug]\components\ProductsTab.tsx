"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { motion } from "framer-motion";
// Note: We're using direct import for framer-motion here, as dynamic import was causing issues
import { Loader2, Search, Filter, SortAsc, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import ProductListItem from "@/app/components/ProductListItem";

import ProductGridSkeleton from "./ProductGridSkeleton";
import { ProductServiceData } from "@/app/(dashboard)/dashboard/business/products/actions";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import { ProductSortBy } from "@/app/(dashboard)/dashboard/business/products/actions";
import { fetchMoreProducts, PublicProductSortBy } from "@/app/[cardSlug]/actions";
import { toast } from "sonner";
// Import product sort options from discovery page
import { ProductSortOption } from "@/app/(main)/discover/context/types";

// Define the BusinessProfile type
type BusinessProfile = BusinessCardData & {
  total_reviews?: number;
  subscription_status?: string;
  has_active_subscription?: boolean;
  trial_end_date?: Date | string | null;
};

interface ProductsTabProps {
  businessProfile: BusinessProfile;
  initialProducts: ProductServiceData[];
  totalProductCount: number;
  defaultSortPreference: ProductSortBy;

}

export default function ProductsTab({
  businessProfile,
  initialProducts,
  totalProductCount,
  defaultSortPreference,
}: ProductsTabProps) {
  const PRODUCTS_PER_PAGE = 20;

  // Map ProductSortBy to ProductSortOption
  const mapSortByToProductSort = (sortBy: ProductSortBy): ProductSortOption => {
    switch (sortBy) {
      case "created_desc":
        return "newest";
      case "name_asc":
        return "name_asc";
      case "name_desc":
        return "name_desc";
      case "price_asc":
        return "price_low";
      case "price_desc":
        return "price_high";
      default:
        return "newest";
    }
  };

  // Map ProductSortOption to PublicProductSortBy (for public-facing pages)
  const mapProductSortToSortBy = (
    sortOption: ProductSortOption
  ): PublicProductSortBy => {
    switch (sortOption) {
      case "newest":
        return "created_desc";
      case "name_asc":
        return "name_asc";
      case "name_desc":
        return "name_desc";
      case "price_low":
        return "price_asc";
      case "price_high":
        return "price_desc";
      default:
        return "created_desc";
    }
  };

  // State variables
  const [products, setProducts] =
    useState<ProductServiceData[]>(initialProducts);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [currentSortOrder, setCurrentSortOrder] = useState<PublicProductSortBy>(
    defaultSortPreference as PublicProductSortBy
  );
  // Discovery page compatible sort option
  const [currentDiscoverySortOrder, setCurrentDiscoverySortOrder] =
    useState<ProductSortOption>(mapSortByToProductSort(defaultSortPreference));
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(
    initialProducts.length < totalProductCount
  );

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState<string>("");
  const [productFilterBy, setProductFilterBy] = useState<string>("all");
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Ref for infinite scrolling
  const loadMoreRef = useRef<HTMLDivElement>(null);

  // Handle search input change with debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);

    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = null;
    }

    // Check if the user has manually cleared the search
    const wasManuallyCleared = debouncedSearchQuery.length > 0 && query === "";

    // If the search was manually cleared, trigger search immediately
    if (wasManuallyCleared) {
      setDebouncedSearchQuery("");
      // Add a small visual delay before triggering the search
      setTimeout(() => {
        handleSearch("");
      }, 100);
      return;
    }

    // Only set a new timeout if the query is empty or at least 3 characters
    if (query === "" || query.length >= 3) {
      searchTimeoutRef.current = setTimeout(() => {
        setDebouncedSearchQuery(query);
        handleSearch(query);
      }, 500);
    }
  };

  // Handle clear search when clicking the X button
  const handleClearSearch = () => {
    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = null;
    }

    setSearchQuery("");
    setDebouncedSearchQuery("");

    // Add a small visual delay before triggering the search
    setTimeout(() => {
      handleSearch("");
    }, 100);
  };

  // Handle filter change
  const handleFilterChange = (value: string) => {
    setProductFilterBy(value);
    handleSearch(debouncedSearchQuery, value);
  };

  // Handle search with current filters
  const handleSearch = useCallback(
    async (searchTerm: string, filterValue: string = productFilterBy) => {
      setCurrentPage(1); // Reset page number
      setProducts([]); // Clear existing products
      setHasMore(true); // Assume there are more until fetch confirms otherwise
      setIsLoadingMore(true); // Show loading state

      const result = await fetchMoreProducts(
        businessProfile.id!,
        1, // Fetch first page
        currentSortOrder,
        PRODUCTS_PER_PAGE,
        searchTerm,
        filterValue === "all" ? null : filterValue
      );

      if (result.data) {
        setProducts(result.data);
        setHasMore(result.data.length < (result.totalCount || 0));
      } else if (result.error) {
        toast.error(`Failed to load products: ${result.error}`);
        setHasMore(false); // Stop loading if error
      } else {
        setHasMore(false); // Stop loading if no data
      }
      setIsLoadingMore(false);
    },
    [
      businessProfile.id,
      currentSortOrder,
      productFilterBy,
      setCurrentPage,
      setHasMore,
      setIsLoadingMore,
      setProducts,
    ]
  );

  // Handle sort change
  const handleSortChange = useCallback(
    async (newSortOption: ProductSortOption) => {
      const newSortOrder = mapProductSortToSortBy(newSortOption);
      if (newSortOrder === currentSortOrder) return;

      setCurrentDiscoverySortOrder(newSortOption);
      setCurrentSortOrder(newSortOrder);
      setCurrentPage(1); // Reset page number
      setProducts([]); // Clear existing products
      setHasMore(true); // Assume there are more until fetch confirms otherwise
      setIsLoadingMore(true); // Show loading state

      const result = await fetchMoreProducts(
        businessProfile.id!,
        1, // Fetch first page
        newSortOrder,
        PRODUCTS_PER_PAGE,
        debouncedSearchQuery || null,
        productFilterBy === "all" ? null : productFilterBy
      );

      if (result.data) {
        setProducts(result.data);
        setHasMore(result.data.length < (result.totalCount || 0));
      } else if (result.error) {
        toast.error(`Failed to load products: ${result.error}`);
        setHasMore(false); // Stop loading if error
      } else {
        setHasMore(false); // Stop loading if no data
      }
      setIsLoadingMore(false);
    },
    [
      businessProfile.id,
      currentSortOrder,
      debouncedSearchQuery,
      productFilterBy,
      setCurrentPage,
      setCurrentSortOrder,
      setHasMore,
      setIsLoadingMore,
      setProducts,
    ]
  );

  // Load more products for infinite scrolling
  const loadMoreProducts = useCallback(async () => {
    if (isLoadingMore || !hasMore) return;

    setIsLoadingMore(true);
    const nextPage = currentPage + 1;
    const result = await fetchMoreProducts(
      businessProfile.id!, // Assert non-null as profile must exist
      nextPage,
      currentSortOrder,
      PRODUCTS_PER_PAGE,
      debouncedSearchQuery || null,
      productFilterBy === "all" ? null : productFilterBy
    );

    if (result.data && result.data.length > 0) {
      setProducts((prev) => [...prev, ...result.data!]);
      setCurrentPage(nextPage);
      // Recalculate hasMore based on the updated products length
      setHasMore(
        products.length + result.data.length <
          (result.totalCount || totalProductCount)
      );
    } else if (result.error) {
      toast.error(`Failed to load more products: ${result.error}`);
      setHasMore(false); // Stop loading if error
    } else {
      setHasMore(false); // Stop loading if no more data
    }

    setIsLoadingMore(false);
  }, [
    isLoadingMore,
    hasMore,
    currentPage,
    businessProfile.id,
    currentSortOrder,
    debouncedSearchQuery,
    productFilterBy,
    products.length,
    totalProductCount,
    setIsLoadingMore,
    setProducts,
    setCurrentPage,
    setHasMore,
  ]);

  // Set up intersection observer for infinite scrolling
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoadingMore) {
          loadMoreProducts();
        }
      },
      { threshold: 0.1 }
    );

    const currentRef = loadMoreRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [hasMore, isLoadingMore, loadMoreProducts]);

  return (
    <div>
      {/* Search and Filter Controls */}
      <motion.div
        className="relative overflow-hidden bg-transparent p-5 rounded-2xl border border-neutral-200/80 dark:border-neutral-800/80 transition-all duration-300 mb-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        {/* Decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-50">
          <div className="absolute -top-24 -right-24 w-48 h-48 rounded-full bg-[var(--brand-gold-rgb)]/5 blur-2xl dark:bg-[var(--brand-gold-rgb)]/10"></div>
          <div className="absolute -bottom-24 -left-24 w-48 h-48 rounded-full bg-[var(--brand-gold-rgb)]/5 blur-2xl dark:bg-[var(--brand-gold-rgb)]/10"></div>
        </div>

        <div className="relative z-10 flex flex-col sm:flex-row gap-3 sm:gap-5 items-start sm:items-center justify-between">
          {/* Search Input */}
          <div className="relative w-full sm:w-auto sm:flex-1 max-w-full sm:max-w-md">
            <form
              onSubmit={(e) => {
                e.preventDefault();
                // Only trigger search if query is empty or at least 3 characters
                if (searchQuery === "" || searchQuery.length >= 3) {
                  // Clear any existing timeout
                  if (searchTimeoutRef.current) {
                    clearTimeout(searchTimeoutRef.current);
                    searchTimeoutRef.current = null;
                  }
                  setDebouncedSearchQuery(searchQuery);
                  handleSearch(searchQuery);
                }
              }}
              className="w-full"
            >
              <div className="relative group w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--brand-gold)] transition-all duration-200" />
                <Input
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="pl-10 h-9 sm:h-10 w-full bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm"
                />
                {searchQuery && (
                  <button
                    type="button"
                    onClick={handleClearSearch}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors duration-200"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
                {/* Hidden submit button for form submission */}
                <button type="submit" className="hidden">
                  Search
                </button>
              </div>
            </form>
          </div>

          <div className="flex flex-row gap-2 sm:gap-3 w-full sm:w-auto sm:flex-shrink-0">
            {/* Filter Dropdown */}
            <Select
              value={productFilterBy}
              onValueChange={handleFilterChange}
              disabled={isLoadingMore}
            >
              <SelectTrigger className="w-full sm:min-w-[140px] h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm">
                <div className="flex items-center">
                  <Filter className="mr-1 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 text-[var(--brand-gold)]" />
                  <SelectValue
                    placeholder="Filter"
                    className="text-xs sm:text-sm truncate"
                  />
                </div>
              </SelectTrigger>
              <SelectContent className="min-w-[180px]">
                <SelectGroup>
                  <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5">
                    Product Type
                  </SelectLabel>
                  <SelectItem value="all" className="relative pl-8">
                    All Products
                  </SelectItem>
                  <SelectItem value="physical" className="relative pl-8">
                    Physical Items
                  </SelectItem>
                  <SelectItem value="service" className="relative pl-8">
                    Services
                  </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>

            {/* Sort Dropdown - Using Discovery Page Sort Options */}
            <Select
              value={currentDiscoverySortOrder}
              onValueChange={(value) =>
                handleSortChange(value as ProductSortOption)
              }
              disabled={isLoadingMore}
            >
              <SelectTrigger className="w-full sm:min-w-[160px] h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm">
                <div className="flex items-center">
                  <SortAsc className="mr-1 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 text-[var(--brand-gold)]" />
                  <SelectValue
                    placeholder="Sort by"
                    className="text-xs sm:text-sm truncate"
                  />
                </div>
              </SelectTrigger>
              <SelectContent className="min-w-[200px]">
                <SelectGroup>
                  <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5">
                    Date
                  </SelectLabel>
                  <SelectItem value="newest" className="relative pl-8">
                    Newest First
                  </SelectItem>
                </SelectGroup>
                <SelectGroup>
                  <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1">
                    Price
                  </SelectLabel>
                  <SelectItem value="price_low" className="relative pl-8">
                    Price: Low to High
                  </SelectItem>
                  <SelectItem value="price_high" className="relative pl-8">
                    Price: High to Low
                  </SelectItem>
                </SelectGroup>
                <SelectGroup>
                  <SelectLabel className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1">
                    Name
                  </SelectLabel>
                  <SelectItem value="name_asc" className="relative pl-8">
                    Name: A to Z
                  </SelectItem>
                  <SelectItem value="name_desc" className="relative pl-8">
                    Name: Z to A
                  </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
        </div>
      </motion.div>

      {/* Results Count */}
      <motion.div
        className="flex items-center justify-between px-2 mb-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <div className="text-xs sm:text-sm text-neutral-500 dark:text-neutral-400 truncate">
          {debouncedSearchQuery && (
            <span>
              Showing results for{" "}
              <span className="font-medium text-neutral-700 dark:text-neutral-300 max-w-[150px] sm:max-w-none inline-block truncate">
                &quot;{debouncedSearchQuery}&quot;
              </span>
            </span>
          )}
          {productFilterBy !== "all" && (
            <span>
              {debouncedSearchQuery ? " • " : ""}
              <span className="font-medium text-neutral-700 dark:text-neutral-300">
                {productFilterBy === "physical" ? "Physical Items" : "Services"}
              </span>
            </span>
          )}
        </div>

        {debouncedSearchQuery && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearSearch}
            className="text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 h-8 px-2 py-1"
          >
            <X className="h-3.5 w-3.5 mr-1" />
            Clear Search
          </Button>
        )}
      </motion.div>

      {/* Product Grid and Loading/End Indicators */}
      {isLoadingMore && products.length === 0 ? (
        <ProductGridSkeleton />
      ) : products.length > 0 ? (
        <motion.div
          className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 sm:gap-3 md:gap-4 overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {products.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <Link href={`/${businessProfile.business_slug}/product/${product.slug || product.id}`} className="block h-full">
                <ProductListItem product={product} isLink={false} />
              </Link>
            </motion.div>
          ))}
        </motion.div>
      ) : (
        <motion.div
          className="text-center py-16 px-4 bg-transparent rounded-2xl border border-neutral-200/80 dark:border-neutral-800/80"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          <div className="max-w-md mx-auto">
            <h3 className="text-xl font-semibold text-neutral-800 dark:text-neutral-200 mb-2">
              No Products Found
            </h3>
            <p className="text-neutral-500 dark:text-neutral-400 mb-4">
              We couldn&apos;t find any products
              {debouncedSearchQuery
                ? ` with "${debouncedSearchQuery}" in the name`
                : ""}
              {productFilterBy !== "all" &&
                ` in the ${
                  productFilterBy === "physical" ? "Physical Items" : "Services"
                } category`}
              . Try adjusting your search criteria or browse all products.
            </p>
            {(debouncedSearchQuery || productFilterBy !== "all") && (
              <Button
                variant="outline"
                className="mt-2 border-[var(--brand-gold)]/30 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10"
                onClick={handleClearSearch}
              >
                <X className="h-4 w-4 mr-2" />
                Clear Filters
              </Button>
            )}
          </div>
        </motion.div>
      )}

      {/* Infinite scroll trigger and end message */}
      {hasMore && (
        <div ref={loadMoreRef} className="text-center py-10">
          {isLoadingMore && products.length > 0 && (
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-neutral-500" />
          )}
        </div>
      )}
      {!hasMore && products.length > 0 && (
        <div className="text-center py-10 text-sm text-neutral-500">
          You&apos;ve reached the end.
        </div>
      )}
    </div>
  );
}
