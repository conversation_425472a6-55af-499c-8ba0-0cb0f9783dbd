// Auto-generated encrypted keys - DO NOT EDIT MANUALLY
// Generated on 2025-06-24T15:22:29.778Z

#include <jni.h>
#include <string.h>

// Simple XOR encryption for runtime decryption
static void xor_decrypt(char* data, int len, const char* key) {
    int key_len = strlen(key);
    for (int i = 0; i < len; i++) {
        data[i] ^= key[i % key_len];
    }
}

// SUPABASE_URL
static const char encrypted_supabase_url[] = {
    0x0c, 0x1f, 0x46, 0x40, 0x41, 0x0f, 0x4b, 0x44, 0x40, 0x5e, 0x58, 0x5a, 0x08, 0x08, 0x5d, 0x55,
    0x51, 0x5a, 0x03, 0x11, 0x55, 0x57, 0x5e, 0x5b, 0x06, 0x07, 0x43, 0x5e, 0x1c, 0x46, 0x11, 0x1b,
    0x53, 0x52, 0x53, 0x46, 0x01, 0x45, 0x51, 0x5f,
    0x00
};

// SUPABASE_ANON
static const char encrypted_supabase_anon[] = {
    0x01, 0x12, 0x78, 0x58, 0x50, 0x72, 0x07, 0x02, 0x7d, 0x59, 0x78, 0x7c, 0x31, 0x11, 0x7b, 0x01,
    0x7c, 0x5c, 0x2d, 0x18, 0x7b, 0x5e, 0x60, 0x00, 0x07, 0x28, 0x7b, 0x06, 0x7b, 0x5e, 0x14, 0x33,
    0x64, 0x73, 0x78, 0x0c, 0x4a, 0x0e, 0x4b, 0x7a, 0x42, 0x56, 0x57, 0x26, 0x5b, 0x7f, 0x5b, 0x7f,
    0x1e, 0x0f, 0x6a, 0x72, 0x5a, 0x6c, 0x09, 0x2d, 0x48, 0x6a, 0x61, 0x7c, 0x17, 0x22, 0x5c, 0x7a,
    0x5e, 0x6f, 0x0d, 0x22, 0x04, 0x79, 0x5c, 0x7f, 0x11, 0x0a, 0x5f, 0x09, 0x41, 0x6c, 0x56, 0x52,
    0x5e, 0x69, 0x00, 0x0c, 0x0a, 0x0e, 0x5f, 0x54, 0x5c, 0x57, 0x23, 0x5e, 0x5b, 0x52, 0x7a, 0x73,
    0x11, 0x22, 0x5b, 0x47, 0x5b, 0x56, 0x09, 0x52, 0x41, 0x6a, 0x61, 0x7c, 0x52, 0x22, 0x5f, 0x76,
    0x47, 0x57, 0x56, 0x5f, 0x5b, 0x7c, 0x71, 0x7f, 0x14, 0x32, 0x6a, 0x61, 0x5b, 0x7a, 0x0e, 0x2e,
    0x01, 0x7e, 0x76, 0x78, 0x13, 0x25, 0x66, 0x79, 0x45, 0x7b, 0x30, 0x32, 0x41, 0x79, 0x5f, 0x63,
    0x50, 0x08, 0x71, 0x79, 0x04, 0x78, 0x0e, 0x2a, 0x03, 0x7f, 0x76, 0x6c, 0x1d, 0x24, 0x76, 0x71,
    0x03, 0x7b, 0x0a, 0x5b, 0x1c, 0x5b, 0x0a, 0x71, 0x11, 0x1d, 0x7d, 0x42, 0x40, 0x7e, 0x35, 0x07,
    0x44, 0x48, 0x75, 0x57, 0x51, 0x1a, 0x76, 0x07, 0x0a, 0x6a, 0x12, 0x33, 0x76, 0x41, 0x7b, 0x67,
    0x0f, 0x06, 0x59, 0x07, 0x68, 0x67, 0x31, 0x01, 0x03, 0x78, 0x50, 0x00, 0x34, 0x27, 0x06, 0x5f,
    0x00
};

// SUPABASE_ADMIN
static const char encrypted_supabase_admin[] = {
    0x01, 0x12, 0x78, 0x58, 0x50, 0x72, 0x07, 0x02, 0x7d, 0x59, 0x78, 0x7c, 0x31, 0x11, 0x7b, 0x01,
    0x7c, 0x5c, 0x2d, 0x18, 0x7b, 0x5e, 0x60, 0x00, 0x07, 0x28, 0x7b, 0x06, 0x7b, 0x5e, 0x14, 0x33,
    0x64, 0x73, 0x78, 0x0c, 0x4a, 0x0e, 0x4b, 0x7a, 0x42, 0x56, 0x57, 0x26, 0x5b, 0x7f, 0x5b, 0x7f,
    0x1e, 0x0f, 0x6a, 0x72, 0x5a, 0x6c, 0x09, 0x2d, 0x48, 0x6a, 0x61, 0x7c, 0x17, 0x22, 0x5c, 0x7a,
    0x5e, 0x6f, 0x0d, 0x22, 0x04, 0x79, 0x5c, 0x7f, 0x11, 0x0a, 0x5f, 0x09, 0x41, 0x6c, 0x56, 0x52,
    0x5e, 0x69, 0x00, 0x0c, 0x0a, 0x0e, 0x5f, 0x54, 0x5c, 0x57, 0x23, 0x5e, 0x5b, 0x52, 0x7a, 0x73,
    0x11, 0x22, 0x5b, 0x47, 0x5b, 0x56, 0x09, 0x52, 0x41, 0x6a, 0x61, 0x7c, 0x52, 0x22, 0x5c, 0x7e,
    0x5e, 0x56, 0x0a, 0x31, 0x42, 0x69, 0x00, 0x63, 0x02, 0x08, 0x5f, 0x09, 0x41, 0x6f, 0x37, 0x22,
    0x41, 0x79, 0x5f, 0x59, 0x0c, 0x0f, 0x71, 0x79, 0x04, 0x78, 0x30, 0x08, 0x02, 0x7d, 0x48, 0x74,
    0x55, 0x26, 0x58, 0x71, 0x03, 0x7b, 0x0d, 0x1c, 0x5b, 0x6a, 0x6a, 0x5d, 0x13, 0x22, 0x58, 0x5f,
    0x4b, 0x78, 0x20, 0x3e, 0x06, 0x7e, 0x58, 0x7c, 0x50, 0x26, 0x76, 0x65, 0x00, 0x53, 0x35, 0x45,
    0x7a, 0x77, 0x78, 0x18, 0x49, 0x3d, 0x67, 0x04, 0x79, 0x51, 0x08, 0x08, 0x40, 0x64, 0x0b, 0x71,
    0x17, 0x0a, 0x00, 0x42, 0x65, 0x60, 0x34, 0x25, 0x46, 0x7c, 0x6d, 0x57, 0x3d, 0x23, 0x77, 0x5c,
    0x04, 0x6c, 0x02, 0x08, 0x48, 0x62, 0x79, 0x0d, 0x2d, 0x5b, 0x77,
    0x00
};

// GOOGLE_ANDROID
static const char encrypted_google_android[] = {
    0x55, 0x5a, 0x02, 0x09, 0x0b, 0x04, 0x5d, 0x5c, 0x00, 0x04, 0x05, 0x04, 0x49, 0x09, 0x53, 0x45,
    0x43, 0x0d, 0x07, 0x06, 0x5e, 0x5c, 0x0b, 0x5b, 0x08, 0x19, 0x56, 0x5c, 0x04, 0x5a, 0x0f, 0x5c,
    0x41, 0x46, 0x44, 0x41, 0x51, 0x19, 0x55, 0x56, 0x47, 0x5d, 0x0a, 0x04, 0x05, 0x1e, 0x53, 0x45,
    0x14, 0x18, 0x1c, 0x57, 0x5d, 0x5a, 0x03, 0x07, 0x57, 0x45, 0x41, 0x50, 0x16, 0x08, 0x5d, 0x5e,
    0x46, 0x50, 0x0a, 0x1f, 0x1c, 0x53, 0x5d, 0x58,
    0x00
};

// GOOGLE_IOS
static const char encrypted_google_ios[] = {
    0x55, 0x5a, 0x02, 0x09, 0x0b, 0x04, 0x5d, 0x5c, 0x00, 0x04, 0x05, 0x04, 0x49, 0x52, 0x55, 0x05,
    0x5a, 0x57, 0x11, 0x01, 0x05, 0x08, 0x41, 0x0d, 0x5c, 0x09, 0x07, 0x56, 0x5b, 0x05, 0x09, 0x52,
    0x05, 0x59, 0x06, 0x5e, 0x54, 0x53, 0x00, 0x03, 0x44, 0x02, 0x50, 0x58, 0x02, 0x1e, 0x53, 0x45,
    0x14, 0x18, 0x1c, 0x57, 0x5d, 0x5a, 0x03, 0x07, 0x57, 0x45, 0x41, 0x50, 0x16, 0x08, 0x5d, 0x5e,
    0x46, 0x50, 0x0a, 0x1f, 0x1c, 0x53, 0x5d, 0x58,
    0x00
};

// GOOGLE_WEB
static const char encrypted_google_web[] = {
    0x55, 0x5a, 0x02, 0x09, 0x0b, 0x04, 0x5d, 0x5c, 0x00, 0x04, 0x05, 0x04, 0x49, 0x0e, 0x59, 0x09,
    0x40, 0x54, 0x54, 0x5a, 0x04, 0x53, 0x53, 0x04, 0x56, 0x1e, 0x51, 0x51, 0x5d, 0x57, 0x0d, 0x07,
    0x00, 0x43, 0x0a, 0x5a, 0x0d, 0x0f, 0x0a, 0x06, 0x59, 0x03, 0x10, 0x04, 0x06, 0x1e, 0x53, 0x45,
    0x14, 0x18, 0x1c, 0x57, 0x5d, 0x5a, 0x03, 0x07, 0x57, 0x45, 0x41, 0x50, 0x16, 0x08, 0x5d, 0x5e,
    0x46, 0x50, 0x0a, 0x1f, 0x1c, 0x53, 0x5d, 0x58,
    0x00
};


// Getter functions
JNIEXPORT jstring JNICALL
Java_com_dukancardapp_dukancard_SecureKeysModule_getSupabaseUrl(JNIEnv *env, jobject instance) {
    char decrypted[256];
    int len = sizeof(encrypted_supabase_url) - 1;
    memcpy(decrypted, encrypted_supabase_url, len);
    decrypted[len] = '\0';
    
    xor_decrypt(decrypted, len, "dk2025");
    
    return (*env)->NewStringUTF(env, decrypted);
}

JNIEXPORT jstring JNICALL
Java_com_dukancardapp_dukancard_SecureKeysModule_getSupabaseAnonKey(JNIEnv *env, jobject instance) {
    char decrypted[512];
    int len = sizeof(encrypted_supabase_anon) - 1;
    memcpy(decrypted, encrypted_supabase_anon, len);
    decrypted[len] = '\0';

    xor_decrypt(decrypted, len, "dk2025");

    return (*env)->NewStringUTF(env, decrypted);
}

JNIEXPORT jstring JNICALL
Java_com_dukancardapp_dukancard_SecureKeysModule_getSupabaseAdminKey(JNIEnv *env, jobject instance) {
    char decrypted[512];
    int len = sizeof(encrypted_supabase_admin) - 1;
    memcpy(decrypted, encrypted_supabase_admin, len);
    decrypted[len] = '\0';

    xor_decrypt(decrypted, len, "dk2025");

    return (*env)->NewStringUTF(env, decrypted);
}

JNIEXPORT jstring JNICALL
Java_com_dukancardapp_dukancard_SecureKeysModule_getGoogleAndroidClientId(JNIEnv *env, jobject instance) {
    char decrypted[256];
    int len = sizeof(encrypted_google_android) - 1;
    memcpy(decrypted, encrypted_google_android, len);
    decrypted[len] = '\0';
    
    xor_decrypt(decrypted, len, "dk2025");
    
    return (*env)->NewStringUTF(env, decrypted);
}

JNIEXPORT jstring JNICALL
Java_com_dukancardapp_dukancard_SecureKeysModule_getGoogleIosClientId(JNIEnv *env, jobject instance) {
    char decrypted[256];
    int len = sizeof(encrypted_google_ios) - 1;
    memcpy(decrypted, encrypted_google_ios, len);
    decrypted[len] = '\0';
    
    xor_decrypt(decrypted, len, "dk2025");
    
    return (*env)->NewStringUTF(env, decrypted);
}

JNIEXPORT jstring JNICALL
Java_com_dukancardapp_dukancard_SecureKeysModule_getGoogleWebClientId(JNIEnv *env, jobject instance) {
    char decrypted[256];
    int len = sizeof(encrypted_google_web) - 1;
    memcpy(decrypted, encrypted_google_web, len);
    decrypted[len] = '\0';
    
    xor_decrypt(decrypted, len, "dk2025");
    
    return (*env)->NewStringUTF(env, decrypted);
}
