"use client";

import { useSearchParams } from "next/navigation";
import { useTransition } from "react";
import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { searchCategoryCombined, fetchMoreProductsByCategoryCombined } from "../actions";
import { CATEGORY_PRODUCTS_PER_PAGE } from "../constants/paginationConstants";
import { NearbyProduct } from "@/app/(main)/discover/actions/types";
import {
  PRODUCT_NAME_PARAM,
  PRODUCT_SORT_PARAM,
  PRODUCT_TYPE_PARAM,
  PINCODE_PARAM,
  LOCALITY_PARAM,
} from "@/app/(main)/discover/constants/urlParamConstants";
import {
  CategorySearchResult,
  ProductFilterOption,
  ProductSortOption,
  ViewType,
  SerializableCategory,
} from "./types";
import { mapProductSortToBackend } from "@/app/(main)/discover/utils/sortMappings";

// Product context functions
export function useProductContextFunctions(
  category: SerializableCategory,
  viewType: ViewType,
  setIsSearching: (_value: boolean) => void,
  setSearchResult: (_value: CategorySearchResult | null) => void,
  setIsAuthenticated: (_value: boolean) => void,
  setProducts: (_value: NearbyProduct[]) => void,
  setHasMore: (_value: boolean) => void,
  setTotalCount: (_value: number) => void,
  setCurrentPage: (_value: number) => void,
  setProductSortBy: (_value: ProductSortOption) => void,
  setProductFilterBy: (_value: ProductFilterOption) => void,
  setSearchError: (_value: string | null) => void,
  products: NearbyProduct[],
  businessSort: BusinessSortBy,
  productSortBy: ProductSortOption,
  productFilterBy: ProductFilterOption,
  locationInfo?: { state?: string | null; city?: string | null; pincode?: string | null; locality?: string | null }
) {
  const searchParams = useSearchParams();
  const [isPending, startSearchTransition] = useTransition();

  // Handle product sort change
  const handleProductSortChange = (newSortBy: ProductSortOption) => {
    if (newSortBy === productSortBy) return;

    // Update URL params
    const url = new URL(window.location.href);
    url.searchParams.set(PRODUCT_SORT_PARAM, newSortBy);
    window.history.pushState({}, "", url.toString());

    // Update state
    setProductSortBy(newSortBy);

    // Only proceed if we're in the products view
    if (viewType !== "products") return;

    // Set loading state
    setIsSearching(true);

    // Get current search params
    const currentSearchTerm = searchParams.get(PRODUCT_NAME_PARAM);
    const pincode = searchParams.get(PINCODE_PARAM);
    const locality = searchParams.get(LOCALITY_PARAM);

    startSearchTransition(async () => {
      try {
        // Add a small delay to prevent race conditions
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Log the pincode value being used
        console.log(`handleProductSortChange using pincode: ${pincode}, from locationInfo: ${locationInfo?.pincode}`);

        // Use pincode from locationInfo if it's not in the URL params
        const effectivePincode = pincode || locationInfo?.pincode || null;

        const result = await searchCategoryCombined({
          categoryName: category.name,
          productName: currentSearchTerm,
          state: locationInfo?.state || null, // Include state from locationInfo
          city: locationInfo?.city || null, // Include city from locationInfo
          pincode: effectivePincode, // Use the effective pincode
          locality,
          viewType,
          page: 1,
          limit: CATEGORY_PRODUCTS_PER_PAGE,
          productSort: mapProductSortToBackend(newSortBy),
          productType: productFilterBy === "all" ? null : productFilterBy,
        });

        if (result.data) {
          setSearchResult(result.data);
          setIsAuthenticated(result.data.isAuthenticated);
          setProducts(result.data.products || []);
          setHasMore(result.data.hasMore);
          setTotalCount(result.data.totalCount);
          setCurrentPage(result.data.nextPage ? 2 : 1);
        } else if (result.error) {
          setSearchError(result.error);
        }
      } catch (error) {
        setSearchError("An error occurred while sorting products.");
        console.error("Error sorting products:", error);
      } finally {
        setIsSearching(false);
      }
    });
  };

  // Handle product filter change
  const handleProductFilterChange = (newFilter: ProductFilterOption) => {
    if (newFilter === productFilterBy) return;

    // Update URL params
    const url = new URL(window.location.href);
    url.searchParams.set(PRODUCT_TYPE_PARAM, newFilter);
    window.history.pushState({}, "", url.toString());

    // Update state
    setProductFilterBy(newFilter);

    // Only proceed if we're in the products view
    if (viewType !== "products") return;

    // Set loading state
    setIsSearching(true);

    // Get current search params
    const currentSearchTerm = searchParams.get(PRODUCT_NAME_PARAM);
    const pincode = searchParams.get(PINCODE_PARAM);
    const locality = searchParams.get(LOCALITY_PARAM);

    startSearchTransition(async () => {
      try {
        // Add a small delay to prevent race conditions
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Log the pincode value being used
        console.log(`handleProductFilterChange using pincode: ${pincode}, from locationInfo: ${locationInfo?.pincode}`);

        // Use pincode from locationInfo if it's not in the URL params
        const effectivePincode = pincode || locationInfo?.pincode || null;

        const result = await searchCategoryCombined({
          categoryName: category.name,
          productName: currentSearchTerm,
          state: locationInfo?.state || null, // Include state from locationInfo
          city: locationInfo?.city || null, // Include city from locationInfo
          pincode: effectivePincode, // Use the effective pincode
          locality,
          viewType,
          page: 1,
          limit: CATEGORY_PRODUCTS_PER_PAGE,
          productSort: mapProductSortToBackend(productSortBy),
          productType: newFilter === "all" ? null : newFilter,
        });

        if (result.data) {
          setSearchResult(result.data);
          setIsAuthenticated(result.data.isAuthenticated);
          setProducts(result.data.products || []);
          setHasMore(result.data.hasMore);
          setTotalCount(result.data.totalCount);
          setCurrentPage(result.data.nextPage ? 2 : 1);
        } else if (result.error) {
          setSearchError(result.error);
        }
      } catch (error) {
        setSearchError("An error occurred while filtering products.");
        console.error("Error filtering products:", error);
      } finally {
        setIsSearching(false);
      }
    });
  };

  // Handle product search
  const handleProductSearch = (searchTerm: string) => {
    // Only proceed if we're in the products view
    if (viewType !== "products") return;

    // Update URL params
    const url = new URL(window.location.href);
    if (searchTerm) url.searchParams.set(PRODUCT_NAME_PARAM, searchTerm);
    else url.searchParams.delete(PRODUCT_NAME_PARAM);
    window.history.pushState({}, "", url.toString());

    // Set loading state
    setIsSearching(true);

    // Get current search params
    const pincode = searchParams.get(PINCODE_PARAM);
    const locality = searchParams.get(LOCALITY_PARAM);

    startSearchTransition(async () => {
      try {
        // Add a small delay to prevent race conditions
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Log the pincode value being used
        console.log(`handleProductSearch using pincode: ${pincode}, from locationInfo: ${locationInfo?.pincode}`);

        // Use pincode from locationInfo if it's not in the URL params
        const effectivePincode = pincode || locationInfo?.pincode || null;

        const result = await searchCategoryCombined({
          categoryName: category.name,
          productName: searchTerm,
          state: locationInfo?.state || null, // Include state from locationInfo
          city: locationInfo?.city || null, // Include city from locationInfo
          pincode: effectivePincode, // Use the effective pincode
          locality,
          viewType,
          page: 1,
          limit: CATEGORY_PRODUCTS_PER_PAGE,
          productSort: mapProductSortToBackend(productSortBy),
          productType: productFilterBy === "all" ? null : productFilterBy,
        });

        if (result.data) {
          setSearchResult(result.data);
          setIsAuthenticated(result.data.isAuthenticated);
          setProducts(result.data.products || []);
          setHasMore(result.data.hasMore);
          setTotalCount(result.data.totalCount);
          setCurrentPage(result.data.nextPage ? 2 : 1);
        } else if (result.error) {
          setSearchError(result.error);
        }
      } catch (error) {
        setSearchError("An error occurred while searching products.");
        console.error("Error searching products:", error);
      } finally {
        setIsSearching(false);
      }
    });
  };

  // Load more products
  const loadMoreProducts = async () => {
    if (!products.length) return;

    const pincode = searchParams.get(PINCODE_PARAM);
    const locality = searchParams.get(LOCALITY_PARAM);
    const productName = searchParams.get(PRODUCT_NAME_PARAM);

    try {
      // Use values from locationInfo if they're not in the URL params
      const effectivePincode = pincode || locationInfo?.pincode || undefined;
      const effectiveCity = locationInfo?.city || undefined;

      // Extract state, city, pincode, and locality slugs from the URL
      const urlParts = window.location.pathname.split('/').filter(part => part.trim() !== '');

      // Find the index of the category slug
      const categoryIndex = urlParts.findIndex(part => part === category.slug);

      // Get stateSlug, citySlug, and localitySlug from URL if available
      let stateSlugFromUrl = null;
      let citySlugFromUrl = null;
      let localitySlugFromUrl = null;

      if (categoryIndex >= 0 && categoryIndex + 1 < urlParts.length) {
          stateSlugFromUrl = urlParts[categoryIndex + 1];
      }

      if (categoryIndex >= 0 && categoryIndex + 2 < urlParts.length) {
          citySlugFromUrl = urlParts[categoryIndex + 2];
      }

      // Check for pincode (needed to extract locality slug)
      if (categoryIndex >= 0 && categoryIndex + 3 < urlParts.length) {
          // pincodeFromUrl = urlParts[categoryIndex + 3]; // Currently not used
      }

      // Check for locality slug
      if (categoryIndex >= 0 && categoryIndex + 4 < urlParts.length) {
          localitySlugFromUrl = urlParts[categoryIndex + 4];
      }



      const result = await fetchMoreProductsByCategoryCombined({
        categoryName: category.name,
        page: products.length > 0 ? Math.ceil(products.length / CATEGORY_PRODUCTS_PER_PAGE) + 1 : 1,
        limit: CATEGORY_PRODUCTS_PER_PAGE,
        sortBy: mapProductSortToBackend(productSortBy),
        productType: productFilterBy === "all" ? null : productFilterBy,
        state: locationInfo?.state || undefined, // Include state from locationInfo
        city: effectiveCity, // Include city from locationInfo
        pincode: effectivePincode, // Use the effective pincode
        locality: locality || undefined,
        stateSlug: stateSlugFromUrl, // Pass state slug from URL
        citySlug: citySlugFromUrl, // Pass city slug from URL
        localitySlug: localitySlugFromUrl, // Pass locality slug from URL
        productName: productName || undefined,
      });

      if (result.data) {
        setProducts([...products, ...(result.data.products || [])]);
        setHasMore(result.data.hasMore);
        setCurrentPage(result.data.nextPage || 1);
      } else if (result.error) {
        setSearchError(result.error);
      }
    } catch (error) {
      console.error("Error loading more products:", error);
      setSearchError("Failed to load more products.");
    }
  };

  return {
    isPending,
    handleProductSortChange,
    handleProductSearch,
    handleProductFilterChange,
    loadMoreProducts,
  };
}
