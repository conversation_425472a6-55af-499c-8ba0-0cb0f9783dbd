/**
 * Business Profile Service for Next Js
 * 
 * Handles all business profile operations using direct Supabase client calls
 * with Row Level Security (RLS) policies for security.
 * 
 * This service replaces the need for API routes by leveraging:
 * - RLS policies for security (users can only access their own profiles)
 * - Public read access for profile discovery
 * - Direct Supabase client calls for better performance
 */

import { createClient } from '@/utils/supabase/server';

// Types for business profile operations
export interface BusinessProfile {
  id: string;
  business_name: string;
  business_slug: string;
  contact_email: string;
  member_name: string;
  title: string;
  phone: string;
  business_category: string;
  address_line: string;
  pincode: string;
  city: string;
  state: string;
  locality: string;
  status: 'online' | 'offline';
  logo_url?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateBusinessProfileData {
  business_name: string;
  business_slug: string;
  contact_email: string;
  member_name: string;
  title: string;
  phone: string;
  business_category: string;
  address_line: string;
  pincode: string;
  city: string;
  state: string;
  locality: string;
  status: 'online' | 'offline';
  logo_url?: string;
}

export interface UpdateBusinessProfileData {
  business_name?: string;
  business_slug?: string;
  contact_email?: string;
  member_name?: string;
  title?: string;
  phone?: string;
  business_category?: string;
  address_line?: string;
  pincode?: string;
  city?: string;
  state?: string;
  locality?: string;
  status?: 'online' | 'offline';
  logo_url?: string;
}

export interface ServiceResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Get the current user's business profile
 */
export async function getBusinessProfile(): Promise<ServiceResult<BusinessProfile | null>> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: 'Authentication required' };
    }

    const { data: profile, error } = await supabase
      .from('business_profiles')
      .select('*')
      .eq('id', user.id)
      .maybeSingle();

    if (error) {
      console.error('[BUSINESS_PROFILE_SERVICE] Error fetching profile:', error);
      return { success: false, error: 'Failed to fetch business profile' };
    }

    return { success: true, data: profile };
  } catch (error) {
    console.error('[BUSINESS_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Check if the current user has a business profile
 */
export async function checkBusinessProfileExists(): Promise<ServiceResult<boolean>> {
  try {
    const result = await getBusinessProfile();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    return { success: true, data: !!result.data };
  } catch (error) {
    console.error('[BUSINESS_PROFILE_SERVICE] Error checking profile existence:', error);
    return { success: false, error: 'Failed to check profile existence' };
  }
}

/**
 * Create a new business profile
 * RLS policy ensures users can only create their own profile
 */
export async function createBusinessProfile(
  profileData: CreateBusinessProfileData
): Promise<ServiceResult<BusinessProfile>> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: 'Authentication required' };
    }

    // RLS policy will ensure user can only create their own profile
    const { data: profile, error } = await supabase
      .from('business_profiles')
      .insert({
        id: user.id, // Use authenticated user's ID
        ...profileData,
      })
      .select()
      .single();

    if (error) {
      console.error('[BUSINESS_PROFILE_SERVICE] Error creating profile:', error);
      
      // Handle specific error cases
      if (error.code === '23505') {
        return { success: false, error: 'Business profile already exists' };
      }
      
      return { success: false, error: 'Failed to create business profile' };
    }

    return { success: true, data: profile };
  } catch (error) {
    console.error('[BUSINESS_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Update the current user's business profile
 * RLS policy ensures users can only update their own profile
 */
export async function updateBusinessProfile(
  updates: UpdateBusinessProfileData
): Promise<ServiceResult<BusinessProfile>> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: 'Authentication required' };
    }

    // RLS policy will ensure user can only update their own profile
    const { data: profile, error } = await supabase
      .from('business_profiles')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', user.id)
      .select()
      .single();

    if (error) {
      console.error('[BUSINESS_PROFILE_SERVICE] Error updating profile:', error);
      return { success: false, error: 'Failed to update business profile' };
    }

    return { success: true, data: profile };
  } catch (error) {
    console.error('[BUSINESS_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get a business profile by ID (public read access)
 * Uses public RLS policy for read access
 */
export async function getBusinessProfileById(
  businessId: string
): Promise<ServiceResult<BusinessProfile | null>> {
  try {
    const supabase = await createClient();
    const { data: profile, error } = await supabase
      .from('business_profiles')
      .select('*')
      .eq('id', businessId)
      .maybeSingle();

    if (error) {
      console.error('[BUSINESS_PROFILE_SERVICE] Error fetching profile by ID:', error);
      return { success: false, error: 'Failed to fetch business profile' };
    }

    return { success: true, data: profile };
  } catch (error) {
    console.error('[BUSINESS_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get multiple business profiles by IDs (public read access)
 * Uses public RLS policy for read access
 */
export async function getBusinessProfilesByIds(
  businessIds: string[]
): Promise<ServiceResult<BusinessProfile[]>> {
  try {
    if (!Array.isArray(businessIds) || businessIds.length === 0) {
      return { success: true, data: [] };
    }

    const supabase = await createClient();
    const { data: profiles, error } = await supabase
      .from('business_profiles')
      .select('*')
      .in('id', businessIds);

    if (error) {
      console.error('[BUSINESS_PROFILE_SERVICE] Error fetching profiles by IDs:', error);
      return { success: false, error: 'Failed to fetch business profiles' };
    }

    return { success: true, data: profiles || [] };
  } catch (error) {
    console.error('[BUSINESS_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Check if a business slug is available
 */
export async function checkSlugAvailability(slug: string): Promise<ServiceResult<boolean>> {
  try {
    const supabase = await createClient();
    const { data: existing, error } = await supabase
      .from('business_profiles')
      .select('id')
      .eq('business_slug', slug)
      .maybeSingle();

    if (error) {
      console.error('[BUSINESS_PROFILE_SERVICE] Error checking slug availability:', error);
      return { success: false, error: 'Failed to check slug availability' };
    }

    return { success: true, data: !existing };
  } catch (error) {
    console.error('[BUSINESS_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Delete the current user's business profile
 * RLS policy ensures users can only delete their own profile
 */
export async function deleteBusinessProfile(): Promise<ServiceResult<void>> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: 'Authentication required' };
    }

    // RLS policy will ensure user can only delete their own profile
    const { error } = await supabase
      .from('business_profiles')
      .delete()
      .eq('id', user.id);

    if (error) {
      console.error('[BUSINESS_PROFILE_SERVICE] Error deleting profile:', error);
      return { success: false, error: 'Failed to delete business profile' };
    }

    return { success: true };
  } catch (error) {
    console.error('[BUSINESS_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}
