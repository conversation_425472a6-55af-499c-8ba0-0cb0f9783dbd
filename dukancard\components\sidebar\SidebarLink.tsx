"use client";

import React from "react";
import Link from "next/link";
import { useSidebar } from "@/components/ui/sidebar";

interface SidebarLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
}

export function SidebarLink({ href, children, className }: SidebarLinkProps) {
  const { isMobile, setOpenMobile } = useSidebar();

  const handleClick = () => {
    // Only close the sidebar on mobile devices
    if (isMobile) {
      setOpenMobile(false);
    }
  };

  return (
    <Link href={href} className={className} onClick={handleClick}>
      {children}
    </Link>
  );
}
