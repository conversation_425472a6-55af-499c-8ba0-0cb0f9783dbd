"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Store } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

export default function OfflineBusinessMessage() {
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);

  // Use useEffect to detect client-side rendering
  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-white dark:bg-black">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full bg-white dark:bg-neutral-900 rounded-xl shadow-lg p-8 border border-neutral-200 dark:border-neutral-800 text-center"
      >
        <div className="mb-6 flex justify-center">
          <div className="p-4 bg-neutral-100 dark:bg-neutral-800 rounded-full">
            <Store className="h-12 w-12 text-[var(--brand-gold)]" />
          </div>
        </div>

        <h2 className="text-2xl font-bold mb-3 text-neutral-900 dark:text-neutral-100">
          This Business is Currently Offline
        </h2>
        
        <p className="text-neutral-600 dark:text-neutral-400 mb-6">
          This business card is currently set to private or offline mode. 
          Meanwhile, you can discover other businesses in your locality.
        </p>

        <div className="relative group">
          {/* Button glow effect with properly rounded corners */}
          {isClient && (
            <motion.div
              className="absolute -inset-0.5 rounded-md blur-md"
              style={{
                background: "linear-gradient(to right, rgba(var(--brand-gold-rgb), 0.6), rgba(var(--brand-gold-rgb), 0.8))"
              }}
              initial={{ opacity: 0.7 }}
              animate={{
                opacity: [0.7, 0.9, 0.7],
                boxShadow: [
                  "0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)",
                  "0 0 20px 4px rgba(var(--brand-gold-rgb), 0.5)",
                  "0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)"
                ]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            />
          )}

          <Button
            onClick={() => router.push("/discover")}
            className="relative w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium px-8 py-6 h-12 rounded-md shadow-md hover:shadow-xl transition-all duration-300 cursor-pointer"
          >
            <span className="relative z-10 flex items-center justify-center gap-2">
              Discover Businesses
              <Store className="h-5 w-5" />
            </span>
          </Button>
        </div>
      </motion.div>
    </div>
  );
}
