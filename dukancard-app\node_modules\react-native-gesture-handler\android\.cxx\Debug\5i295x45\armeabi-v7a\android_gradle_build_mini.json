{"buildFiles": ["C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\5i295x45\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\5i295x45\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"gesturehandler::@6890427a1f51a3e7e1df": {"artifactName": "gesturehandler", "abi": "armeabi-v7a", "output": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\5i295x45\\obj\\armeabi-v7a\\libgesturehandler.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6e9cabc4b841ecb8bc48e2ce54799a1d\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6e9cabc4b841ecb8bc48e2ce54799a1d\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so"]}}}