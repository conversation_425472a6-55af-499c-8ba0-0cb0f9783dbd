"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronUp } from "lucide-react";
import { usePathname } from "next/navigation";

interface ScrollToTopButtonProps {
  excludePaths?: string[];
}

export default function ScrollToTopButton({ excludePaths = [] }: ScrollToTopButtonProps) {
  const [isVisible, setIsVisible] = useState(false);
  const pathname = usePathname();

  // Check if current path should be excluded
  const shouldExclude = excludePaths.some(path => pathname.startsWith(path));

  // Show button when page is scrolled down
  useEffect(() => {
    // Don't run effect if path is excluded
    if (shouldExclude) {
      return;
    }
    const toggleVisibility = () => {
      if (window.scrollY > 500) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener("scroll", toggleVisibility);
    return () => window.removeEventListener("scroll", toggleVisibility);
  }, [shouldExclude]);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.button
          className="fixed bottom-[70px] md:bottom-[60px] lg:bottom-8 right-6 z-50 p-3 rounded-full bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-[var(--brand-gold)] focus:ring-offset-2 dark:focus:ring-offset-black cursor-pointer"
          onClick={scrollToTop}
          initial={{ opacity: 0, scale: 0.5, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.5, y: 20 }}
          transition={{ duration: 0.3 }}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          {/* Button glow effect */}
          <div className="absolute inset-0 rounded-full bg-[var(--brand-gold)]/50 blur-md -z-10"></div>

          {/* Animated arrow */}
          <motion.div
            animate={{ y: [0, -3, 0] }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              repeatType: "loop",
              ease: "easeInOut"
            }}
          >
            <ChevronUp className="h-6 w-6" />
          </motion.div>
        </motion.button>
      )}
    </AnimatePresence>
  );
}
