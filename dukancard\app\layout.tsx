import { Inter } from "next/font/google"; // Import Inter font
import "./globals.css";
import { ThemeProvider } from "next-themes"; // Import ThemeProvider
import { Toaster } from "@/components/ui/sonner";
import GoogleAnalytics from "@/app/components/GoogleAnalytics";
import MetaPixel from "@/app/components/MetaPixel";

// Configure Inter font
const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans", // Assign CSS variable
});

export async function generateMetadata() {
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const siteTitle = "Dukancard - Your Digital Business Card Solution";
  const siteDescription =
    "Create a digital business card with Dukancard to showcase your shop, services, or portfolio. Boost your online presence, connect with customers, and grow your business across India.";

  const ogImage = `${siteUrl}/opengraph-image.png`;

  return {
    metadataBase: new URL(siteUrl),
    title: {
      default: siteTitle,
      template: `%s - Dukancard`,
    },
    description: siteDescription,
    keywords:
      "digital business card, online presence, small business India, shop digital card, freelancer portfolio, Tier 2 cities, Tier 3 cities, Dukancard, local business growth, QR code business card",
    robots: "index, follow",
    alternates: {
      canonical: siteUrl,
    },
    openGraph: {
      title: siteTitle,
      description: siteDescription,
      url: siteUrl,
      siteName: "Dukancard",
      type: "website",
      locale: "en_IN",
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: "Dukancard - Digital Business Card Platform",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: siteTitle,
      description: siteDescription,
      image: ogImage,
    },
  };
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    // Apply font variable to html tag
    <html lang="en" className={inter.variable} suppressHydrationWarning>
      <head>
        <GoogleAnalytics />
        <MetaPixel />
      </head>
      {/* Apply flex layout to body for sticky footer */}
      <body className="antialiased flex flex-col min-h-screen bg-white dark:bg-black text-black dark:text-white transition-colors duration-300">
        <ThemeProvider
          attribute="class"
          defaultTheme="system" // Or "dark" / "light" if preferred
          enableSystem
          disableTransitionOnChange
        >
          {/* Header/Footer could be added back here if needed globally */}
          {children}
          <Toaster />
          {/* Removed GoogleCMP - only custom ads now */}
        </ThemeProvider>
      </body>
    </html>
  );
}
