'use client';

import { Button } from '@/components/ui/button';
import { MessageCircle, Phone } from 'lucide-react';
import { motion } from 'framer-motion';

interface PostActionsProps {
  business: {
    business_name: string | null;
    whatsapp_number: string | null;
    phone: string | null;
  };
  hasWhatsApp: boolean;
  hasPhone: boolean;
}

export default function PostActions({ business, hasWhatsApp, hasPhone }: PostActionsProps) {
  // Handle contact actions
  const handleWhatsAppClick = () => {
    if (hasWhatsApp) {
      const whatsappNumber = business.whatsapp_number?.replace(/\D/g, ''); // Remove non-digits
      const message = `Hi ${business.business_name}, I saw your post and would like to know more.`;
      window.open(`https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`, '_blank');
    }
  };

  const handlePhoneClick = () => {
    if (hasPhone) {
      window.open(`tel:${business.phone}`, '_self');
    }
  };

  const buttonVariants = {
    hover: { scale: 1.02, transition: { duration: 0.2 } },
    tap: { scale: 0.98, transition: { duration: 0.1 } }
  };

  // Only show buttons if they have valid contact info - match React Native behavior
  const showWhatsApp = hasWhatsApp;
  const showPhone = hasPhone;

  // If neither button should be shown, don't render anything
  if (!showWhatsApp && !showPhone) {
    return null;
  }

  return (
    <div className="flex gap-3">
      {showWhatsApp && (
        <motion.div
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
          className="flex-1"
        >
          <Button
            variant="outline"
            size="sm"
            onClick={handleWhatsAppClick}
            className="w-full h-10 bg-white dark:bg-black border-green-200 dark:border-green-700 text-green-700 dark:text-green-300 hover:bg-green-500 hover:text-white hover:border-green-500 transition-all duration-300 cursor-pointer"
          >
            <MessageCircle className="h-4 w-4 mr-2" />
            WhatsApp
          </Button>
        </motion.div>
      )}

      {showPhone && (
        <motion.div
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
          className="flex-1"
        >
          <Button
            variant="outline"
            size="sm"
            onClick={handlePhoneClick}
            className="w-full h-10 bg-white dark:bg-black border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-300 hover:bg-blue-500 hover:text-white hover:border-blue-500 transition-all duration-300 cursor-pointer"
          >
            <Phone className="h-4 w-4 mr-2" />
            Call Now
          </Button>
        </motion.div>
      )}
    </div>
  );
}
