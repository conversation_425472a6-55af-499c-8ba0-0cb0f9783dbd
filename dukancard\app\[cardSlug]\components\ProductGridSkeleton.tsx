"use client";

import { Skeleton } from "@/components/ui/skeleton";

export default function ProductGridSkeleton() {
  return (
    <div className="space-y-6">
      {/* Product Grid Skeleton */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 sm:gap-3 md:gap-4">
        {Array.from({ length: 8 }).map((_, index) => (
          <div
            key={index}
            className="border border-neutral-200/80 dark:border-neutral-800/80 rounded-xl overflow-hidden bg-transparent transition-all duration-300"
          >
            <Skeleton className="h-48 w-full" />
            <div className="p-4 space-y-3">
              <Skeleton className="h-5 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
              <div className="flex justify-between items-center pt-2">
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-8 w-8 rounded-full" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
