"use client";

import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import { AdData } from "@/types/ad";
import { cardVariants } from "./animations";

interface EnhancedAdSectionProps {
  topAdData: AdData | null;
  businessCustomAd?: {
    enabled?: boolean;
    image_url?: string;
    link_url?: string;
  } | null;
  userPlan?: "free" | "basic" | "growth" | "pro" | "enterprise" | "trial";
}

// Simple URL validation utility
const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export default function EnhancedAdSection({ topAdData, businessCustomAd, userPlan }: EnhancedAdSectionProps) {
  const adRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(adRef, { once: false, amount: 0.2 });

  // Check if business owner has Pro/Enterprise access for custom ads
  const hasProEnterpriseAccess = userPlan === "pro" || userPlan === "enterprise";

  // Validate custom ad data - must have Pro/Enterprise plan, enabled=true, and valid image URL
  const isCustomAdValid = businessCustomAd &&
    typeof businessCustomAd === 'object' &&
    businessCustomAd.enabled === true &&
    businessCustomAd.image_url &&
    typeof businessCustomAd.image_url === 'string' &&
    businessCustomAd.image_url.trim() !== "" &&
    isValidUrl(businessCustomAd.image_url);

  // Validate custom ad link URL if provided
  const hasValidLinkUrl = businessCustomAd?.link_url &&
    typeof businessCustomAd.link_url === 'string' &&
    businessCustomAd.link_url.trim() !== "" &&
    isValidUrl(businessCustomAd.link_url);

  // Determine which ad to show - business custom ad takes priority (only for Pro/Enterprise with valid data and enabled=true)
  const shouldShowBusinessAd = hasProEnterpriseAccess && isCustomAdValid;
  const shouldShowTopAd = !shouldShowBusinessAd && topAdData && topAdData.imageUrl;



  return (
    <motion.div
      ref={adRef}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={cardVariants}
      className="transition-all duration-300"
    >
      {/* Ad Slot with enhanced animations */}
      <div className="flex-shrink-0 w-full overflow-hidden">
        {shouldShowBusinessAd ? (
          /* Business Custom Ad */
          hasValidLinkUrl ? (
            <Link
              href={businessCustomAd!.link_url!}
              target="_blank"
              rel="noopener noreferrer"
              className="block w-full rounded-lg overflow-hidden group cursor-pointer"
            >
            <div className="relative w-full overflow-hidden rounded-lg">
              <motion.div
                initial={{ scale: 1 }}
                whileHover={{ scale: 1.03 }}
                transition={{ duration: 0.3 }}
              >
                <Image
                  src={businessCustomAd!.image_url!}
                  alt="Business Advertisement"
                  width={1200}
                  height={675} // 16:9 aspect ratio
                  className="w-full h-auto rounded-lg object-cover max-w-full transition-all duration-300"
                  unoptimized
                />
              </motion.div>

              {/* Subtle overlay on hover */}
              <motion.div
                className="absolute inset-0 bg-black/0 rounded-lg"
                initial={{ opacity: 0 }}
                whileHover={{
                  opacity: 1,
                  background: "linear-gradient(to bottom, rgba(0,0,0,0) 80%, rgba(0,0,0,0.1) 100%)"
                }}
                transition={{ duration: 0.3 }}
              />

              {/* Business ad indicator */}
              <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                Sponsored
              </div>
            </div>
            </Link>
          ) : (
            /* Non-clickable Business Custom Ad (no link URL) */
            <div className="block w-full rounded-lg overflow-hidden">
              <div className="relative w-full overflow-hidden rounded-lg">
                <motion.div
                  initial={{ scale: 1 }}
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.3 }}
                >
                  <Image
                    src={businessCustomAd!.image_url!}
                    alt="Business Advertisement"
                    width={1200}
                    height={675} // 16:9 aspect ratio
                    className="w-full h-auto rounded-lg object-cover max-w-full transition-all duration-300"
                    unoptimized
                  />
                </motion.div>

                {/* Business ad indicator */}
                <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                  Sponsored
                </div>
              </div>
            </div>
          )
        ) : shouldShowTopAd ? (
          /* Platform/Global Ad */
          <Link
            href={topAdData!.linkUrl || "#"}
            target="_blank"
            rel="noopener noreferrer"
            className="block w-full rounded-lg overflow-hidden group"
          >
            <div className="relative w-full overflow-hidden rounded-lg">
              <motion.div
                initial={{ scale: 1 }}
                whileHover={{ scale: 1.03 }}
                transition={{ duration: 0.3 }}
              >
                <Image
                  src={topAdData!.imageUrl}
                  alt="Advertisement"
                  width={1200}
                  height={675} // 16:9 aspect ratio
                  className="w-full h-auto rounded-lg object-contain max-w-full transition-all duration-300"
                  unoptimized
                />
              </motion.div>

              {/* Subtle overlay on hover */}
              <motion.div
                className="absolute inset-0 bg-black/0 rounded-lg"
                initial={{ opacity: 0 }}
                whileHover={{
                  opacity: 1,
                  background: "linear-gradient(to bottom, rgba(0,0,0,0) 80%, rgba(0,0,0,0.1) 100%)"
                }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </Link>
        ) : (
          <div className="w-full h-32 md:h-48 lg:h-64 bg-gradient-to-r from-neutral-100 to-neutral-200 dark:from-neutral-800 dark:to-neutral-700 rounded-lg flex items-center justify-center">
            <p className="text-neutral-500 dark:text-neutral-400 text-sm">
              Advertisement space
            </p>
          </div>
        )}
      </div>
    </motion.div>
  );
}
