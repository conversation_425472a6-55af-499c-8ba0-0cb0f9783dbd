import { NearbyProduct } from "@/app/(main)/discover/actions/types";
import { BusinessProfilePublicData } from "@/lib/actions/businessProfiles/types";

export interface CategoryLocation {
  state?: string;
  city?: string;
  pincode?: string;
  locality?: string;
}

export interface CategorySearchResult {
  products?: NearbyProduct[];
  businesses?: BusinessProfilePublicData[];
  isAuthenticated: boolean;
  totalCount: number;
  hasMore: boolean;
  nextPage: number | null;
  location?: CategoryLocation;
}
