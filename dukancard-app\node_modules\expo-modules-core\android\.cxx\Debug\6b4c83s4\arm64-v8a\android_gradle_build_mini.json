{"buildFiles": ["C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\src\\fabric\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"fabric::@3c04bbf757b97f4dae7c": {"artifactName": "fabric", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\6b4c83s4\\arm64-v8a\\src\\fabric\\libfabric.a", "runtimeFiles": []}, "expo-modules-core::@6890427a1f51a3e7e1df": {"artifactName": "expo-modules-core", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Desktop\\Dukancard Web App\\dukancard-app\\node_modules\\expo-modules-core\\android\\build\\intermediates\\cxx\\Debug\\6b4c83s4\\obj\\arm64-v8a\\libexpo-modules-core.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8050d15875717ad3c035882deb89d68f\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6e9cabc4b841ecb8bc48e2ce54799a1d\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6e9cabc4b841ecb8bc48e2ce54799a1d\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8050d15875717ad3c035882deb89d68f\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6e9cabc4b841ecb8bc48e2ce54799a1d\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6e9cabc4b841ecb8bc48e2ce54799a1d\\transformed\\react-android-0.79.3-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}}}