# Customer Profiles Table Documentation

## Table Overview

The `customer_profiles` table in the Dukancard application stores information about customers who use the platform to discover and interact with businesses. Each customer profile is linked to a user account and contains basic profile information such as name, email, and avatar.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | | Primary key, references users.id |
| name | text | YES | | Customer's full name |
| email | text | YES | | Customer's email address (unique) |
| phone | text | YES | | Customer's mobile number (unique, 10 digits) |
| created_at | timestamptz | NO | now() | Timestamp when the record was created |
| updated_at | timestamptz | NO | now() | Timestamp when the record was last updated |
| avatar_url | text | YES | | URL to the customer's avatar/profile image |
| address | text | YES | | Street address of the customer |
| pincode | text | YES | | Postal/ZIP code |
| state | text | YES | | State where the customer is located |
| city | text | YES | | City where the customer is located |
| locality | text | YES | | Specific locality or area within the city |
| city_slug | text | YES | | Slug version of the city name |
| state_slug | text | YES | | Slug version of the state name |
| locality_slug | text | YES | | Slug version of the locality name |

## Constraints

### Primary Key
- `customer_profiles_pkey` - Primary key constraint on the `id` column

### Foreign Keys
- `customer_profiles_id_fkey` - Foreign key constraint linking `id` to `users.id`

### Unique Constraints
None - All unique constraints have been removed to allow data sharing across profiles

### Check Constraints
Multiple check constraints ensure that required fields are not null.

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| customer_profiles_pkey | UNIQUE | id | Primary key index |

## Triggers

### update_customer_profile_slugs_trigger
- **Event**: UPDATE
- **Function**: update_customer_profile_slugs()
- **Description**: Updates the slug fields (city_slug, state_slug, locality_slug) when address-related fields change

### sync_auth_to_customer_profiles_trigger
- **Event**: UPDATE on auth.users table
- **Function**: sync_auth_to_customer_profiles()
- **Description**: Automatically synchronizes changes from auth.users table (email, phone, full_name) to customer_profiles table

### Trigger Function Definitions

#### Address Slug Update Function
```sql
CREATE OR REPLACE FUNCTION public.update_customer_profile_slugs()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
BEGIN
    -- If pincode or locality changed, update the slug columns
    IF (NEW.pincode IS DISTINCT FROM OLD.pincode) OR
       (NEW.locality IS DISTINCT FROM OLD.locality) OR
       (NEW.city IS DISTINCT FROM OLD.city) OR
       (NEW.state IS DISTINCT FROM OLD.state) THEN

        -- Try to find matching pincode and locality
        SELECT p.city_slug, p.state_slug, p.locality_slug
        INTO NEW.city_slug, NEW.state_slug, NEW.locality_slug
        FROM pincodes p
        WHERE p."Pincode" = NEW.pincode
          AND LOWER(p."OfficeName") = LOWER(NEW.locality)
        LIMIT 1;

        -- If no match found, try to match by city
        IF NEW.city_slug IS NULL THEN
            SELECT p.city_slug, p.state_slug
            INTO NEW.city_slug, NEW.state_slug
            FROM pincodes p
            WHERE p."Pincode" = NEW.pincode
              AND LOWER(p."DivisionName") = LOWER(NEW.city)
            LIMIT 1;
        END IF;

        -- If still no match for state, try to match just by state
        IF NEW.state_slug IS NULL THEN
            SELECT p.state_slug
            INTO NEW.state_slug
            FROM pincodes p
            WHERE LOWER(p."StateName") = LOWER(NEW.state)
            LIMIT 1;
        END IF;
    END IF;

    RETURN NEW;
END;
$function$
```

#### Auth Users Synchronization Function
```sql
CREATE OR REPLACE FUNCTION public.sync_auth_to_customer_profiles()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
    -- Only proceed if this is an UPDATE operation
    IF TG_OP = 'UPDATE' THEN
        -- Check if email, phone, or user_metadata.full_name changed
        IF (NEW.email IS DISTINCT FROM OLD.email) OR
           (NEW.phone IS DISTINCT FROM OLD.phone) OR
           (NEW.raw_user_meta_data->>'full_name' IS DISTINCT FROM OLD.raw_user_meta_data->>'full_name') THEN

            -- Update customer_profiles table if a record exists for this user
            UPDATE public.customer_profiles
            SET
                email = NEW.email,
                phone = CASE
                    WHEN NEW.phone IS NOT NULL AND NEW.phone LIKE '91%' THEN SUBSTRING(NEW.phone FROM 3)
                    WHEN NEW.phone IS NOT NULL THEN NEW.phone
                    ELSE NULL
                END,
                name = COALESCE(NEW.raw_user_meta_data->>'full_name', name),
                updated_at = NOW()
            WHERE id = NEW.id;

            -- Log the sync operation (optional)
            RAISE LOG 'Synced auth.users changes to customer_profiles for user %', NEW.id;
        END IF;
    END IF;

    RETURN NEW;
END;
$function$
```

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| Allow individual read access | SELECT | (auth.uid() = id) | |
| Allow individual insert access | INSERT | | |
| Allow individual update access | UPDATE | (auth.uid() = id) | |
| Allow individual delete access | DELETE | (auth.uid() = id) | |

These policies ensure that users can only access and modify their own customer profile data.

## Related Tables

### users
The `users` table is referenced by the `id` foreign key and contains authentication information for the customer.

### Potential Related Tables
While there are no direct foreign key relationships from other tables to `customer_profiles`, the following tables may have logical relationships through the user ID:

- `likes` - Records of businesses liked by customers
- `ratings_reviews` - Customer ratings and reviews for businesses
- `subscriptions` - Customer subscriptions to businesses

## Usage Notes

1. **User Authentication**:
   - The `customer_profiles` table is linked to the `users` table via the `id` column, which serves as both the primary key and a foreign key.
   - This design ensures that each customer profile is associated with exactly one authenticated user account.

2. **Profile Information**:
   - Basic profile information such as name, email, and avatar URL is stored in this table.
   - Email addresses can be shared across multiple customer profiles (no uniqueness constraint).

3. **Security**:
   - Row Level Security (RLS) policies ensure that users can only access and modify their own profile data.
   - The INSERT policy allows new profile creation without restrictions, but subsequent operations are limited to the profile owner.

4. **Profile Creation Flow**:
   - Typically, a customer profile is created after a user registers and authenticates.
   - The application should ensure that the `id` value matches the authenticated user's ID from the `users` table.

5. **Avatar Management**:
   - The `avatar_url` field stores a reference to the customer's profile image.
   - The actual image file is likely stored in Supabase Storage or another storage service.
   - When a customer profile is deleted, associated storage files should be cleaned up as well.

6. **Customer vs. Business Users**:
   - The Dukancard application appears to have two types of users: customers and businesses.
   - Customers are represented in this `customer_profiles` table.
   - Businesses are represented in the separate `business_profiles` table.
   - A user might have either a customer profile, or a business profile, not both.

7. **Data Relationships**:
   - Customers can interact with businesses through likes, subscriptions, and ratings.
   - These interactions are tracked in separate tables that reference the user ID.
   - While there are no direct foreign key relationships from other tables to `customer_profiles`, logical relationships exist through the common user ID.

8. **Profile Updates**:
   - The `updated_at` timestamp is automatically updated whenever the profile is modified.
   - This can be useful for tracking when a customer last updated their profile information.

9. **Address Information**:
   - The table includes address fields (address, pincode, state, city, locality) for customer location data.
   - Address validation is enforced in the application to ensure customers provide complete address information.
   - The `update_customer_profile_slugs` trigger automatically updates slug fields when address information changes.

10. **Location Data and Slugs**:
    - Location data includes city, state, pincode, and locality, with corresponding slug fields for URL-friendly versions.
    - The slug fields are automatically populated from the `pincodes` table when address information is updated.
    - These slugs can be used for location-based filtering and SEO-friendly URLs.

11. **Phone Number Management**:
    - The `phone` field stores the customer's mobile number (10 digits, Indian format).
    - Phone numbers can be shared across multiple customer and business profiles (no uniqueness constraint).
    - Phone numbers are stored without country code in profile tables (e.g., `8458060663`), but with country code in `auth.users` table (e.g., `************`).
    - Phone number validation follows Indian mobile number format (10 digits starting with 6-9).

12. **Automatic Data Synchronization**:
    - The `sync_auth_to_customer_profiles_trigger` automatically synchronizes changes from `auth.users` table to `customer_profiles` table.
    - When email, phone, or full_name is updated in `auth.users`, the corresponding fields in `customer_profiles` are automatically updated.
    - This ensures data consistency between authentication and profile tables without manual synchronization in application code.
    - The trigger handles phone number format conversion (removes `91` country code prefix when syncing to customer_profiles).
    - Application code should only update `auth.users` table for email, phone, and name changes - the profile table will be updated automatically.

13. **Data Source of Truth**:
    - `auth.users` table is the primary source of truth for email, phone, and full_name.
    - `customer_profiles` table serves as a denormalized copy for easier querying and application logic.
    - All updates to email, phone, and name should be made through `auth.users` table using Supabase Auth methods.
    - Direct updates to email/phone/name in `customer_profiles` table should be avoided as they will be overwritten by the trigger.
