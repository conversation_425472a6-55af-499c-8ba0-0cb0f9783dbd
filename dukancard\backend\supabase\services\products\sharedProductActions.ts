'use server';

import { createClient } from '@/utils/supabase/server';
import { createAdminClient } from '@/utils/supabase/admin';
import { ProductData } from '@/lib/types/posts';

// Search business products for the current user
export async function searchBusinessProducts(query: string): Promise<{
  success: boolean;
  data?: ProductData[];
  error?: string;
}> {
  try {
    // Validate input
    if (!query || query.trim().length < 2) {
      return {
        success: false,
        error: 'Search query must be at least 2 characters long'
      };
    }

    // Get authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: 'Authentication required'
      };
    }

    // Use admin client to bypass RLS policies
    const supabaseAdmin = createAdminClient();



    // Search products for the current user's business only
    const { data, error } = await supabaseAdmin
      .from('products_services')
      .select('id, name, base_price, discounted_price, image_url, slug')
      .eq('business_id', user.id) // Ensure user can only see their own products
      .eq('is_available', true)
      .ilike('name', `%${query.trim()}%`)
      .order('name', { ascending: true })
      .limit(10); // Limit search results to 10 items

    if (error) {
      console.error('Error searching products:', error);
      return {
        success: false,
        error: 'Failed to search products'
      };
    }



    return {
      success: true,
      data: data || []
    };
  } catch (error) {
    console.error('Error in searchBusinessProducts:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

// Get selected products by IDs for the current user
export async function getSelectedProducts(productIds: string[]): Promise<{
  success: boolean;
  data?: ProductData[];
  error?: string;
}> {
  try {
    // Validate input
    if (!productIds || productIds.length === 0) {
      return {
        success: true,
        data: []
      };
    }

    // Get authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: 'Authentication required'
      };
    }

    // Use admin client to bypass RLS policies
    const supabaseAdmin = createAdminClient();

    // Get products by IDs, but only for the current user's business
    const { data, error } = await supabaseAdmin
      .from('products_services')
      .select('id, name, base_price, discounted_price, image_url, slug')
      .in('id', productIds)
      .eq('business_id', user.id) // Ensure user can only access their own products
      .order('name', { ascending: true });

    if (error) {
      console.error('Error getting selected products:', error);
      return {
        success: false,
        error: 'Failed to get selected products'
      };
    }

    return {
      success: true,
      data: data || []
    };
  } catch (error) {
    console.error('Error in getSelectedProducts:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}
