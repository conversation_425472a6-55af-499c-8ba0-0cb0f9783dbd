import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { getScheduledChanges } from "@/lib/razorpay/services/subscription";

/**
 * GET /api/subscriptions/:id/pending-update
 *
 * Fetches details of pending updates for a subscription in Razorpay
 *
 * Example response:
 * ```json
 * {
 *   "success": true,
 *   "data": {
 *     "id": "sub_00000000000001",
 *     "entity": "subscription",
 *     "plan_id": "plan_00000000000003",
 *     "customer_id": "cust_00000000000001",
 *     "status": "active",
 *     "current_start": 1580284732,
 *     "current_end": 1580841000,
 *     "ended_at": null,
 *     "quantity": 25,
 *     "notes": {
 *       "notes_key_1": "Tea, <PERSON>, Hot",
 *       "notes_key_2": "Tea, <PERSON>… decaf."
 *     },
 *     "charge_at": 1580841000,
 *     "start_at": 1580284732,
 *     "end_at": 1611081000,
 *     "auth_attempts": 0,
 *     "total_count": 6,
 *     "paid_count": 1,
 *     "customer_notify": true,
 *     "created_at": 1580284702,
 *     "expire_by": 1580626111,
 *     "short_url": "https://rzp.io/i/fFWTkbf",
 *     "has_scheduled_changes": true,
 *     "change_scheduled_at": 1557253800,
 *     "source": "api",
 *     "offer_id": "offer_JHD834hjbxzhd38d",
 *     "remaining_count": 5
 *   }
 * }
 * ```
 */
export async function GET(
  _: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the subscription ID from the URL params
    const { id: subscriptionId } = await params;

    // Verify authentication using Supabase
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if the user has access to this subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("*")
      .eq("razorpay_subscription_id", subscriptionId)
      .single();

    if (subscriptionError) {
      // If no subscription is found with this ID, return a 404
      if (subscriptionError.code === "PGRST116") {
        return NextResponse.json(
          { success: false, error: "Subscription not found" },
          { status: 404 }
        );
      }

      console.error("[RAZORPAY_ERROR] Error fetching subscription:", subscriptionError);
      return NextResponse.json(
        { success: false, error: "Error fetching subscription" },
        { status: 500 }
      );
    }

    // Check if the user is authorized to access this subscription
    const isOwner = subscription.business_profile_id === user.id;

    if (!isOwner) {
      return NextResponse.json(
        { success: false, error: "Unauthorized to access this subscription" },
        { status: 403 }
      );
    }

    // Fetch scheduled changes from Razorpay
    const result = await getScheduledChanges(subscriptionId);

    if (!result.success) {
      console.error("[RAZORPAY_ERROR] Error fetching scheduled changes:", result.error);
      return NextResponse.json(
        { success: false, error: "Failed to fetch scheduled changes" },
        { status: 400 }
      );
    }

    // Return the subscription with scheduled changes
    return NextResponse.json(
      { success: true, data: result.data },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error fetching scheduled changes:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 }
    );
  }
}
