"use client";

import { But<PERSON> } from "@/components/ui/button";
import WhatsAppIcon from "@/app/components/icons/WhatsAppIcon";
import { useState } from "react";

interface WhatsAppButtonProps {
  whatsappNumber: string;
  productName: string;
  businessName: string;
  productUrl: string;
}

export default function WhatsAppButton({
  whatsappNumber,
  productName,
  businessName,
  productUrl,
}: WhatsAppButtonProps) {
  const [isHovered, setIsHovered] = useState(false);

  // Format WhatsApp number (remove any non-digit characters)
  const formattedNumber = whatsappNumber.replace(/\D/g, "");

  // Create default message with product URL
  const defaultMessage = encodeURIComponent(
    `Hi ${businessName}, I'm interested in your product "${productName}" that I saw on your Dukancard (${productUrl}). Can you provide more information?`
  );

  // Create WhatsApp URL
  const whatsappUrl = `https://wa.me/${formattedNumber}?text=${defaultMessage}`;

  return (
    <div className="relative group">
      {/* Button glow effect */}
      <div className="absolute -inset-0.5 bg-gradient-to-r from-green-500/30 to-green-600/30 rounded-xl blur-md opacity-75 group-hover:opacity-100 transition-opacity duration-300"></div>

      <Button
        className="relative w-full bg-gradient-to-br from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium flex items-center justify-center gap-3 py-6 rounded-xl border border-green-500/20 transition-all duration-300"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={() => window.open(whatsappUrl, "_blank")}
      >
        {/* Shimmer effect */}
        <div className="absolute inset-0 w-full h-full overflow-hidden rounded-xl">
          <div className="absolute inset-0 w-[200%] h-full bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer"></div>
        </div>

        <WhatsAppIcon className={`w-5 h-5 ${isHovered ? 'animate-spin-slow' : ''}`} />
        <span className="text-base font-semibold tracking-wide">WhatsApp</span>
      </Button>
    </div>
  );
}

// Fallback button for when WhatsApp is not available
export function WhatsAppButtonDisabled() {
  return (
    <div className="relative group">
      {/* Button glow effect (dimmed) */}
      <div className="absolute -inset-0.5 bg-gradient-to-r from-neutral-400/20 to-neutral-600/20 rounded-xl blur-md opacity-50 transition-opacity duration-300"></div>

      <Button
        className="relative w-full bg-gradient-to-br from-neutral-500 to-neutral-600 text-white font-medium flex items-center justify-center gap-3 py-6 cursor-not-allowed opacity-80 rounded-xl border border-neutral-500/10 transition-all duration-300"
        disabled
      >
        <WhatsAppIcon className="w-5 h-5" />
        <span className="text-base font-semibold tracking-wide">WhatsApp Unavailable</span>
      </Button>
    </div>
  );
}
