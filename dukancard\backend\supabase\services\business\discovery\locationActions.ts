"use server";

import { createClient } from "../../../utils/supabase/server";
import { createAdminClient } from "../../../utils/supabase/admin";
import { pincodeSchema, citySchema } from "../../../lib/schemas/locationSchemas";
import { getPincodeDetails } from "../../../location/location";
import { BusinessSortBy, getSecureBusinessProfiles } from "../../profiles/profileRetrieval";
import { DiscoverSearchResult, getSortingColumn, getSortingDirection } from "./types";

// Action to find businesses or products based on location (pincode) and view type
export async function searchDiscoverData(params: {
  pincode?: string;
  city?: string;
  locality?: string | null;
  viewType: "cards" | "products";
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy;
  productType?: "physical" | "service" | null;
}): Promise<{
  data?: DiscoverSearchResult;
  error?: string;
}> {
  const {
    pincode,
    city,
    locality,
    viewType,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
    productType = null,
  } = params;

  // Check if we have either pincode or city
  if (!pincode && !city) {
    return { error: "Either pincode or city is required." };
  }

  // Initialize Supabase clients early
  const supabase = await createClient();
  const supabaseAdmin = createAdminClient();

  let locationCity: string;
  let locationState: string;
  let validPincodes: string[] = [];

  // Handle pincode-based search
  if (pincode) {
    // 1. Validate Pincode
    const validatedPincode = pincodeSchema.safeParse({ pincode });
    if (!validatedPincode.success) {
      return { error: "Invalid Pincode format. Must be 6 digits." };
    }
    const validPincode = validatedPincode.data.pincode;

    // 2. Get Location Details
    const locationDetails = await getPincodeDetails(validPincode);
    if (
      locationDetails.error ||
      !locationDetails.city ||
      !locationDetails.state
    ) {
      return { error: locationDetails.error || "Pincode not found." };
    }

    locationCity = locationDetails.city;
    locationState = locationDetails.state;
    validPincodes = [validPincode];
  }
  // Handle city-based search
  else if (city) {
    // 1. Validate City
    const validatedCity = citySchema.safeParse({ city });
    if (!validatedCity.success) {
      return { error: "Invalid city name. Must be at least 2 characters." };
    }
    const validCity = validatedCity.data.city;

    // For city-based search, we'll directly filter by the city column
    // No need to get pincodes or other location details
    locationCity = validCity;

    // Set empty pincodes array to indicate we're doing a direct city search
    validPincodes = [];

    // Try to get the state for display purposes only
    try {
      const { data } = await supabaseAdmin
        .from("pincodes")
        .select("StateName")
        .ilike("DivisionName", `%${validCity}%`)
        .limit(1);

      if (data && data.length > 0) {
        locationState = data[0].StateName;
      } else {
        locationState = ""; // Default empty state if not found
      }
    } catch (error) {
      console.error("Error getting state for city:", error);
      locationState = ""; // Default empty state on error
    }
  } else {
    return { error: "Either pincode or city is required." };
  }

  // 3. Check Authentication
  const {
    data: { user },
  } = await supabase.auth.getUser();
  const isAuthenticated = !!user;

  try {
    // Add a small delay to prevent infinite loops
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 4. Build Base Query for Valid Businesses
    // Reference code removed

    // 5. Fetch Data Based on viewType
    if (viewType === "cards") {
      // Check if we're searching by city directly
      if (city && validPincodes.length === 0) {
        // Direct city-based search using Supabase
        const nowISO = new Date().toISOString();
        const offset = (page - 1) * limit;

        // Define fields to select
        const businessFields = `
          id, business_name, logo_url, member_name, title,
          address_line, city, state, pincode, locality, phone, business_category, instagram_url,
          facebook_url, whatsapp_number, about_bio, status, business_slug, theme_color,
          delivery_info, total_likes, total_subscriptions, average_rating, business_hours,
          trial_end_date, created_at, updated_at, contact_email
        `;

        // Get count of businesses matching the city
        const { count, error: countError } = await supabaseAdmin
          .from("business_profiles")
          .select("id", { count: "exact" })
          .eq("city", city) // Use exact matching for city
          .eq("status", "online")
          .or(`has_active_subscription.eq.true,trial_end_date.gt.${nowISO}`);

        if (countError) {
          console.error("City Business Count Error:", countError);
          return { error: "Database error counting businesses by city." };
        }

        // Fetch businesses matching the city
        const { data, error } = await supabaseAdmin
          .from("business_profiles")
          .select(businessFields)
          .eq("city", city) // Use exact matching for city
          .eq("status", "online")
          .or(`has_active_subscription.eq.true,trial_end_date.gt.${nowISO}`)
          .range(offset, offset + limit - 1)
          .order(getSortingColumn(sortBy), { ascending: getSortingDirection(sortBy) });

        if (error) {
          console.error("City Business Query Error:", error);
          return { error: "Database error fetching businesses by city." };
        }

        // Get subscription data for all profiles
        const profileIds = data.map(profile => profile.id);

        // Fetch subscription data for all profiles
        const { data: subscriptionsData, error: subscriptionsError } = await supabaseAdmin
          .from("payment_subscriptions")
          .select("business_profile_id, subscription_status, plan_id")
          .in("business_profile_id", profileIds)
          .order("created_at", { ascending: false });

        if (subscriptionsError) {
          console.error("Subscriptions Error:", subscriptionsError);
        }

        // Create a map of subscription data by profile ID
        const subscriptionMap: Record<string, { subscription_status: string | null, plan_id: string | null }> = {};
        if (subscriptionsData) {
          subscriptionsData.forEach(sub => {
            if (sub.business_profile_id && !subscriptionMap[sub.business_profile_id]) {
              subscriptionMap[sub.business_profile_id] = {
                subscription_status: sub.subscription_status,
                plan_id: sub.plan_id
              };
            }
          });
        }

        // Map the data to the expected format
        const businessesData = data.map(profile => {
          const subData = subscriptionMap[profile.id] || { subscription_status: null, plan_id: null };
          return {
            ...profile,
            subscription_status: subData.subscription_status,
            plan_id: subData.plan_id
          };
        });

        return {
          data: {
            location: { city: locationCity, state: locationState },
            businesses: businessesData.map(data => ({
              id: data.id,
              business_name: data.business_name ?? "",
              contact_email: "", // contact_email is not in BusinessProfilePublicData
              has_active_subscription: data.subscription_status === "active",
              trial_end_date: data.trial_end_date ?? null,
              created_at: data.created_at ?? undefined,
              updated_at: data.updated_at ?? undefined,
              logo_url: data.logo_url ?? "",
              member_name: data.member_name ?? "",
              title: data.title ?? "",
              address_line: data.address_line ?? "",
              city: data.city ?? "",
              state: data.state ?? "",
              pincode: data.pincode ?? "",
              locality: data.locality ?? "",
              phone: data.phone ?? "",
              business_category: data.business_category ?? "",
              instagram_url: data.instagram_url ?? "",
              facebook_url: data.facebook_url ?? "",
              whatsapp_number: data.whatsapp_number ?? "",
              about_bio: data.about_bio ?? "",
              status:
                data.status === "online"
                  ? "online"
                  : ("offline" as "online" | "offline"),
              business_slug: data.business_slug ?? "",
              total_likes: data.total_likes ?? 0,
              total_subscriptions: data.total_subscriptions ?? 0,
              average_rating: data.average_rating ?? 0,
              theme_color: data.theme_color ?? "#D4AF37",
              delivery_info: data.delivery_info ?? "",
              business_hours: data.business_hours,
              google_maps_url: "", // Not available in this query
              established_year: null, // Not available in this query
              website_url: "",
              linkedin_url: "",
              twitter_url: "",
              youtube_url: "",
              call_number: "",
            })),
            isAuthenticated: isAuthenticated,
            totalCount: count || 0,
            hasMore: (count || 0) > (page * limit),
            nextPage: (count || 0) > (page * limit) ? page + 1 : null,
          },
        };
      }

      // Use the secure method to fetch business profiles by pincode
      const {
        data: businessesData,
        count,
        error: businessesError,
      } = await getSecureBusinessProfiles(
        null, // no business name search
        validPincodes[0] || null, // use first pincode
        locality,
        page,
        limit,
        sortBy
      );

      if (businessesError) {
        console.error("Search Discover (Cards) Error:", businessesError);
        return { error: businessesError };
      }

      const totalCount = count || 0;

      // Calculate if there are more pages
      const hasMore = totalCount > page * limit;
      const nextPage = hasMore ? page + 1 : null;

      // Map raw data to BusinessCardData, handling potential nulls
      const businesses =
        businessesData?.map((data) => {
          // Use the actual data from the database
          return {
            id: data.id,
            business_name: data.business_name ?? "",
            contact_email: "", // contact_email is not in BusinessProfilePublicData
            has_active_subscription: data.subscription_status === "active",
            trial_end_date: data.trial_end_date ?? null,
            created_at: data.created_at ?? undefined,
            updated_at: data.updated_at ?? undefined,
            logo_url: data.logo_url ?? "",
            member_name: data.member_name ?? "",
            title: data.title ?? "",
            address_line: data.address_line ?? "",
            city: data.city ?? "",
            state: data.state ?? "",
            pincode: data.pincode ?? "",
            locality: data.locality ?? "",
            phone: data.phone ?? "",
            business_category: data.business_category ?? "",
            instagram_url: data.instagram_url ?? "",
            facebook_url: data.facebook_url ?? "",
            whatsapp_number: data.whatsapp_number ?? "",
            about_bio: data.about_bio ?? "",
            status:
              data.status === "online"
                ? "online"
                : ("offline" as "online" | "offline"),
            business_slug: data.business_slug ?? "",

            // Include metrics data
            total_likes: data.total_likes ?? 0,
            total_subscriptions: data.total_subscriptions ?? 0,
            average_rating: data.average_rating ?? 0,

            // Use actual data if available, otherwise use defaults
            theme_color: data.theme_color ?? "#D4AF37",
            delivery_info: data.delivery_info ?? "",
            business_hours: data.business_hours,
            google_maps_url: data.google_maps_url ?? "",
            established_year: data.established_year ?? null,

            // Add default values for fields required by BusinessCardData but not in our query
            website_url: "",
            linkedin_url: "",
            twitter_url: "",
            youtube_url: "",
            call_number: "", // This field doesn't exist in the database
          };
        }) ?? [];

      return {
        data: {
          location: { city: locationCity, state: locationState },
          businesses: businesses,
          isAuthenticated: isAuthenticated,
          totalCount,
          hasMore,
          nextPage,
        },
      };
    } else {
      // viewType === 'products'
      let validBusinessIds: string[] = [];

      // Check if we're searching by city directly
      if (city && validPincodes.length === 0) {
        // Direct city-based search using Supabase
        const nowISO = new Date().toISOString();

        // Get business IDs matching the city
        const { data, error } = await supabaseAdmin
          .from("business_profiles")
          .select("id")
          .eq("city", city) // Use exact matching for city
          .eq("status", "online")
          .or(`has_active_subscription.eq.true,trial_end_date.gt.${nowISO}`);

        if (error) {
          console.error("City Business IDs Error:", error);
          return { error: "Database error fetching business IDs by city." };
        }

        validBusinessIds = data.map(item => item.id);
      } else {
        // First, get IDs of valid businesses using direct query
        let businessQuery = supabaseAdmin
          .from("business_profiles")
          .select("id")
          .eq("status", "online");

        // Add pincode filter if available
        if (validPincodes.length > 0) {
          businessQuery = businessQuery.in("pincode", validPincodes);
        }

        // Add locality filter if provided
        if (locality) {
          businessQuery = businessQuery.eq("locality", locality);
        }

        // Apply sorting
        businessQuery = businessQuery.order(getSortingColumn(sortBy), {
          ascending: getSortingDirection(sortBy)
        });

        const { data: ids, error: validBusinessesError } = await businessQuery;

        if (validBusinessesError) {
          console.error(
            "Search Discover (Product IDs) Error:",
            validBusinessesError
          );
          return { error: validBusinessesError.message || "Database error fetching business IDs" };
        }

        validBusinessIds = ids?.map(item => item.id) || [];
      }

      // This check is now handled inside the else block above

      if (!validBusinessIds || validBusinessIds.length === 0) {
        // No valid businesses found, return empty results
        return {
          data: {
            location: { city: locationCity, state: locationState },
            products: [],
            isAuthenticated: isAuthenticated,
            totalCount: 0,
            hasMore: false,
            nextPage: null,
          },
        };
      }

      // Get total count of products first
      let countQuery = supabaseAdmin
        .from("products_services")
        .select("id", { count: "exact" })
        .in("business_id", validBusinessIds || [])
        .eq("is_available", true);

      // Add product type filter if provided
      if (productType) {
        countQuery = countQuery.eq("product_type", productType);
      }

      const { count: totalProductCount, error: productCountError } = await countQuery;

      if (productCountError) {
        console.error(
          "Search Discover (Product Count) Error:",
          productCountError
        );
        return { error: "Database error counting products." };
      }

      // Calculate pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      const totalCount = totalProductCount || 0;
      const hasMore = totalCount > page * limit;
      const nextPage = hasMore ? page + 1 : null;

      // Fetch Products belonging to valid businesses with pagination

      // Build the query for products
      let productsQuery = supabaseAdmin
        .from("products_services")
        .select(
          // Select required fields + business_slug
          `
          id, business_id, name, description, base_price, discounted_price, product_type,
          is_available, image_url, created_at, updated_at, slug,
          business_profiles!business_id(business_slug)
        `
        )
        .in("business_id", validBusinessIds || []) // Filter products by valid business IDs
        .eq("is_available", true);

      // Add product type filter if provided
      if (productType) {
        productsQuery = productsQuery.eq("product_type", productType);
      }

      // Add pagination
      productsQuery = productsQuery.range(from, to);

      // Apply sorting based on the sortBy parameter
      const sortColumn = getSortingColumn(sortBy, true); // true indicates product view
      const sortAscending = getSortingDirection(sortBy);

      // Special handling for price sorting to use discounted_price when available, otherwise base_price
      if (sortColumn === "price") {
        if (sortAscending) {
          productsQuery = productsQuery.order('discounted_price', { ascending: true, nullsFirst: false })
                                       .order('base_price', { ascending: true, nullsFirst: false });
        } else {
          productsQuery = productsQuery.order('discounted_price', { ascending: false, nullsFirst: false })
                                       .order('base_price', { ascending: false, nullsFirst: false });
        }
      } else {
        productsQuery = productsQuery.order(sortColumn, {
          ascending: sortAscending,
        });
      }

      const { data: productsData, error: productsError } = await productsQuery;

      if (productsError) {
        console.error("Search Discover (Products) Error:", productsError);
        return { error: "Database error fetching nearby products." };
      }

      // Define a type representing the actual data structure returned by the query
      type ProductQueryResult = {
        id: string;
        business_id: string | null;
        name: string | null;
        description: string | null;
        base_price: number | null;
        discounted_price: number | null;
        product_type: "physical" | "service" | null;
        is_available: boolean | null;
        image_url: string | null;
        created_at: string | null;
        updated_at: string | null;
        slug: string | null;
        business_profiles:
          | { business_slug: string | null }[]
          | { business_slug: string | null }
          | null;
      };

      // Process products to match the full ProductServiceData structure + business_slug
      const products =
        productsData?.map((p: ProductQueryResult) => {
          // Extract business_slug from the joined business_profiles
          let business_slug = null;

          if (p.business_profiles) {
            // Check if it's an array or an object
            if (
              Array.isArray(p.business_profiles) &&
              p.business_profiles.length > 0
            ) {
              business_slug = p.business_profiles[0].business_slug;
            } else if (
              typeof p.business_profiles === "object" &&
              p.business_profiles !== null
            ) {
              // Cast to a more specific type to handle different response formats
              business_slug = (
                p.business_profiles as { business_slug: string | null }
              ).business_slug;
            }
          }

          // Ensure we have a valid business_slug

          const product = {
            id: p.id,
            business_id: p.business_id ?? undefined,
            name: p.name ?? "",
            description: p.description ?? "",
            base_price: p.base_price ?? 0,
            discounted_price: p.discounted_price ?? null,
            product_type: p.product_type ?? "physical",
            is_available: p.is_available ?? true,
            image_url: p.image_url,
            created_at: p.created_at ? new Date(p.created_at) : undefined,
            updated_at: p.updated_at ? new Date(p.updated_at) : undefined,
            business_slug: business_slug,
            featured_image_index: 0, // Default value for NearbyProduct
            images: [], // Default empty array for images
            slug: p.slug || undefined, // Use the fetched slug or undefined if not available
            // Add default/empty values for fields not fetched but required by ProductServiceData
          };
          return product;
        }) ?? [];

      return {
        data: {
          location: { city: locationCity, state: locationState },
          products: products,
          isAuthenticated: isAuthenticated,
          totalCount,
          hasMore,
          nextPage,
        },
      };
    }
  } catch (e) {
    console.error("Search Discover Exception:", e);
    return { error: "An unexpected error occurred during the search." };
  }
}
