'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import CustomerPostForm from '@/components/feed/shared/forms/CustomerPostForm';
import { useCustomerProfile } from '@/contexts/UserDataContext';

interface ModernCustomerPostCreatorProps {
  customerName?: string;
  onPostCreated?: () => void;
}

export default function ModernCustomerPostCreator({
  customerName = 'Customer',
  onPostCreated
}: ModernCustomerPostCreatorProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const { customerProfile, getCustomerProfile } = useCustomerProfile();

  // Use profile data from context or fallback to prop
  const customerDisplayName = customerProfile?.name || customerName || 'Customer';
  const customerAvatar = customerProfile?.avatar_url;

  // Fetch customer profile data if not already available
  useEffect(() => {
    if (!customerProfile) {
      getCustomerProfile();
    }
  }, [customerProfile, getCustomerProfile]);

  const handlePostSuccess = () => {
    setIsExpanded(false);
    if (onPostCreated) {
      onPostCreated();
    }
  };

  const handleCancel = () => {
    setIsExpanded(false);
  };

  return (
    <Card className="bg-white dark:bg-black border-neutral-200 dark:border-neutral-800 shadow-sm overflow-hidden">
      <CardContent className="p-0">
        <AnimatePresence mode="wait">
          {!isExpanded ? (
            <motion.div
              key="collapsed"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="px-4 py-1 md:px-4 md:py-1.5"
            >
              {/* Simplified Layout: Avatar + Text + Plus Button (matching React Native) */}
              <div className="flex items-center gap-3 cursor-pointer" onClick={() => setIsExpanded(true)}>
                <Avatar className="w-12 h-12 border-2 border-[var(--brand-gold)]/30 shadow-sm">
                  <AvatarImage src={customerAvatar || undefined} alt={customerDisplayName} />
                  <AvatarFallback className="bg-muted text-foreground border border-[var(--brand-gold)]/30">
                    {customerDisplayName.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1">
                  <p className="text-neutral-500 dark:text-neutral-400 font-normal text-base">
                    What&apos;s on your mind, {customerDisplayName}?
                  </p>
                </div>

                <Button
                  size="sm"
                  className="h-8 w-8 p-0 bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-dark)] text-white rounded-full"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsExpanded(true);
                  }}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>


            </motion.div>
          ) : (
            <motion.div
              key="expanded"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="overflow-hidden"
            >
              {/* Header - Simplified like React Native */}
              <div className="flex items-center justify-between p-4 md:p-6 pb-4 border-b border-neutral-200 dark:border-neutral-700">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCancel}
                  className="h-8 w-8 p-0 hover:bg-neutral-100 dark:hover:bg-neutral-800"
                >
                  <X className="h-4 w-4" />
                </Button>

                <h3 className="font-semibold text-lg text-neutral-900 dark:text-neutral-100">
                  Create Post
                </h3>

                <div className="w-8 h-8" /> {/* Spacer for centering */}
              </div>

              {/* Content */}
              <div className="p-4 md:p-6 pt-4">
                {/* User Info - matching React Native */}
                <div className="flex items-center gap-3 mb-4">
                  <Avatar className="w-12 h-12 border-2 border-[var(--brand-gold)]/30 shadow-sm">
                    <AvatarImage src={customerAvatar || undefined} alt={customerDisplayName} />
                    <AvatarFallback className="bg-muted text-foreground border border-[var(--brand-gold)]/30">
                      {customerDisplayName.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-semibold text-base text-neutral-900 dark:text-neutral-100">
                      {customerDisplayName}
                    </h4>
                  </div>
                </div>

                <CustomerPostForm
                  onSuccess={handlePostSuccess}
                  onCancel={handleCancel}
                  showCard={false}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  );
}
